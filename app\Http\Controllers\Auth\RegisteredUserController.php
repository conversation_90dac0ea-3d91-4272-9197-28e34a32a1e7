<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Env;
use Illuminate\Validation\Rules;
use Illuminate\Validation\Rule;
use Inertia\Inertia;
use Inertia\Response;

class RegisteredUserController extends Controller
{
    /**
     * Show the registration page.
     */
    public function create(): Response
    {
        // Check if registration is allowed
        if (!config('auth.allow_public_registration') && !(Auth::check() && Auth::user()->role === 'Admin')) {
            abort(403, 'Registration is disabled.');
        }
        // Get the authenticated user, if any
        $user = Auth::user();

        return Inertia::render('auth/register', [
            'canAssignRole' => $user && $user->role === 'Admin', // Pass flag if user is Admin
            'availableRoles' => ['Admin', 'Editor', 'Viewer'] // Pass available roles
        ]);
    }

    /**
     * Handle an incoming registration request.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request): RedirectResponse
    {
        // Get the authenticated user, if any
        $authUser = Auth::user();
        $isAuthAdmin = $authUser && $authUser->role === 'Admin';

        $validationRules = [
            'name' => 'required|string|max:255',
            'username' => 'required|string|max:255|unique:users,username',
            'email' => 'required|string|lowercase|email|max:255|unique:'.User::class,
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
        ];

        // Add role validation only if the authenticated user is an Admin
        if ($isAuthAdmin) {
            $validationRules['role'] = ['required', Rule::in(['Admin', 'Editor', 'Viewer'])];
        }

        $validatedData = $request->validate($validationRules);

        // Determine the role to assign
        $roleToAssign = 'Viewer'; // Default role
        if ($isAuthAdmin) {
            // If an Admin is creating the user, use the validated role from the request
            $roleToAssign = $validatedData['role'];
        }

        $user = User::create([
            'name' => $request->name,
            'username' => $request->username,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role' => $request->role ?? 'Viewer',
        ]);

        event(new Registered($user));

        // Log in the newly created user only if they registered themselves (not created by an admin)
        // If an admin creates a user, the admin remains logged in.
        if (!Auth::check()) {
            Auth::login($user);
        }

        // Redirect based on who created the user
        if ($isAuthAdmin) {
            // Option 1: Redirect admin back to a user list or dashboard
             return redirect()->route('dashboard')->with('success', 'User created successfully.'); // Adjust route as needed
            // Option 2: Redirect to the new user's profile (if applicable)
            // return redirect()->route('users.show', $user->id);
        } else {
            // Redirect self-registered user to their dashboard
            return redirect()->route('dashboard');
        }
    }
}
