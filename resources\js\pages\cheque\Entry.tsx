import React, { useState, useEffect } from 'react'; // Added useEffect
import { Head, useForm } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { ComboBox } from '@/components/ui/combobox';
import { DatePicker } from '@/components/ui/date-picker';
import { Input } from '@/components/ui/input';
import AppLayout from '@/layouts/app-layout';
import { NumericFormat } from 'react-number-format';
import { toast } from 'sonner';
import { BreadcrumbItem } from '@/types';
import axios from 'axios'; // Import axios

const breadcrumbs: BreadcrumbItem[] = [
  { title: 'Dashboard', href: '/dashboard' },
  { title: 'Cheque Entry', href: '/cheque/entry' },
];

interface Bank {
  id: number;
  name: string;
}

interface Account {
  id: number;
  name: string;
}


export default function Entry() {
  const { data, setData, processing, errors, reset } = useForm({
    entry_type: 'inward',
    entry_date: '',
    cheque_date: '',
    posting_date: '',
    from_account_id: '',
    to_account_id: '',
    amount: '',
    chq_ref_bank_id: '',
    chq_no: '',
  });

  const [showPostingDate, setShowPostingDate] = useState(true);
  const [entryDate, setEntryDate] = useState<Date | undefined>(undefined);
  const [chequeDate, setChequeDate] = useState<Date | undefined>(undefined);
  const [postingDate, setPostingDate] = useState<Date | undefined>(undefined);

  // States for fetched data
  const [banks, setBanks] = useState<Bank[]>([]);
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        const banksResponse = await axios.get('/api/chq-ref-banks');
        setBanks(banksResponse.data);

        const accountsResponse = await axios.get('/api/accounts');
        // Filter accounts based on specified types (excluding)
        const excludedTypes = ['currency_account', 'income', 'property'];
        const filteredAccounts = accountsResponse.data.filter((account: { account_type?: { name: string } }) =>
            account.account_type && !excludedTypes.includes(account.account_type.name)
        );
        setAccounts(filteredAccounts);
      } catch (error) {
        console.error('Failed to fetch initial data:', error);
        toast.error('Failed to load required data. Please try refreshing the page.');
      }
      setLoading(false);
    };
    fetchInitialData();
  }, []);

  function handleEntryTypeChange(value: string) {
    setData('entry_type', value);
    setShowPostingDate(value === 'inward');
  }

  // Helper to format date as YYYY-MM-DD in local time
  const formatDateLocal = (d: Date) => {
    const year = d.getFullYear();
    const month = (d.getMonth() + 1).toString().padStart(2, '0');
    const day = d.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  function handleEntryDateSelect(date: Date | undefined) {
    setEntryDate(date);
    if (date) {
      setData('entry_date', formatDateLocal(date));
    }
  }

  function handleChequeDateSelect(date: Date | undefined) {
    setChequeDate(date);
    if (date) {
      setData('cheque_date', formatDateLocal(date));
    }
  }

  function handlePostingDateSelect(date: Date | undefined) {
    setPostingDate(date);
    if (date) {
      setData('posting_date', formatDateLocal(date));
    }
  }

  function handleSubmit(e: React.FormEvent) {
    e.preventDefault();

    // Client-side validation
    if (!data.entry_type) {
      toast.error('Entry type is required');
      return;
    }
    if (!data.entry_date) {
      toast.error('Entry date is required');
      return;
    }
    if (!data.cheque_date) {
      toast.error('Cheque date is required');
      return;
    }
    if (showPostingDate && !data.posting_date) {
      toast.error('Posting date is required for Inward entry');
      return;
    }
    if (!data.from_account_id) {
      toast.error('From Account is required');
      return;
    }
    if (!data.to_account_id) {
      toast.error('To Account is required');
      return;
    }
    if (!data.amount || isNaN(Number(data.amount)) || Number(data.amount) <= 0) {
      toast.error('Amount must be a positive number');
      return;
    }
    if (!data.chq_ref_bank_id) {
      toast.error('CHQ Ref Bank is required');
      return;
    }
    if (!data.chq_no) {
      toast.error('CHQ No is required');
      return;
    }

    // Use axios.post instead of Inertia's post
    // Add a new state for processing to disable button during submission
    // const [isSubmitting, setIsSubmitting] = useState(false);
    // setIsSubmitting(true); // Set before API call

    axios.post('/api/cheque-entries', data) // Send form data directly
      .then(response => {
        // Assuming server returns { message: '...', data: ... } on success
        toast.success(response.data.message || 'Cheque entry created successfully');
        reset();
        setEntryDate(undefined);
        setChequeDate(undefined);
        setPostingDate(undefined);
      })
      .catch(error => {
        if (error.response && error.response.data && error.response.data.message) {
          toast.error(error.response.data.message);
          // Handle validation errors if server sends them in a specific format
          if (error.response.data.errors) {
            Object.values(error.response.data.errors).forEach((errMsg: any) => {
              toast.error(Array.isArray(errMsg) ? errMsg.join(' ') : errMsg);
            });
          }
        } else {
          toast.error('Failed to create cheque entry. Please try again.');
        }
        console.error('Submission error:', error);
      })
      .finally(() => {
        // setIsSubmitting(false); // Reset after API call completes
      });
  }

  function handleReset() {
    reset();
    setEntryDate(undefined);
    setChequeDate(undefined);
    setPostingDate(undefined);
  }

  // if (loading) {
  //   return (
  //     <AppLayout breadcrumbs={breadcrumbs}>
  //       <Head>
  //         <title>New Cheque Entry</title>
  //       </Head>
  //       <div className="container mx-auto py-6">
  //         <p>Loading...</p>
  //       </div>
  //     </AppLayout>
  //   );
  // }

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="New Cheque Entry" />

      <div className="container mx-auto p-6">
        <h1 className="mb-4 text-2xl font-bold">Cheque Entry</h1>

        
        <form onSubmit={handleSubmit} className="max-w-2xl space-y-4">
          <div className="space-y-4">
            <div className="grid grid-cols-12 items-center gap-4">
              <label className="col-span-3 text-right">Entry Type</label>
              <div className="col-span-6">
                <ComboBox
                  options={[
                    { value: 'inward', label: 'Inward' },
                    { value: 'outward', label: 'Outward' },
                  ]}
                  value={data.entry_type}
                  onChange={handleEntryTypeChange}
                />
                {errors.entry_type && <p className="text-red-600 text-sm">{errors.entry_type}</p>}
              </div>
            </div>

            <div className="grid grid-cols-12 items-center gap-4">
              <label className="col-span-3 text-right">Entry Date</label>
              <div className="col-span-6 lg:col-span-3">
                <DatePicker selected={entryDate} onSelect={handleEntryDateSelect} />
                {errors.entry_date && <p className="text-red-600 text-sm">{errors.entry_date}</p>}
              </div>
            </div>

            <div className="grid grid-cols-12 items-center gap-4">
              <label className="col-span-3 text-right">Cheque Date</label>
              <div className="col-span-6 lg:col-span-3">
                <DatePicker selected={chequeDate} onSelect={handleChequeDateSelect} />
                {errors.cheque_date && <p className="text-red-600 text-sm">{errors.cheque_date}</p>}
              </div>
            </div>

            {showPostingDate && (
              <div className="grid grid-cols-12 items-center gap-4">
                <label className="col-span-3 text-right">Posting Date</label>
                <div className="col-span-6 lg:col-span-3">
                  <DatePicker selected={postingDate} onSelect={handlePostingDateSelect} />
                  {errors.posting_date && <p className="text-red-600 text-sm">{errors.posting_date}</p>}
                </div>
              </div>
            )}

            <div className="grid grid-cols-12 items-center gap-4">
              <label className="col-span-3 text-right">From Account (Credit)</label>
              <div className="col-span-6">
                <ComboBox
                  options={accounts.map((acc: Account) => ({ value: acc.id.toString(), label: acc.name }))}
                  value={data.from_account_id}
                  onChange={(value: string) => setData('from_account_id', value)}
                  placeholder="Select from account"
                />

                {errors.from_account_id && <p className="text-red-600 text-sm">{errors.from_account_id}</p>}
              </div>
            </div>

            <div className="grid grid-cols-12 items-center gap-4">
              <label className="col-span-3 text-right">To Account (Debit)</label>
              <div className="col-span-6">
                <ComboBox
                  options={accounts.map((acc: Account) => ({ value: acc.id.toString(), label: acc.name }))}
                  value={data.to_account_id}
                  onChange={(value: string) => setData('to_account_id', value)}
                  placeholder="Select to account"
                />

                {errors.to_account_id && <p className="text-red-600 text-sm">{errors.to_account_id}</p>}
              </div>
            </div>

            <div className="grid grid-cols-12 items-center gap-4">
              <label className="col-span-3 text-right">Amount</label>
              <div className="col-span-6">
                <NumericFormat
                  value={data.amount}
                  onValueChange={(values) => setData('amount', values.value)}
                  thousandSeparator={true}
                  allowNegative={false}
                  customInput={Input}
                  placeholder="Enter amount"
                />
                {errors.amount && <p className="text-red-600 text-sm">{errors.amount}</p>}
              </div>
            </div>

            <div className="grid grid-cols-12 items-center gap-4">
              <label className="col-span-3 text-right">CHQ Ref Bank</label>
              <div className="col-span-6">
                <ComboBox
                  options={banks.map((bank: Bank) => ({ value: bank.id.toString(), label: bank.name }))}
                  value={data.chq_ref_bank_id}
                  onChange={(value: string) => setData('chq_ref_bank_id', value)}
                  placeholder="Select bank"
                />

                {errors.chq_ref_bank_id && <p className="text-red-600 text-sm">{errors.chq_ref_bank_id}</p>}
              </div>
            </div>

            <div className="grid grid-cols-12 items-center gap-4">
              <label className="col-span-3 text-right">CHQ No</label>
              <div className="col-span-6">
                <Input
                  value={data.chq_no}
                  onChange={e => setData('chq_no', e.target.value)}
                  placeholder="Enter CHQ NO"
                />
                {errors.chq_no && <p className="text-red-600 text-sm">{errors.chq_no}</p>}
              </div>
            </div>

            <div className="grid grid-cols-12 gap-4">
              <div className="col-span-3" />
              <div className="col-span-9 flex gap-4">
                <Button type="submit" disabled={processing}>
                  {processing ? 'Submitting...' : 'Submit'}
                </Button>
                <Button type="button" variant="outline" onClick={handleReset}>
                  Reset
                </Button>
              </div>
            </div>
          </div>
        </form>
        
      </div>
    </AppLayout>
  );
}
