# Memory.md

## Mistakes & Lessons Learned

### Laravel Validation for Uniqueness
- Observation: When creating or updating resources (like Accounts), <PERSON><PERSON>'s built-in `unique` validation rule (e.g., `unique:accounts,name`) automatically handles duplicate entry checks.
- Lesson: Rely on <PERSON><PERSON>'s default validation for common constraints like uniqueness. It provides standard 422 JSON error responses that frontends (like Inertia/React) can easily parse and display, reducing the need for custom error handling in the controller for these cases.

### Linter Error: Inertia.js post Usage
- Mistake: Used three arguments in the `post` function call in the registration form, which is not supported by Inertia.js `useForm` API. Only two arguments are allowed: (url: string, options?: object).
- Lesson: Always check the latest official documentation or use context7 for API changes and best practices.
- Fix: Refactor all `post` usages to match the correct signature.

### File Update Process
- Mistake: Attempted to update `AccountController.php` using `update_file` without first verifying the latest content after a previous view. The update failed because the `old_str` didn't match the actual file content.
- Lesson: Always use `view_files` to get the most recent version of a file before attempting modifications with `update_file` or similar tools, especially if there's a chance the file has changed or a previous edit attempt failed.

### Project Process
- Always update or create `Todo.md`, `projectDetails.md`, and `Memory.md` as part of any significant feature or bugfix task.
- Use `.env` variables to control feature toggles like registration availability.
- Document all new or changed environment variables in `.env.example`.

## Memory: Fixing Common Errors with shadcn/ui Components

- If you get `Cannot find module '@/components/ui/combobox' or its corresponding type declarations`, ensure the file exists at `resources/js/components/ui/combobox.tsx` and that the import path matches exactly. No type declaration file is needed if you use a `.tsx` with proper exports.
- For Inertia useForm errors, always ensure all initial values are the correct type (e.g., string) and never `unknown`.
- If you see a module not found error but the file exists, try restarting your dev server or IDE to refresh the TypeScript cache.

## Memory: Official shadcn/ui ComboBox Implementation
- ComboBox is a composition of Popover and Command.
- Always follow the official API and usage pattern from https://ui.shadcn.com/docs/components/combobox.
- Export `ComboBox` and `ComboBoxOption` for type safety and reusability.

## Memory: Integration Checklist
- After installing shadcn/ui components, always check imports and usage in all forms and pages.
- Update or refactor legacy code to use the new ComboBox, Command, and Popover as per official docs.

## Memory: Notifications
- Always use Sonner (not deprecated Toast) from shadcn/ui for notifications.
- When adding toast notifications, import 'toast' from 'sonner' and use toast.success and toast.error for feedback.
- Update all relevant documentation and Todo/project details after implementing major UI feedback features.

## Memory: Modern Inertia Table UX
- Show first backend error in toast for clear feedback.
- Highlight invalid fields with red border for better UX.
- Add spinners and disable buttons during processing for clear loading state.

This pattern ensures a responsive and user-friendly admin interface.

## Memory: SaaS-Style Section Animations
- Remember: When adding animation to any new section, use a unique animation class and keyframe with a slight delay for a staggered effect, matching the SaaS style.
- Avoid duplicating animation delays for sibling sections to keep the entrance flow smooth.
- All major homepage sections (Header, Hero, Features, CTA, Footer) now use this pattern.

## Memory: API & Table Best Practices
- Always separate API (JSON) and Inertia (page) routes. Use api.php for API, web.php for Inertia.
- Secure API endpoints with sanctum middleware.
- For @tanstack/react-table v8+, use 'id' for column key and 'accessorKey' for data columns. Avoid using non-existent properties.
- Update all usages in forms and table rendering to match the correct column shape.

## Memory: API Routes
- Always use api.php for API routes and web.php for Inertia routes.
- Secure API endpoints with sanctum middleware.
- Laravel 12 + API routes require explicit inclusion in bootstrap/app.php within `withRouting()` call for API routes to be loaded.
- Always add 'sanctum' guard to auth.php for API authentication.

## Memory: Laravel12 New Structure
- Laravel 12 with the new application structure (including the React starter kit) no longer uses app/Http/Kernel.php for middleware registration. Instead, middleware is now registered in bootstrap/app.php using the new API.

## Memory: Laravel Sanctum Stateful API
- Laravel Sanctum stateful API requires explicit stateful middleware in bootstrap/app.php.
- Use `statefulApi()` method in `withMiddleware()` to register stateful API middleware.
- Protect API routes in routes/api.php with auth:sanctum.
- Set SESSION_DOMAIN and SANCTUM_STATEFUL_DOMAINS in .env for local SPA development.
- On the frontend, always call /sanctum/csrf-cookie before login or state-changing requests, and use withCredentials: true.
- Do not manually append EnsureFrontendRequestsAreStateful in Laravel 12+.
- For local SPA development with Laravel 12 + Sanctum, always use SESSION_DOMAIN=null in .env to allow cookies for any local domain (localhost or 127.0.0.1).

## [2025-04-24] Memory: Modal Close on Success (User Creation)
- Issue: Previously, after successful user creation by admin, the modal did not close automatically, causing confusion about completion.
- Solution: Added `onSuccess` prop to `CreateUserForm` and handled modal close in `settings/users.tsx`.
- Lesson: For modal forms, always provide a way for the form to notify the parent of success, so UX is clear and smooth.
- Action: Always check for and implement parent-child success callbacks for modal forms in React projects.

## [2025-04-24] Memory: User Deletion Implementation
- Lesson: Destructive actions require backend routes, authorization, and frontend feedback.
- Pattern: Prevent self-deletion for admin users to avoid accidental lockout.
- Implementation: Always add a backend route and controller method for destructive actions like delete, with proper authorization checks, and integrate frontend delete logic with backend and show user feedback (toasts) for all outcomes.

## [2025-04-24] Memory: Inertia Import and Route Usage Error
- Problem: Importing a non-existent '@/lib/inertia' caused build errors. Also, using a missing or custom route helper and missing Inertia import led to runtime errors.
- Solution: Always import Inertia directly from '@inertiajs/inertia' and use explicit route strings if no global route() helper exists. Type parameters explicitly for TypeScript.
- Lesson: Always verify imports and helper existence, and prefer framework-official imports and usage for reliability and portability.

## [2025-04-24] Memory: Preventing Self-Deletion in User Management Tables
- Lesson: Always prevent self-deletion in admin/user management tables. Show a clear toast notification so users understand why the action is blocked.
- Always use ShadCN DataTables wherever we will use tables.

## [2025-04-25] Sidebar Header Pattern: Breadcrumb + Actions
- When adding global actions (like a theme switcher) to the sidebar layout header, always update the shared header component (e.g., AppSidebarHeader) instead of injecting extra divs in each page.
- Use a flex container with justify-between so breadcrumbs are left-aligned and actions are right-aligned.
- This avoids layout duplication, keeps the UI consistent, and ensures all pages using the sidebar layout benefit from the enhancement.
- Do not add extra header divs in individual pages like dashboard or settings—always centralize these changes in the shared layout/header.

## [2025-04-25] Memory: Conditional Rendering of CTAs
- Remember: When implementing landing page CTAs, always conditionally render registration/login vs. dashboard buttons based on authentication state, in both hero and CTA sections.
- If Memory.md already exists, always append new learnings.

## Memory: File Management
- Always check if Memory.md exists before trying to create it. Use edit_file if it exists.
- For frontend CRUD, always remove dummy data and use backend endpoints.
- After backend integration, update UI to use loading states and error handling.

## Memory: Table Skeleton Loading UI Pattern (April 27, 2025)
- Table headings always appear instantly and reflect the selected account type.
- Skeleton loaders show only in the table body cells while data is loading, not over the entire table.
- Skeleton cell style uses className="px-4 py-3" for table cells and a Skeleton with h-5 w-full rounded.
- This design is now standard for all data tables in the project.

## [2025-04-29] Memory: Conditional Validation for Formula Field
- Bug: Unconditional required validation for formula field caused errors for non-Currency accounts.
- Root Cause: Missing conditional validation based on account_type_id.
- Lesson: Always make validation rules conditional based on account_type_id.

## Learnings

- **TypeScript Best Practices:** Avoid using the `any` type. Define specific interfaces or types whenever possible to improve type safety, code clarity, and prevent potential runtime errors. This was highlighted by multiple `@typescript-eslint/no-explicit-any` errors in `resources/js/pages/accounts/index.tsx`.

## [2025-05-17] Memory: API Documentation Standards

### Documentation Structure
1. **File Location**: `resources/js/pages/ApiDocumentation.tsx`
2. **Component Structure**:
   - Each API section is an object in the `apiSections` array
   - Each section has an `id`, `title`, and `content`
   - Content is written in JSX with Tailwind CSS classes

### Required Sections for Each Endpoint
1. **Overview**: Brief description of the endpoint's purpose
2. **Endpoint Details**:
   - HTTP Method and Path
   - Authentication requirements
   - Request format (if applicable)
   - Response format with examples
   - Possible error responses

### Code Examples
- Use code blocks with syntax highlighting
- Include both request and response examples
- Show all possible response status codes

### Authentication Requirements
- Clearly state if authentication is required
- Specify required roles (Admin, Editor, Viewer)
- Include example of authentication headers if needed

### Error Handling
- Document all possible error responses
- Include status codes and example error messages
- Explain how to handle validation errors

### Versioning
- Include API version in the URL (e.g., `/api/v1/...`)
- Document any breaking changes between versions

### Rate Limiting
- Document any rate limits
- Include headers for rate limit information
- Explain how to handle rate limit exceeded errors

### Best Practices
- Keep examples up-to-date with the actual API
- Use consistent formatting
- Include type information for request/response bodies
- Document any deprecated endpoints
- Include links to related resources

## [2025-05-17] Memory: Laravel 12 Routing Approaches

### 1. Resourceful Controllers vs Individual Route Definitions

**Resourceful Controllers (Recommended)**
```php
// routes/api.php
Route::apiResource('accounts', AccountController::class);
```
*Pros:*
- Follows RESTful conventions
- Single line creates all CRUD routes
- Consistent route naming
- Better maintainability
*Cons:*
- Less flexible for custom actions
- May require additional routes for non-CRUD operations

**Individual Routes**
```php
// routes/api.php
Route::get('/accounts', [AccountController::class, 'index']);
Route::post('/accounts', [AccountController::class, 'store']);
// ... more routes
```
*Pros:*
- Complete control over each endpoint
- Can customize URL structure
- Good for non-RESTful APIs
*Cons:*
- More verbose
- Higher chance of inconsistency
- Harder to maintain

### 2. Route Groups for Better Organization

**Middleware Groups**
```php
Route::middleware('auth:sanctum')->group(function () {
    Route::apiResource('accounts', AccountController::class);
    // Other protected routes
});
```

**Route Prefixing**
```php
Route::prefix('v1')->group(function () {
    Route::apiResource('accounts', AccountController::class);
    // Version 1 routes
});
```

### 3. Route Model Binding

**Implicit Binding**
```php
// In controller
public function show(Account $account) {
    return new AccountResource($account);
}
```

**Explicit Binding**
```php
// In RouteServiceProvider.php
public function boot() {
    Route::model('account', Account::class);
}
```

### 4. API Resource Routes

**Recommended Structure**
```php
// routes/api.php
Route::middleware('auth:sanctum')->group(function () {
    Route::apiResource('accounts', AccountController::class);
    Route::apiResource('currencies', CurrencyController::class);
    // Nested resources
    Route::apiResource('accounts.transactions', TransactionController::class);
});
```

### 5. Route Caching for Production

```bash
# Cache routes for production
php artisan route:cache

# Clear route cache
php artisan route:clear
```

### 6. Rate Limiting

```php
// In RouteServiceProvider.php
protected function configureRateLimiting() {
    RateLimiter::for('api', function (Request $request) {
        return Limit::perMinute(60)->by($request->user()?->id ?: $request->ip());
    });
}
```

### Recommendations for This Project

1. **Consolidate Routes**
   - Move all API routes to `routes/api.php`
   - Use `apiResource` for standard CRUD operations
   - Group related routes with middleware

2. **Versioning**
   - Consider adding API versioning (e.g., `/api/v1/accounts`)
   - Helps with backward compatibility

3. **Documentation**
   - Document all API endpoints
   - Include request/response examples
   - List required permissions

4. **Testing**
   - Write feature tests for all routes
   - Test validation rules
   - Test authentication/authorization

5. **Performance**
   - Implement route caching in production
   - Use rate limiting to prevent abuse
   - Consider API response caching for read-heavy endpoints

---

## [2025-05-20] Memory: Filtering and Caching Payment Types
- Implemented filtering for payment types in the `PaymentTypeController`'s `index` method using an optional `filter_names` request parameter.
- Added caching for filtered payment type results to improve performance for frequently requested subsets.
- Updated frontend components (`single.tsx`, `multiple.tsx`, `EditJournalEntry.tsx`) to request specific payment types using the new filter parameter.
- Lesson: Filtering API responses on the backend based on frontend needs and caching these filtered results can optimize performance and reduce unnecessary data transfer.

This file will be updated with future lessons and recurring mistakes for continuous improvement.
