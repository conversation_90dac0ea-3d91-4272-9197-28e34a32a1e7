import { Head, usePage, useForm } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import SettingsLayout from '@/layouts/settings/layout';
import HeadingSmall from '@/components/heading-small';
import { type BreadcrumbItem } from '@/types';
import CreateUserForm from '../../components/create-user-form';
import UserEditForm from '../../components/user-edit-form';
import { Dialog, DialogContent, DialogTrigger, DialogTitle, DialogDescription, DialogClose } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { useState, useEffect } from 'react';
import { Inertia } from '@inertiajs/inertia';
import { toast } from 'sonner';
import { DataTable } from '@/components/ui/data-table';
import { userColumns } from './users-columns';
import type { User } from './users-types';
import { Pencil, Trash2 } from "lucide-react";

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'User management',
        href: '/settings/users',
    },
];

// Assuming roles are static or passed via props if dynamic
const roles = ['Admin', 'Editor', 'Viewer'];

export default function Users() {
    const { auth, users: initialUsers } = usePage().props as Partial<{
        auth: { user: { role: string, id: number } },
        users: User[]
    }>;

    const [users, setUsers] = useState<User[]>(initialUsers || []);
    const [openCreateModal, setOpenCreateModal] = useState(false);
    const [editingUser, setEditingUser] = useState<User | null>(null);
    const [deletingUserId, setDeletingUserId] = useState<number | null>(null);

    // Form hook for creating users
    const createUserForm = useForm({
        name: '',
        email: '',
        username: '',
        password: '',
        password_confirmation: '',
        role: roles[2], // Default role: Viewer
    });

    // Form hook for editing users
    const editUserForm = useForm({
        name: '',
        username: '',
        email: '',
        password: '',
        password_confirmation: '',
        role: '',
    });

    useEffect(() => {
        setUsers(initialUsers || []);
    }, [initialUsers]);

    // Function to open edit modal and populate form
    const handleEditClick = (userToEdit: User) => {
        setEditingUser(userToEdit);
        editUserForm.setData({
            name: userToEdit.name,
            username: userToEdit.username,
            email: userToEdit.email,
            role: userToEdit.role,
            password: '',
            password_confirmation: '',
        });
        editUserForm.clearErrors();
    };

    const handleDelete = (id: number) => {
        if (auth?.user?.id === id) {
            toast.error('You cannot delete yourself.');
            return;
        }

        const originalUsers = [...users];

        setUsers(prevUsers => prevUsers.filter(user => user.id !== id));
        setDeletingUserId(id);

        Inertia.delete(`/settings/users/${id}`, {
            preserveScroll: true,
            onSuccess: () => {
                toast.success('User deleted successfully!');
            },
            onError: (errs) => {
                setUsers(originalUsers);
                const firstError = errs && typeof errs === 'object' ? Object.values(errs)[0] : null;
                if (firstError) {
                    toast.error(Array.isArray(firstError) ? firstError[0] : firstError);
                } else {
                    toast.error('Failed to delete user.');
                }
            },
            onFinish: () => setDeletingUserId(null),
        });
    };

    // Submit handler for creating users - NOW OPTIMISTIC
    const handleCreateSubmit: React.FormEventHandler<HTMLFormElement> = (e) => {
        e.preventDefault();

        // --- Optimistic Add --- 
        const tempId = -Date.now(); 
        const newUser: User = {
            id: tempId,
            name: createUserForm.data.name,
            username: createUserForm.data.username,
            email: createUserForm.data.email,
            role: createUserForm.data.role,
        };
        const originalUsers = [...users]; 
        setUsers(prev => [newUser, ...prev]); 
        setOpenCreateModal(false); 
        // --- End Optimistic Add ---

        createUserForm.post(route('admin.users.store'), {
            preserveScroll: true,
            onSuccess: () => { 
                toast.success("User created successfully!");
                createUserForm.reset('password', 'password_confirmation', 'name', 'email', 'username'); 
                // Optimistic add done. List updates via props later.
            },
            onError: () => {
                toast.error("Failed to create user. Please check the form errors.");
                // --- Revert Optimistic Add ---
                setUsers(originalUsers); 
                setOpenCreateModal(true); // Re-open modal
                // --- End Revert ---
            },
        });
    };

    // Submit handler for editing users - Refined Error Handling
    const handleEditSubmit: React.FormEventHandler<HTMLFormElement> = (e) => {
        e.preventDefault();
        if (!editingUser) return;

        const userId = editingUser.id;
        const originalUsers = [...users];

        // Optimistic Update (update only data fields, not actions)
        const updatedUsers = users.map(u => 
            u.id === userId 
                ? { ...u,
                    name: editUserForm.data.name,
                    username: editUserForm.data.username,
                    role: editUserForm.data.role,
                  }
                : u 
        );
        setUsers(updatedUsers);
        setEditingUser(null); // Close modal optimistically

        editUserForm.put(route('admin.users.update', userId), {
             preserveScroll: true,
             onSuccess: () => {
                 toast.success('User updated successfully!');
                 // Optimistic update already applied
             },
             onError: () => {
                 toast.error('Update failed. Please check errors.');
                 // Revert optimistic update
                 setUsers(originalUsers);
                 // Keep modal open by *not* setting editingUser to null here.
                 // Instead, re-set it to ensure form has correct context if needed?
                 // Or rely on errors populating in the still-open modal context?
                 // Let's stick to just reverting the list and leaving modal state as is (should stay closed).
                 // If user needs to retry, they reopen. Let's re-close it explicitly on error too for now.
                 setEditingUser(null); 
             },
             onFinish: () => {
                 editUserForm.reset('password', 'password_confirmation');
             }
        });
    };

    if (auth?.user?.role !== 'Admin') {
        return <div className="p-6 text-red-600">You do not have permission to view this page.</div>;
    }

    // Map users data state to include JSX actions for rendering
    const usersWithActions = (users || []).map(user => ({
        ...user, // Include all data fields from user state
        // Generate actions JSX here
        actions: (
            <div className="flex gap-2">
                 {/* Edit Dialog Trigger */}
                <Dialog open={editingUser?.id === user.id} onOpenChange={(isOpen) => {
                    if (!isOpen) {
                        setEditingUser(null);
                    }
                }}>
                     <DialogTrigger asChild>
                        <Button size="icon" variant="ghost" aria-label="Edit User" onClick={() => handleEditClick(user)} disabled={user.id < 0}>
                            <Pencil className="h-4 w-4" />
                        </Button>
                    </DialogTrigger>
                     <DialogContent className="max-w-md w-full">
                         <DialogTitle>Edit User</DialogTitle>
                         <DialogDescription>Edit the user's details below to update account.</DialogDescription>
                         {(editingUser && editingUser.id === user.id) && (
                             <UserEditForm 
                                user={editingUser} 
                                data={editUserForm.data}
                                setData={editUserForm.setData}
                                errors={editUserForm.errors}
                                processing={editUserForm.processing}
                                onSubmit={handleEditSubmit} 
                                onClose={() => setEditingUser(null)} 
                            />
                        )}
                    </DialogContent>
                 </Dialog>
                {/* Delete Dialog Trigger */}
                 <Dialog>
                     <DialogTrigger asChild>
                         <Button size="icon" variant="ghost" aria-label="Delete User" disabled={deletingUserId === user.id || user.id < 0}>
                            <Trash2 className="h-4 w-4 text-red-500" />
                            {deletingUserId === user.id && <span className="ml-2 inline-block w-4 h-4 animate-spin border-2 border-t-transparent border-red-500 rounded-full"></span>}
                        </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-md w-full">
                        <DialogTitle>Delete User</DialogTitle>
                        <DialogDescription><b>{user.name}</b> will be deleted. This action cannot be undone.</DialogDescription>
                        <div className="flex justify-end gap-2 mt-4">
                            <DialogClose asChild>
                                <Button variant="outline" disabled={deletingUserId === user.id}>
                                    Cancel
                                </Button>
                            </DialogClose>
                            <Button variant="destructive" onClick={() => handleDelete(user.id)} disabled={deletingUserId === user.id}>
                                {deletingUserId === user.id && <span className="mr-2 inline-block w-4 h-4 animate-spin border-2 border-t-transparent border-white rounded-full"></span>}
                                Delete
                            </Button>
                        </div>
                    </DialogContent>
                 </Dialog>
            </div>
        )
    }));

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="User management" />
            <SettingsLayout>
                <div className="space-y-6">
                    <div className="flex items-center justify-between">
                        <HeadingSmall title="User Management" description="Admins can manage users here." />
                        <Dialog open={openCreateModal} onOpenChange={setOpenCreateModal}>
                            <DialogTrigger asChild>
                                <Button onClick={() => { 
                                    createUserForm.reset(); 
                                    setOpenCreateModal(true); 
                                }}>Create User</Button>
                            </DialogTrigger>
                            <DialogContent className="max-w-md w-full">
                                <DialogTitle>Create User</DialogTitle>
                                <DialogDescription>Fill in the form to create a new user account.</DialogDescription>
                                <CreateUserForm 
                                    data={createUserForm.data}
                                    setData={createUserForm.setData}
                                    errors={createUserForm.errors}
                                    processing={createUserForm.processing}
                                    onSubmit={handleCreateSubmit}
                                />
                            </DialogContent>
                        </Dialog>
                    </div>
                    <DataTable columns={userColumns} data={usersWithActions} globalFilterPlaceholder="Search users..." />
                </div>
            </SettingsLayout>
        </AppLayout>
    );
}
