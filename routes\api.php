<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Todo\TodoController;
use App\Http\Controllers\Auth\ApiAuthController;
use App\Http\Controllers\Accounts\AccountTypeController;
use App\Http\Controllers\Accounts\AccountController;
use App\Http\Controllers\Accounts\CurrencyController;
use App\Http\Controllers\JournalEntry\JournalEntryController;
use App\Http\Controllers\JournalEntry\PaymentTypeController;
use App\Http\Controllers\Reports\LedgerController;
use App\Http\Controllers\ChequeEntry\ChequeController;
use App\Http\Controllers\Transaction\PurchaseController;
use App\Http\Controllers\Transaction\SaleController;
use App\Http\Controllers\RecalculationStatusController;

Route::post('login', [ApiAuthController::class, 'login']);

Route::middleware(['auth:sanctum'])->group(function () {
    // Define all custome routes before apiResource routes
    Route::get('journal-entries/by-date', [JournalEntryController::class, 'getEntriesByDate']);
    // Additional route for multiple journal entries
    Route::post('journal-entries/multiple', [JournalEntryController::class, 'storeMultiple']);
    // Route for ledger report
    Route::get('/ledgers-pkr/data', [JournalEntryController::class, 'getLedgerData']);
    // API route for PDF generation
    Route::get('/reports/ledger-pkr/pdf', [JournalEntryController::class, 'generateLedgerPdf']);
    
    // Route for balance sheet report
    Route::get('/reports/balance-sheet', [JournalEntryController::class, 'getBalanceSheetData']);
    // Add route for chq_ref_banks listing
    Route::get('chq-ref-banks', [AccountController::class, 'listChqRefBanks']);

    // Route for multiple cheque entries
    Route::post('cheque-entries/multiple', [ChequeController::class, 'storeMultiple']);

    // GET route for fetching initial details for a new purchase entry based on currency account
    Route::get('purchase-entries/get-details', [PurchaseController::class, 'getPurchaseEntryDetails']);

    // GET route for fetching initial details for a new sale entry
    Route::get('sale-entries/get-details', [SaleController::class, 'getSaleEntryDetails']);

    // Recalculation status routes
    Route::prefix('recalculation-status')->group(function () {
        Route::get('account', [RecalculationStatusController::class, 'getAccountStatus']);
        Route::get('system', [RecalculationStatusController::class, 'getSystemStatus']);
        Route::post('retry-failed', [RecalculationStatusController::class, 'retryFailed']);
        Route::delete('clear-old', [RecalculationStatusController::class, 'clearOldLogs']);
    });

     // Define specific routes for sales if the resource controller handles both purchases and sales.
    // If TransactionController's index, show, update, destroy are only for purchases, then we need a new controller or named routes for sales.
    // Given the current structure, let's add specific routes for sales pointing to the new methods in TransactionController.

    // API endpoints using Laravel 12's simplified apiResource
    Route::apiResource('account-types', AccountTypeController::class);
    Route::apiResource('accounts', AccountController::class);
    Route::apiResource('currencies', CurrencyController::class);
    Route::apiResource('todos', TodoController::class);
    Route::apiResource('payment-types', PaymentTypeController::class);
    Route::apiResource('journal-entries', JournalEntryController::class);
    Route::apiResource('cheque-entries', ChequeController::class);
    Route::apiResource('purchase-entries', PurchaseController::class);
    Route::apiResource('sale-entries', SaleController::class);
});

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

