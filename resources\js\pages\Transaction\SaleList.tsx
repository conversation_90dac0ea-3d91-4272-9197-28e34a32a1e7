import React, { useState, useEffect, useMemo } from 'react';
import { Head, Link, router } from '@inertiajs/react';
import axios from 'axios';
import { format } from 'date-fns';
import {
    ColumnDef,
    flexRender,
    getCoreRowModel,
    getPaginationRowModel,
    useReactTable,
    SortingState,
    getSortedRowModel,
    PaginationState,
} from '@tanstack/react-table';

// Shadcn/UI Components
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ComboBox, type ComboBoxOption } from '@/components/ui/combobox';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

// Icons
import { CalendarIcon, FilterIcon, PencilIcon, Trash2Icon, Loader2, ArrowUpDown, PlusCircle, MoreHorizontal, ListFilter } from 'lucide-react';

import AppLayout from '@/layouts/app-layout';
import { BreadcrumbItem } from '@/types';
import { toast } from 'sonner';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';

// Helper for formatting numbers (consistent with Sale.tsx)
const formatNumber = (value: number | string | null | undefined, options?: Intl.NumberFormatOptions) => {
    if (value === null || value === undefined) return '';
    const num = typeof value === 'string' ? parseFloat(String(value).replace(/,/g, '')) : Number(value);
    if (isNaN(num)) return '';
    return num.toLocaleString(undefined, options);
};

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Dashboard', href: route('dashboard') },
    { title: 'Sale List', href: route('sale.list') }, 
];

interface Account {
    id: number;
    name: string;
    currency_code?: string;
}

// Based on SaleController@index response structure
interface SaleListItem {
    id: string;
    date: string;
    ref_no: string | null;
    order_id: string;
    customer_account_name: string;
    currency_account_name: string;
    currency_code: string | null;
    currency_amount: number;
    rate: number;
    pkr_amount: number;
}

interface Filters {
    date_from: string;
    date_to: string;
    customer_account_id: string;
    currency_account_id: string;
}

// Adjusted to match Laravel's default paginator JSON structure
interface PaginatedSalesResponse {
    data: SaleListItem[];
    links: {
        first: string | null;
        last: string | null;
        prev: string | null;
        next: string | null;
    };
    // Pagination fields are at the root of the response object (response.data)
    current_page: number;
    from: number | null;
    last_page: number;
    path: string;
    per_page: number;
    to: number | null;
    total: number;
}

const initialFilters: Filters = {
    date_from: '',
    date_to: '',
    customer_account_id: '',
    currency_account_id: '',
};

export default function SaleList() {
    const [sales, setSales] = useState<SaleListItem[]>([]);
    const [customerAccounts, setCustomerAccounts] = useState<Account[]>([]);
    const [currencyAccounts, setCurrencyAccounts] = useState<Account[]>([]);
    
    const [filters, setFilters] = useState<Filters>(initialFilters);
    const [tempFilters, setTempFilters] = useState<Filters>(initialFilters);

    const [loading, setLoading] = useState(false);
    const [datePickerFromOpen, setDatePickerFromOpen] = useState(false);
    const [datePickerToOpen, setDatePickerToOpen] = useState(false);
    
    const [pagination, setPagination] = useState<PaginationState>({
        pageIndex: 0, 
        pageSize: 15, 
    });
    const [pageCount, setPageCount] = useState(0);
    const [sorting, setSorting] = useState<SortingState>([]);
    const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
    const [saleToDelete, setSaleToDelete] = useState<SaleListItem | null>(null);
    const [isDeleting, setIsDeleting] = useState(false);

    const fetchSales = async () => {
        setLoading(true);
        const queryParams: Record<string, string | number | undefined> = {
            ...filters,
            page: pagination.pageIndex + 1, 
            per_page: pagination.pageSize,
            sort_by: sorting.length > 0 ? sorting[0].id : 'date',
            sort_direction: sorting.length > 0 ? (sorting[0].desc ? 'desc' : 'asc') : 'desc',
        };
        
        const activeFilters = Object.fromEntries(
            Object.entries(queryParams).filter(([, value]) => value !== '' && value !== undefined && value !== null)
        );

        try {
            const response = await axios.get<PaginatedSalesResponse>(route('sale-entries.index'), { params: activeFilters });
            setSales(response.data.data);
            // Access last_page directly from response.data
            setPageCount(response.data.last_page);
        } catch (err) {
            console.error("Failed to fetch sales", err);
            toast.error("Failed to load sale data.");
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        const fetchFilterAccounts = async () => {
            try {
                const [customerRes, currencyRes] = await Promise.all([
                    axios.get('api/accounts', { params: { exclude_account_type_ids: '4,5,7' } }),
                    axios.get('api/accounts', { params: { account_type_id: 5 } })
                ]);

                // Simplify parsing: assume API returns an array directly when these filters are applied,
                // or an object where .data is the array (e.g. paginated but with results)
                // Default to empty array if the structure is not as expected.
                const rawCustomerData = customerRes.data;
                setCustomerAccounts(Array.isArray(rawCustomerData) ? rawCustomerData : Array.isArray(rawCustomerData?.data) ? rawCustomerData.data : []);

                const rawCurrencyData = currencyRes.data;
                setCurrencyAccounts(Array.isArray(rawCurrencyData) ? rawCurrencyData : Array.isArray(rawCurrencyData?.data) ? rawCurrencyData.data : []);

            } catch (error) {
                console.error('Error fetching accounts for filters:', error);
                toast.error('Failed to load filter accounts.');
                // Ensure state remains an array on error to prevent .map issues
                setCustomerAccounts([]);
                setCurrencyAccounts([]);
            }
        };
        fetchFilterAccounts();
    }, []);

    useEffect(() => {
        fetchSales();
    }, [filters, pagination, sorting]);

    const handleTempFilterChange = (field: keyof Filters, value: string) => {
        setTempFilters(prev => ({ ...prev, [field]: value }));
    };
    
    const handleTempDateChange = (field: 'date_from' | 'date_to', selectedDate: Date | undefined) => {
        if (selectedDate) {
            handleTempFilterChange(field, format(selectedDate, 'yyyy-MM-dd'));
        } else {
            handleTempFilterChange(field, '');
        }
        if (field === 'date_from') setDatePickerFromOpen(false);
        if (field === 'date_to') setDatePickerToOpen(false);
    };

    const applyFilters = () => {
        setPagination(prev => ({ ...prev, pageIndex: 0 }));
        setFilters(tempFilters); 
    };

    const clearFilters = () => {
        setTempFilters(initialFilters);
        setFilters(initialFilters);
        setPagination(prev => ({ ...prev, pageIndex: 0}));
    };
    
    const openDeleteDialog = (sale: SaleListItem) => {
        setSaleToDelete(sale);
        setDeleteDialogOpen(true);
    };

    const handleDeleteConfirm = async () => {
        if (!saleToDelete) return;
        setIsDeleting(true);
        try {
            await axios.delete(route('sale-entries.destroy', saleToDelete.id));
            toast.success(`Sale S-NO: ${saleToDelete.order_id} deleted successfully.`);
            fetchSales();
        } catch (error: any) {
            console.error('Error deleting sale:', error);
            toast.error(error.response?.data?.error || 'Failed to delete sale.');
        } finally {
            setIsDeleting(false);
            setDeleteDialogOpen(false);
            setSaleToDelete(null);
        }
    };

    const columns = useMemo<ColumnDef<SaleListItem>[]>(() => [
        {
            accessorKey: 'date',
            header: ({ column }) => (
                <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
                    Date <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
            ),
            cell: ({ row }) => format(new Date(row.original.date + 'T00:00:00'), "dd-MMM-yy"),
        },
        {
            accessorKey: 'ref_no',
            header: 'Ref #',
            cell: ({ row }) => row.original.ref_no || '-',
        },
        {
            accessorKey: 'order_id',
            header: ({ column }) => (
                <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
                    S-No <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
            ),
        },
        {
            accessorKey: 'customer_account_name',
            header: 'Customer',
        },
        {
            accessorKey: 'currency_account_name',
            header: 'Currency A/c',
        },
        {
            accessorKey: 'currency_amount',
            header: ({ column }) => (
                <div className="text-right">
                    <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
                        Curr. Amount <ArrowUpDown className="ml-2 h-4 w-4" />
                    </Button>
                </div>
            ),
            cell: ({ row }) => (
                <div className="text-right">
                    {formatNumber(row.original.currency_amount, { minimumFractionDigits: 2, maximumFractionDigits: 2 })} {row.original.currency_code}
                </div>
            ),
        },
        {
            accessorKey: 'rate',
             header: ({ column }) => (
                <div className="text-right">
                    <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
                        Rate <ArrowUpDown className="ml-2 h-4 w-4" />
                    </Button>
                </div>
            ),
            cell: ({ row }) => <div className="text-right">{formatNumber(row.original.rate, { minimumFractionDigits: 2, maximumFractionDigits: 5 })}</div>,
        },
        {
            accessorKey: 'pkr_amount',
            header: ({ column }) => (
                <div className="text-right">
                    <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
                        PKR Amount <ArrowUpDown className="ml-2 h-4 w-4" />
                    </Button>
                </div>
            ),
            cell: ({ row }) => <div className="text-right">{formatNumber(row.original.pkr_amount, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</div>,
        },
        {
            id: 'actions',
            cell: ({ row }) => {
                const sale = row.original;
                return (
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                                <span className="sr-only">Open menu</span>
                                <MoreHorizontal className="h-4 w-4" />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem onClick={() => router.visit(route('sale.edit', sale.id))}>
                                <PencilIcon className="mr-2 h-4 w-4" /> Edit
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => openDeleteDialog(sale)} className="text-red-600 focus:text-red-700 focus:bg-red-50">
                                <Trash2Icon className="mr-2 h-4 w-4" /> Delete
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                );
            },
        },
    ], []);

    const table = useReactTable({
        data: sales,
        columns,
        state: {
            sorting,
            pagination,
        },
        pageCount: pageCount,
        onSortingChange: setSorting,
        onPaginationChange: setPagination,
        getCoreRowModel: getCoreRowModel(),
        getSortedRowModel: getSortedRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        manualPagination: true,
        manualSorting: true,
    });

    const customerOptions: ComboBoxOption[] = customerAccounts.map(acc => ({ label: acc.name, value: acc.id.toString() }));
    const currencyOptions: ComboBoxOption[] = currencyAccounts.map(acc => ({ label: `${acc.name} ${acc.currency_code ? '('+acc.currency_code+')' : ''}`, value: acc.id.toString() }));

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Sale List" />
            <div className="container mx-auto py-6 px-4 md:px-6">
                <Card>
                    <CardHeader>
                        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
                            <CardTitle>Sale Transactions</CardTitle>
                            <Link href={route('sale.create')}>
                                <Button>
                                    <PlusCircle className="mr-2 h-4 w-4" /> Add New Sale
                                </Button>
                            </Link>
                        </div>
                    </CardHeader>
                    <CardContent>
                        <div className="mb-6 p-4 border rounded-lg bg-muted/40">
                            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 items-end">
                                <div>
                                    <Label htmlFor="date_from">Date From</Label>
                                    <Popover open={datePickerFromOpen} onOpenChange={setDatePickerFromOpen}>
                                        <PopoverTrigger asChild>
                                            <Button variant="outline" className="w-full justify-start text-left font-normal">
                                                <CalendarIcon className="mr-2 h-4 w-4" />
                                                {tempFilters.date_from ? format(new Date(tempFilters.date_from + 'T00:00:00'), 'PPP') : <span>Pick a date</span>}
                                            </Button>
                                        </PopoverTrigger>
                                        <PopoverContent className="w-auto p-0"><Calendar mode="single" selected={tempFilters.date_from ? new Date(tempFilters.date_from + 'T00:00:00') : undefined} onSelect={(d) => handleTempDateChange('date_from', d)} /></PopoverContent>
                                    </Popover>
                                </div>
                                <div>
                                    <Label htmlFor="date_to">Date To</Label>
                                    <Popover open={datePickerToOpen} onOpenChange={setDatePickerToOpen}>
                                        <PopoverTrigger asChild>
                                            <Button variant="outline" className="w-full justify-start text-left font-normal">
                                                <CalendarIcon className="mr-2 h-4 w-4" />
                                                {tempFilters.date_to ? format(new Date(tempFilters.date_to + 'T00:00:00'), 'PPP') : <span>Pick a date</span>}
                                            </Button>
                                        </PopoverTrigger>
                                        <PopoverContent className="w-auto p-0"><Calendar mode="single" selected={tempFilters.date_to ? new Date(tempFilters.date_to + 'T00:00:00') : undefined} onSelect={(d) => handleTempDateChange('date_to', d)} /></PopoverContent>
                                    </Popover>
                                </div>
                                <div>
                                    <Label htmlFor="customer_account_id">Customer</Label>
                                    <ComboBox
                                        options={customerOptions}
                                        value={tempFilters.customer_account_id}
                                        onChange={(val) => handleTempFilterChange('customer_account_id', val)}
                                        placeholder="Select Customer..."
                                    />
                                </div>
                                <div>
                                    <Label htmlFor="currency_account_id">Currency Account</Label>
                                    <ComboBox
                                        options={currencyOptions}
                                        value={tempFilters.currency_account_id}
                                        onChange={(val) => handleTempFilterChange('currency_account_id', val)}
                                        placeholder="Select Currency A/c..."
                                    />
                                </div>
                            </div>
                            <div className="flex justify-end gap-2 mt-4">
                                <Button variant="outline" onClick={clearFilters} disabled={loading}>Clear Filters</Button>
                                <Button onClick={applyFilters} disabled={loading}>
                                    {loading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <FilterIcon className="mr-2 h-4 w-4" />}
                                    Apply Filters
                                </Button>
                            </div>
                        </div>

                        <div className="rounded-md border">
                            <Table>
                                <TableHeader>
                                    {table.getHeaderGroups().map(headerGroup => (
                                        <TableRow key={headerGroup.id}>
                                            {headerGroup.headers.map(header => (
                                                <TableHead key={header.id} style={{ width: header.getSize() !== 150 ? header.getSize() : undefined }}>
                                                    {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                                                </TableHead>
                                            ))}
                                        </TableRow>
                                    ))}
                                </TableHeader>
                                <TableBody>
                                    {table.getRowModel().rows?.length ? (
                                        table.getRowModel().rows.map(row => (
                                            <TableRow key={row.id} data-state={row.getIsSelected() && "selected"}>
                                                {row.getVisibleCells().map(cell => (
                                                    <TableCell key={cell.id}>
                                                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                                                    </TableCell>
                                                ))}
                                            </TableRow>
                                        ))
                                    ) : (
                                        <TableRow>
                                            <TableCell colSpan={columns.length} className="h-24 text-center">
                                                {loading ? 'Loading sales...' : 'No sales found.'}
                                            </TableCell>
                                        </TableRow>
                                    )}
                                </TableBody>
                            </Table>
                        </div>

                        <div className="flex items-center justify-between space-x-2 py-4">
                            <div className="text-sm text-muted-foreground">
                                Page {table.getState().pagination.pageIndex + 1} of {table.getPageCount()}
                            </div>
                             <div className="flex items-center space-x-2">
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => table.setPageIndex(0)}
                                    disabled={!table.getCanPreviousPage()}
                                >
                                    First
                                </Button>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => table.previousPage()}
                                    disabled={!table.getCanPreviousPage()}
                                >
                                    Previous
                                </Button>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => table.nextPage()}
                                    disabled={!table.getCanNextPage()}
                                >
                                    Next
                                </Button>
                                 <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => table.setPageIndex(table.getPageCount() - 1)}
                                    disabled={!table.getCanNextPage()}
                                >
                                    Last
                                </Button>
                            </div>
                            <div className="flex items-center space-x-2">
                                <Select
                                    value={table.getState().pagination.pageSize.toString()}
                                    onValueChange={(value) => {
                                        table.setPageSize(Number(value));
                                    }}
                                >
                                    <SelectTrigger className="w-[100px]">
                                        <SelectValue placeholder={`${table.getState().pagination.pageSize} / page`} />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {[10, 15, 20, 30, 50].map(pageSize => (
                                            <SelectItem key={pageSize} value={pageSize.toString()}>
                                                {pageSize} / page
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>
                    </CardContent>
                </Card>
                 <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
                    <DialogContent>
                        <DialogHeader>
                            <DialogTitle>Confirm Deletion</DialogTitle>
                            <DialogDescription>
                                Are you sure you want to delete Sale S-NO: <strong>{saleToDelete?.order_id}</strong>? 
                                This action cannot be undone and will remove all associated journal entries.
                            </DialogDescription>
                        </DialogHeader>
                        <DialogFooter>
                            <Button variant="outline" onClick={() => setDeleteDialogOpen(false)} disabled={isDeleting}>Cancel</Button>
                            <Button variant="destructive" onClick={handleDeleteConfirm} disabled={isDeleting}>
                                {isDeleting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Trash2Icon className="mr-2 h-4 w-4" />}
                                Delete
                            </Button>
                        </DialogFooter>
                    </DialogContent>
                </Dialog>
            </div>
        </AppLayout>
    );
} 
