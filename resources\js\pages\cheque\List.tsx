import React, { useState, useEffect } from 'react';
import { NumericFormat } from 'react-number-format';
import { Head, useForm, Link, router } from '@inertiajs/react'; // Added router
import { ColumnDef, CellContext } from '@tanstack/react-table';
import { ComboBox } from '@/components/ui/combobox';
import { DatePicker } from '@/components/ui/date-picker';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { DataTable } from '@/components/ui/data-table';
import { TableFooter, TableRow, TableCell } from '@/components/ui/table';
import AppLayout from '@/layouts/app-layout';
import { BreadcrumbItem } from '@/types';
import { Pencil, Trash2 } from 'lucide-react'; 
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import Heading from '@/components/heading';
import axios from 'axios';
import { toast } from 'sonner'; 
import { format } from 'date-fns'; 

interface Account {
  id: number;
  name: string;
}

// Consolidate Cheque type, assuming ChequeEntry is the more detailed/correct one
interface ChequeEntry {
  id: number;
  entry_type: 'inward' | 'outward';
  entry_date: string;
  cheque_date: string;
  posting_date: string | null;
  from_account: Account;
  to_account: Account;
  amount: number;
  chq_ref_bank: { id: number; name: string };
  chq_no: string;
  status: 'pending' | 'ok' | 'returned';
  days_remaining?: number; // Made optional as it might not always be present
}

type StatusType = '' | 'pending' | 'ok' | 'returned';
type EntryType = '' | 'inward' | 'outward';


const breadcrumbs: BreadcrumbItem[] = [
  { title: 'Dashboard', href: route('dashboard') },
  { title: 'Cheque List', href: '/cheque/list' }, // Use static path
];

export default function List() { 
  const [chequeEntries, setChequeEntries] = useState<ChequeEntry[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchChequeEntries = async () => {
      try {
        const response = await axios.get('/api/cheque-entries');
        setChequeEntries(Object.values(response.data)); // Assuming API returns data as an object with numeric keys
      } catch (error) {
        console.error('Failed to fetch cheque entries:', error);
        toast.error('Failed to load cheque entries. Please try refreshing the page.');
      }
      setLoading(false);
    };
    fetchChequeEntries();
  }, []);

  const handleDelete = (id: number) => {
    if (confirm('Are you sure you want to delete this cheque entry?')) {
      router.delete(`/api/cheque-entries/${id}`, {
        onSuccess: () => {
          toast.success('Cheque entry deleted successfully');
          // Re-fetch or filter out the deleted entry from local state
          setChequeEntries(prevEntries => prevEntries.filter(entry => entry.id !== id));
        },
        onError: (errors: any) => { // Added type for errors
          console.error('Delete error:', errors);
          toast.error('Failed to delete cheque entry. ' + (errors.message || ''));
        },
      });
    }
  };

  // Helper to format date as YYYY-MM-DD in local time
  const formatDateToYYYYMMDD = (date: Date | undefined | null): string => {
    if (!date) return '';
    const d = new Date(date);
    const year = d.getFullYear();
    const month = (d.getMonth() + 1).toString().padStart(2, '0');
    const day = d.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  const { data, setData, processing, reset } = useForm({ // Removed unused get and errors from useForm
    from_cheque_date: '',
    to_cheque_date: '',
    status: '' as StatusType | '',
    entry_type: '' as EntryType | '',
    from_account_id: '',
    to_account_id: '',
  });

  const [fromChequeDateObj, setFromChequeDateObj] = useState<Date | undefined>(undefined);
  const [toChequeDateObj, setToChequeDateObj] = useState<Date | undefined>(undefined);

  const handleFilterSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    axios.get('/api/cheque-entries', { params: data })
      .then(response => {
        setChequeEntries(Object.values(response.data)); // Assuming API returns data as an object with numeric keys
      })
      .catch(error => {
        console.error('Failed to fetch filtered cheque entries:', error);
        toast.error('Failed to load filtered cheque entries.');
      })
      .finally(() => setLoading(false));
  };

  const handleResetFilters = () => {
    const emptyFilters = {
      from_cheque_date: '',
      to_cheque_date: '',
      status: '' as StatusType | '',
      entry_type: '' as EntryType | '',
      from_account_id: '',
      to_account_id: '',
    };
    reset(); // Reset useForm state
    setData(emptyFilters); // Explicitly set data to empty
    setFromChequeDateObj(undefined);
    setToChequeDateObj(undefined);
    setLoading(true);
    axios.get('/api/cheque-entries', { params: emptyFilters })
      .then(response => {
        setChequeEntries(Object.values(response.data)); // Assuming API returns data as an object with numeric keys
      })
      .catch(error => {
        console.error('Failed to fetch cheque entries after reset:', error);
        toast.error('Failed to load cheque entries.');
      })
      .finally(() => setLoading(false));
  };

  const columns: ColumnDef<ChequeEntry>[] = [
    { header: 'Entry Date', accessorKey: 'entry_date', cell: ({ row }) => format(new Date(row.original.entry_date), 'dd-MMM-yyyy') },
    { header: 'Cheque Date', accessorKey: 'cheque_date', cell: ({ row }) => format(new Date(row.original.cheque_date), 'dd-MMM-yyyy') },
    {
      header: 'Posting Date',
      accessorKey: 'posting_date',
      cell: ({ row }: CellContext<ChequeEntry, unknown>) => row.original.posting_date ? format(new Date(row.original.posting_date), 'dd-MMM-yyyy') : '-',
    },
    {
      header: 'Days',
      accessorKey: 'days_remaining',
      cell: ({ row }: CellContext<ChequeEntry, unknown>) => {
        const days = row.original.days_remaining;
        let variant: 'default' | 'destructive' | 'outline' | 'secondary' = 'secondary';
        if (days !== undefined) {
            if (days < 0) variant = 'destructive';
            else if (days <= 5 && days >= 0) variant = 'outline';
            else if (days > 5) variant = 'default';
        }
        return <Badge variant={variant}>{days === undefined ? '-' : `${days} days`}</Badge>;
      },
    },
    { header: 'From Account', accessorKey: 'from_account.name' },
    { header: 'To Account', accessorKey: 'to_account.name' },
    {
      header: 'Amount',
      accessorKey: 'amount',
      cell: ({ row }: CellContext<ChequeEntry, unknown>) => (
        <NumericFormat
          value={row.original.amount}
          displayType="text"
          thousandSeparator={true}
          decimalScale={0}
        />
      ),
    },
    { header: 'Cheque No', accessorKey: 'chq_no' },
    { header: 'Bank', accessorKey: 'chq_ref_bank.name' },
    {
      header: 'Type',
      accessorKey: 'entry_type',
      cell: ({ row }: CellContext<ChequeEntry, unknown>) => (
        <Badge variant={row.original.entry_type === 'inward' ? 'default' : 'secondary'}>
          {row.original.entry_type}
        </Badge>
      ),
    },
    {
      header: 'Status',
      accessorKey: 'status',
      cell: ({ row }: CellContext<ChequeEntry, unknown>) => {
        const status = row.original.status;
        let variant: 'default' | 'destructive' | 'outline' | 'secondary' = 'secondary';
        if (status === 'ok') variant = 'default';
        else if (status === 'returned') variant = 'destructive';
        return <Badge variant={variant}>{status}</Badge>;
      },
    },
    {
      header: 'Actions',
      id: 'actions',
      cell: ({ row }: CellContext<ChequeEntry, unknown>) => (
        <div className="flex space-x-2">
          <Link href={`/cheque/edit/${row.original.id}`}>
            <Button variant="ghost" size="icon">
              <Pencil className="h-4 w-4" />
            </Button>
          </Link>
          <Button variant="ghost" size="icon" onClick={() => handleDelete(row.original.id)}>
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ];

  const totalCheques = (chequeEntries ?? []).length;
  const totalAmount = (chequeEntries ?? []).reduce((sum, cheque) => sum + Number(cheque.amount), 0);

  // Fetch accounts for filter dropdowns
  const [accounts, setAccounts] = useState<Account[]>([]);
  useEffect(() => {
    axios.get('/api/accounts')
      .then(response => setAccounts(response.data))
      .catch(error => {
        console.error('Failed to fetch accounts for filters:', error);
        toast.error('Failed to load accounts for filters.');
      });
  }, []);

  // if (loading) {
  //   return (
  //     <AppLayout breadcrumbs={breadcrumbs}>
  //       <Head>
  //         <title>Cheque Entries</title>
  //       </Head>
  //       <div className="container mx-auto py-6">
  //         <p>Loading cheque entries...</p>
  //       </div>
  //     </AppLayout>
  //   );
  // }

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head>
        <title>Cheque List</title>
      </Head>
      <div className="container mx-auto p-6">
        <Heading title="Cheque Entries List" description="View and filter cheque entries." />
        <Card className="mb-4">
          <CardHeader>
            <CardTitle>Filters</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleFilterSubmit}>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-7 gap-2 items-end">
                <div className='min-w-[140px]'>
                  <label htmlFor="from_cheque_date" className="block text-sm font-medium text-gray-700 mb-1">From Cheque Date</label>
                  <DatePicker
                    selected={fromChequeDateObj}
                    onSelect={(date) => {
                      setFromChequeDateObj(date);
                      setData('from_cheque_date', formatDateToYYYYMMDD(date));
                    }}
                  />
                </div>
                <div className='min-w-[140px]'>
                  <label htmlFor="to_cheque_date" className="block text-sm font-medium text-gray-700 mb-1">To Cheque Date</label>
                  <DatePicker
                    selected={toChequeDateObj}
                    onSelect={(date) => {
                      setToChequeDateObj(date);
                      setData('to_cheque_date', formatDateToYYYYMMDD(date));
                    }}
                  />
                </div>
                <div className='min-w-[140px]'>
                  <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                  <ComboBox
                    options={[
                      { value: '', label: 'All Statuses' },
                      { value: 'pending', label: 'Pending' },
                      { value: 'ok', label: 'OK' },
                      { value: 'returned', label: 'Returned' },
                    ]}
                    value={data.status}
                    onChange={(value) => setData('status', value as StatusType | '')}
                    placeholder="Status..."
                  />
                </div>
                <div className='min-w-[140px]'>
                  <label htmlFor="entry_type" className="block text-sm font-medium text-gray-700 mb-1">Entry Type</label>
                  <ComboBox
                    options={[
                      { value: '', label: 'All Types' },
                      { value: 'inward', label: 'Inward' },
                      { value: 'outward', label: 'Outward' },
                    ]}
                    value={data.entry_type}
                    onChange={(value) => setData('entry_type', value as EntryType | '')}
                    placeholder="Select type"
                  />
                </div>
                <div className='min-w-[140px]'>
                  <label htmlFor="from_account_id" className="block text-sm font-medium text-gray-700 mb-1">From Account</label>
                  <ComboBox
                    options={[{ value: '', label: 'All Accounts' }, ...accounts.map((acc) => ({ value: acc.id.toString(), label: acc.name }))]}
                    value={data.from_account_id}
                    onChange={(value) => setData('from_account_id', value)}
                    placeholder="From Party..."
                  />
                </div>
                <div className='min-w-[140px]'>
                  <label htmlFor="to_account_id" className="block text-sm font-medium text-gray-700 mb-1">To Account</label>
                  <ComboBox
                    options={[{ value: '', label: 'All Accounts' }, ...accounts.map((acc) => ({ value: acc.id.toString(), label: acc.name }))]}
                    value={data.to_account_id}
                    onChange={(value) => setData('to_account_id', value)}
                    placeholder="To Party..."
                  />
                </div>
                <div className="flex space-x-2">
                  <Button
                    type="submit"
                    disabled={processing || loading}
                    className="bg-red-600 hover:bg-red-700 text-white mt-auto"
                  >
                    Filter!
                  </Button>
                </div>
              </div>
            </form>
          </CardContent>
        </Card>

        <DataTable
          columns={columns}
          data={chequeEntries ?? []}
          tableFooter={ // Corrected prop name from tableFooter to footer
            <TableFooter>
              <TableRow>
                <TableCell colSpan={4}>
                  Total Cheques: {totalCheques}
                </TableCell>
                <TableCell colSpan={2} className="text-right">
                  Total Amount:
                </TableCell>
                <TableCell colSpan={6}>
                  <NumericFormat
                    value={totalAmount}
                      displayType={'text'}
                    thousandSeparator={true}
                    decimalScale={0}
                  />
                </TableCell>
              </TableRow>
            </TableFooter>
          }
        />
      </div>
    </AppLayout>
  );
}

