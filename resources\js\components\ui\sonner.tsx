import * as React from "react"
import { useTheme } from "next-themes"
import { Toaster as Son<PERSON>, ToasterP<PERSON>, toast as sonnerToast, ExternalToast } from "sonner"
import { CheckCircle, XCircle, AlertCircle, Info, X } from "lucide-react"
import { cn } from "@/lib/utils"

// Helper function to get current theme
const getCurrentTheme = () => {
  if (typeof window === 'undefined') return 'light'
  return document.documentElement.classList.contains('dark') ? 'dark' : 'light'
}

type ToastPosition =
  | "top-left"
  | "top-center"
  | "top-right"
  | "bottom-left"
  | "bottom-center"
  | "bottom-right"

type ToastVariant = "default" | "success" | "error" | "warning" | "info"

interface ToastAction {
  label: string
  onClick: () => void
  variant?: "default" | "destructive"
}

interface CustomToastProps extends ExternalToast {
  variant?: ToastVariant
  title?: string
  description?: string
  action?: ToastAction
  cancel?: {
    label: string
    onClick?: () => void
  }
  richContent?: React.ReactNode
}

interface ToasterProps extends Omit<ToasterProps, 'position'> {
  position?: ToastPosition
  variant?: "default" | "rich"
  animation?: "slide" | "fade" | "scale"
}

const Toaster = ({
  position = "bottom-right",
  variant = "default",
  animation = "slide",
  ...props
}: ToasterProps) => {
  const { theme = "system" } = useTheme()

  const getAnimationClass = () => {
    switch (animation) {
      case "fade":
        return "data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-80 data-[state=open]:fade-in-0"
      case "scale":
        return "data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-80 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95"
      default:
        return "data-[state=open]:slide-in-from-bottom-full data-[state=closed]:slide-out-to-bottom-full"
    }
  }

  return (
    <Sonner
      theme={theme as ToasterProps["theme"]}
      position={position}
      className={cn(
        "toaster group",
        getAnimationClass(),
        variant === "rich" && "rich-toaster"
      )}
      style={
        {
          "--normal-bg": "var(--popover)",
          "--normal-text": "var(--popover-foreground)",
          "--normal-border": "var(--border)",
          "--success-bg": "var(--toast-success-bg)",
          "--success-text": "var(--success)",
          "--success-border": "var(--toast-success-border)",
          "--error-bg": "var(--toast-error-bg)",
          "--error-text": "var(--error)",
          "--error-border": "var(--toast-error-border)",
          "--warning-bg": "var(--toast-warning-bg)",
          "--warning-text": "var(--warning)",
          "--warning-border": "var(--toast-warning-border)",
          "--info-bg": "var(--toast-info-bg)",
          "--info-text": "var(--info)",
          "--info-border": "var(--toast-info-border)",
        } as React.CSSProperties
      }
      toastOptions={{
        className: cn(
          "group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",
          variant === "rich" && "group-[.rich-toaster]:min-h-[4rem] group-[.rich-toaster]:p-4"
        ),
        ...props.toastOptions,
      }}
      {...props}
    />
  )
}

// Custom toast component for rich content
const ToastContent = ({
  variant,
  title,
  description,
  action,
  cancel,
  richContent
}: CustomToastProps) => {
  const getIcon = () => {
    switch (variant) {
      case "success":
        return <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400" />
      case "error":
        return <XCircle className="h-5 w-5 text-red-600 dark:text-red-400" />
      case "warning":
        return <AlertCircle className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
      case "info":
        return <Info className="h-5 w-5 text-blue-600 dark:text-blue-400" />
      default:
        return null
    }
  }

  if (richContent) {
    return <div className="w-full">{richContent}</div>
  }

  return (
    <div className="flex items-start gap-3 w-full">
      {getIcon()}
      <div className="flex-1 space-y-1">
        {title && <div className="font-semibold text-sm">{title}</div>}
        {description && <div className="text-sm text-muted-foreground">{description}</div>}
        {(action || cancel) && (
          <div className="flex items-center gap-2 mt-2">
            {action && (
              <button
                onClick={action.onClick}
                className={cn(
                  "px-3 py-1 text-xs font-medium rounded-md transition-colors border",
                  action.variant === "destructive"
                    ? "bg-red-600 text-white border-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800"
                    : variant === "success"
                    ? "bg-green-600 text-white border-green-600 hover:bg-green-700 dark:bg-green-700 dark:hover:bg-green-800"
                    : variant === "warning"
                    ? "bg-yellow-600 text-white border-yellow-600 hover:bg-yellow-700 dark:bg-yellow-700 dark:hover:bg-yellow-800"
                    : variant === "info"
                    ? "bg-blue-600 text-white border-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800"
                    : "bg-slate-600 text-white border-slate-600 hover:bg-slate-700 dark:bg-slate-700 dark:hover:bg-slate-800"
                )}
              >
                {action.label}
              </button>
            )}
            {cancel && (
              <button
                onClick={cancel.onClick}
                className="px-3 py-1 text-xs font-medium rounded-md transition-colors border bg-white text-slate-700 border-slate-300 hover:bg-slate-50 dark:bg-slate-800 dark:text-slate-300 dark:border-slate-600 dark:hover:bg-slate-700"
              >
                {cancel.label}
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  )
}

// Preset toast functions
const toast = {
  // Basic toast
  message: (message: string, options?: ExternalToast) => {
    return sonnerToast(message, options)
  },

  // Success toast
  success: (message: string | CustomToastProps, options?: ExternalToast) => {
    const isDark = getCurrentTheme() === 'dark'
    const styles = isDark
      ? {
          backgroundColor: 'rgb(20 83 45)', // green-900/80
          color: 'rgb(187 247 208)', // green-100
          borderColor: 'rgb(21 128 61)', // green-700
        }
      : {
          backgroundColor: 'rgb(220 252 231)', // green-100
          color: 'rgb(22 101 52)', // green-800
          borderColor: 'rgb(134 239 172)', // green-300
        }

    if (typeof message === "string") {
      return sonnerToast.success(message, {
        icon: <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />,
        className: "toast-success",
        style: styles,
        ...options,
      })
    }
    return sonnerToast(<ToastContent {...message} variant="success" />, {
      className: "toast-success",
      style: styles,
      ...options,
    })
  },

  // Error toast
  error: (message: string | CustomToastProps, options?: ExternalToast) => {
    const isDark = getCurrentTheme() === 'dark'
    const styles = isDark
      ? {
          backgroundColor: 'rgb(127 29 29)', // red-900/80
          color: 'rgb(254 226 226)', // red-100
          borderColor: 'rgb(185 28 28)', // red-700
        }
      : {
          backgroundColor: 'rgb(254 226 226)', // red-100
          color: 'rgb(153 27 27)', // red-800
          borderColor: 'rgb(252 165 165)', // red-300
        }

    if (typeof message === "string") {
      return sonnerToast.error(message, {
        icon: <XCircle className="h-4 w-4 text-red-600 dark:text-red-400" />,
        className: "toast-error",
        style: styles,
        ...options,
      })
    }
    return sonnerToast(<ToastContent {...message} variant="error" />, {
      className: "toast-error",
      style: styles,
      ...options,
    })
  },

  // Warning toast
  warning: (message: string | CustomToastProps, options?: ExternalToast) => {
    const isDark = getCurrentTheme() === 'dark'
    const styles = isDark
      ? {
          backgroundColor: 'rgb(120 113 108)', // yellow-900/80
          color: 'rgb(254 249 195)', // yellow-100
          borderColor: 'rgb(161 98 7)', // yellow-700
        }
      : {
          backgroundColor: 'rgb(254 249 195)', // yellow-100
          color: 'rgb(133 77 14)', // yellow-800
          borderColor: 'rgb(253 224 71)', // yellow-300
        }

    if (typeof message === "string") {
      return sonnerToast.warning(message, {
        icon: <AlertCircle className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />,
        className: "toast-warning",
        style: styles,
        ...options,
      })
    }
    return sonnerToast(<ToastContent {...message} variant="warning" />, {
      className: "toast-warning",
      style: styles,
      ...options,
    })
  },

  // Info toast
  info: (message: string | CustomToastProps, options?: ExternalToast) => {
    const isDark = getCurrentTheme() === 'dark'
    const styles = isDark
      ? {
          backgroundColor: 'rgb(30 58 138)', // blue-900/80
          color: 'rgb(219 234 254)', // blue-100
          borderColor: 'rgb(29 78 216)', // blue-700
        }
      : {
          backgroundColor: 'rgb(219 234 254)', // blue-100
          color: 'rgb(30 64 175)', // blue-800
          borderColor: 'rgb(147 197 253)', // blue-300
        }

    if (typeof message === "string") {
      return sonnerToast.info(message, {
        icon: <Info className="h-4 w-4 text-blue-600 dark:text-blue-400" />,
        className: "toast-info",
        style: styles,
        ...options,
      })
    }
    return sonnerToast(<ToastContent {...message} variant="info" />, {
      className: "toast-info",
      style: styles,
      ...options,
    })
  },

  // Loading toast
  loading: (message: string, options?: ExternalToast) => {
    return sonnerToast.loading(message, options)
  },

  // Promise toast
  promise: sonnerToast.promise,

  // Custom toast with rich content
  custom: (content: React.ReactNode, options?: ExternalToast) => {
    return sonnerToast(content, options)
  },

  // Dismiss toast
  dismiss: sonnerToast.dismiss,
}

export {
  Toaster,
  toast,
  ToastContent,
  type ToastPosition,
  type ToastVariant,
  type ToastAction,
  type CustomToastProps,
  type ToasterProps as CustomToasterProps
}
