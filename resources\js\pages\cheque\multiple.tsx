import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ComboBox } from '@/components/ui/combobox';
import { DatePicker } from '@/components/ui/date-picker';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AppLayout from '@/layouts/app-layout';
import { BreadcrumbItem } from '@/types';
import axios from 'axios';
import { Info, Loader2, Plus, X } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { NumericFormat } from 'react-number-format';
import { toast } from 'sonner';
import { v4 as uuidv4 } from 'uuid';
import { Head } from '@inertiajs/react';

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Multiple Cheque Entries', href: '/cheque/multiple' },
];

interface Account {
    id: number;
    name: string;
    account_type?: {
        name: string;
    };
}

interface Bank {
    id: number;
    name: string;
}

interface ComboBoxOption {
    value: string;
    label: string;
}

interface ChequeEntry {
    id: string;
    chequeDate: Date | undefined;
    fromAccountId: string;
    toAccountId: string;
    chqRefBankId: string;
    chqNo: string;
    amount: string;
}

export default function MultipleChequeEntries() {
    const [entryDate, setEntryDate] = useState<Date | undefined>(new Date());
    const [entryType, setEntryType] = useState<'inward' | 'outward'>('outward');
    const [loading, setLoading] = useState(false);
    const [accounts, setAccounts] = useState<Account[]>([]);
    const [banks, setBanks] = useState<Bank[]>([]);
    const [entries, setEntries] = useState<ChequeEntry[]>([
        createNewEntry(),
    ]);

    // Helper function to create a new empty entry
    function createNewEntry(): ChequeEntry {
        return {
            id: uuidv4(),
            chequeDate: undefined,
            fromAccountId: '',
            toAccountId: '',
            chqRefBankId: '',
            chqNo: '',
            amount: '',
        };
    }

    // Fetch accounts and banks
    useEffect(() => {
        const fetchData = async () => {
            try {
                setLoading(true);
                const [accountsRes, banksRes] = await Promise.all([
                    axios.get('/api/accounts'),
                    axios.get('/api/chq-ref-banks'),
                ]);

                // Filter accounts based on specified types (excluding)
                const excludedTypes = ['currency_account', 'income', 'property'];
                const filteredAccounts = accountsRes.data.filter((account: Account) =>
                    account.account_type && !excludedTypes.includes(account.account_type.name)
                );

                setAccounts(filteredAccounts);
                setBanks(banksRes.data);
            } catch (err) {
                console.error('Error fetching data:', err);
                toast.error('Failed to load required data');
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, []);

    // Update an entry field
    const updateEntry = (id: string, field: keyof ChequeEntry, value: any) => {
        setEntries(prevEntries =>
            prevEntries.map(entry =>
                entry.id === id ? { ...entry, [field]: value } : entry
            )
        );
    };

    // Add a new entry
    const addNewEntry = () => {
        setEntries(prevEntries => [...prevEntries, createNewEntry()]);
    };

    // Remove an entry
    const removeEntry = (id: string) => {
        if (entries.length > 1) {
            setEntries(prevEntries => prevEntries.filter(entry => entry.id !== id));
        }
    };

    // Handle form submission
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        // Validate required fields
        const validationErrors: string[] = [];
        
        if (!entryDate) {
            validationErrors.push('Entry date is required');
        }

        entries.forEach((entry, index) => {
            const row = index + 1;
            if (!entry.chequeDate) validationErrors.push(`Row ${row}: Cheque date is required`);
            if (!entry.fromAccountId) validationErrors.push(`Row ${row}: From Account is required`);
            if (!entry.toAccountId) validationErrors.push(`Row ${row}: To Account is required`);
            if (!entry.chqRefBankId) validationErrors.push(`Row ${row}: Bank is required`);
            if (!entry.chqNo) validationErrors.push(`Row ${row}: Cheque No is required`);
            if (!entry.amount || parseFloat(entry.amount.replace(/,/g, '')) <= 0) {
                validationErrors.push(`Row ${row}: Valid amount is required`);
            }
        });

        if (validationErrors.length > 0) {
            validationErrors.forEach(error => toast.error(error));
            return;
        }

        // Prepare data for submission with proper date formatting
        const formatDateForBackend = (date: Date | undefined) => {
            if (!date) return '';
            // Format the date in local timezone as YYYY-MM-DD
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        };

        const formattedEntries = entries.map(entry => ({
            entry_type: entryType,
            entry_date: formatDateForBackend(entryDate),
            cheque_date: formatDateForBackend(entry.chequeDate),
            from_account_id: parseInt(entry.fromAccountId),
            to_account_id: parseInt(entry.toAccountId),
            chq_ref_bank_id: parseInt(entry.chqRefBankId),
            chq_no: entry.chqNo,
            amount: parseFloat(entry.amount.replace(/,/g, '')),
            status: 'pending',
        }));

        try {
            setLoading(true);
            const response = await axios.post('/api/cheque-entries/multiple', {
                entries: formattedEntries,
            });

            toast.success('Cheque entries created successfully');
            // Reset form
            setEntries([createNewEntry()]);
        } catch (error: any) {
            console.error('Error creating cheque entries:', error);
            
            // Handle validation errors
            if (error.response?.status === 422 && error.response?.data?.errors?.entries) {
                // Show each validation error as a separate toast
                error.response.data.errors.entries.forEach((err: string) => {
                    toast.error(err);
                });
            } else {
                // Fallback to generic error message
                const errorMessage = error.response?.data?.message || 'Failed to create cheque entries';
                toast.error(errorMessage);
            }
        } finally {
            setLoading(false);
        }
    };

    // Calculate total amount
    const totalAmount = entries.reduce((sum, entry) => {
        return sum + (parseFloat(entry.amount.replace(/,/g, '')) || 0);
    }, 0);

    // Prepare account options for combo boxes
    const accountOptions: ComboBoxOption[] = accounts.map(account => ({
        value: account.id.toString(),
        label: account.name,
    }));

    const bankOptions: ComboBoxOption[] = banks.map(bank => ({
        value: bank.id.toString(),
        label: bank.name,
    }));

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Multiple Cheque Entries" />
            <div className="container mx-auto py-6">
                <Card>
                    <CardHeader>
                        <CardTitle>Multiple Cheque Entries</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 gap-4 md:grid-cols-12 mb-6">
                            <div className="flex items-center gap-2 sm:col-span-6 md:col-span-4 lg:col-span-3">
                                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Entry Date:</label>
                                <DatePicker selected={entryDate} onSelect={setEntryDate} />
                            </div>
                            <div className="flex items-center gap-2 sm:col-span-6 md:col-span-4 lg:col-span-3">
                                <label className="text-sm font-medium whitespace-nowrap text-gray-700 dark:text-gray-300">Entry Type:</label>
                                <ComboBox
                                    value={entryType}
                                    onChange={(value: string) => setEntryType(value as 'inward' | 'outward')}
                                    options={[
                                        { value: 'inward', label: 'Inward' },
                                        { value: 'outward', label: 'Outward' },
                                    ]}
                                    placeholder="Select entry type"
                                />
                            </div>
                        </div>

                        <form onSubmit={handleSubmit} className="space-y-4">
                            <div className="overflow-x-auto">
                                <Table>
                                    <TableHeader>
                                        <TableRow>
                                            <TableHead>Cheque Date</TableHead>
                                            <TableHead>From Account</TableHead>
                                            <TableHead>To Account</TableHead>
                                            <TableHead>Bank</TableHead>
                                            <TableHead>Cheque No</TableHead>
                                            <TableHead>Amount</TableHead>
                                            <TableHead className="w-10"></TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {entries.map((entry) => (
                                            <TableRow key={entry.id}>
                                                <TableCell className="w-48">
                                                    <DatePicker
                                                        selected={entry.chequeDate}
                                                        onSelect={(date) => updateEntry(entry.id, 'chequeDate', date)}
                                                    />
                                                </TableCell>
                                                <TableCell className="min-w-[200px]">
                                                    <ComboBox
                                                        value={entry.fromAccountId}
                                                        onChange={(value) => updateEntry(entry.id, 'fromAccountId', value)}
                                                        options={accountOptions}
                                                        placeholder="Select account"
                                                    />
                                                </TableCell>
                                                <TableCell className="min-w-[200px]">
                                                    <ComboBox
                                                        value={entry.toAccountId}
                                                        onChange={(value) => updateEntry(entry.id, 'toAccountId', value)}
                                                        options={accountOptions}
                                                        placeholder="Select account"
                                                    />
                                                </TableCell>
                                                <TableCell className="min-w-[150px]">
                                                    <ComboBox
                                                        value={entry.chqRefBankId}
                                                        onChange={(value) => updateEntry(entry.id, 'chqRefBankId', value)}
                                                        options={bankOptions}
                                                        placeholder="Select bank"
                                                    />
                                                </TableCell>
                                                <TableCell className="w-32">
                                                    <Input
                                                        value={entry.chqNo}
                                                        onChange={(e) => updateEntry(entry.id, 'chqNo', e.target.value)}
                                                        placeholder="Cheque no"
                                                    />
                                                </TableCell>
                                                <TableCell className="w-36">
                                                    <NumericFormat
                                                        value={entry.amount}
                                                        onValueChange={(values) => updateEntry(entry.id, 'amount', values.value)}
                                                        thousandSeparator={true}
                                                        allowNegative={false}
                                                        customInput={Input}
                                                        placeholder="0.00"
                                                    />
                                                </TableCell>
                                                <TableCell className="w-10">
                                                    {entries.length > 1 && (
                                                        <Button
                                                            type="button"
                                                            variant="ghost"
                                                            size="icon"
                                                            onClick={() => removeEntry(entry.id)}
                                                            className="h-8 w-8 p-0"
                                                        >
                                                            <X className="h-4 w-4 text-red-500" />
                                                        </Button>
                                                    )}
                                                </TableCell>
                                            </TableRow>
                                        ))}
                                    </TableBody>
                                </Table>
                            </div>

                            <div className="flex justify-between items-center">
                                <Button
                                    type="button"
                                    variant="outline"
                                    size="sm"
                                    onClick={addNewEntry}
                                    className="mt-2"
                                >
                                    <Plus className="mr-2 h-4 w-4" /> Add Row
                                </Button>
                                
                                <div className="text-right">
                                    <div className="text-lg font-semibold">
                                        Total Amount: {totalAmount.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                                    </div>
                                    <Button
                                        type="submit"
                                        className="mt-2"
                                        disabled={loading}
                                    >
                                        {loading ? (
                                            <>
                                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                                Processing...
                                            </>
                                        ) : (
                                            'Save Entries'
                                        )}
                                    </Button>
                                </div>
                            </div>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}