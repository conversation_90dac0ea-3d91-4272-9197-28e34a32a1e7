import React, { useState, useEffect } from 'react';
import { Head, useForm, router } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { ComboBox } from '@/components/ui/combobox';
import { DatePicker } from '@/components/ui/date-picker';
import { Input } from '@/components/ui/input';
import AppLayout from '@/layouts/app-layout';
import { NumericFormat } from 'react-number-format';
import { toast } from 'sonner';
import { BreadcrumbItem } from '@/types';
import axios from 'axios';

const breadcrumbs: BreadcrumbItem[] = [
  { title: 'Dashboard', href: route('dashboard') },
  { title: 'Cheque List', href: '/cheque/list' },
  { title: 'Edit Cheque', href: '#' }, // Current page, no link needed or link to itself
];

interface Bank {
  id: number;
  name: string;
}

interface Account {
  id: number;
  name: string;
}

// Define ChequeEntry type for form data and fetched cheque data
interface ChequeEntry {
  id: number;
  entry_type: string;
  entry_date: string; // YYYY-MM-DD
  cheque_date: string; // YYYY-MM-DD
  posting_date: string | null; // YYYY-MM-DD
  from_account_id: string | number; // Allow string for form, number for fetched
  to_account_id: string | number;
  amount: string | number;
  chq_ref_bank_id: string | number;
  chq_no: string;
  status: string;
  [key: string]: any; // Add index signature here
}

interface EditProps {
  chequeId: number;
}

// Helper function to format date to YYYY-MM-DD for API
const formatDateLocal = (date: Date): string => {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  return `${year}-${month}-${day}`;
};

export default function Edit({ chequeId }: EditProps) {
  const { data, setData, errors } = useForm<ChequeEntry>({
    id: chequeId,
    entry_type: 'inward', // Default or fetched
    entry_date: '',
    cheque_date: '',
    posting_date: null,
    from_account_id: '',
    to_account_id: '',
    amount: '',
    chq_ref_bank_id: '',
    chq_no: '',
    status: 'pending', // Default or fetched
  });

  const [showPostingDate, setShowPostingDate] = useState(false);
  const [entryDate, setEntryDate] = useState<Date | undefined>(undefined);
  const [chequeDate, setChequeDate] = useState<Date | undefined>(undefined);
  const [postingDate, setPostingDate] = useState<Date | undefined>(undefined);

  // States for fetched data
  const [banks, setBanks] = useState<Bank[]>([]);
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [loading, setLoading] = useState(true);
  const [initialChequeData, setInitialChequeData] = useState<ChequeEntry | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const [chequeRes, banksRes, accountsRes] = await Promise.all([
          axios.get(`/api/cheque-entries/${chequeId}`),
          axios.get('/api/chq-ref-banks'),
          axios.get('/api/accounts'),
        ]);

        const fetchedChequeData: ChequeEntry = chequeRes.data; // Assuming data is not nested under 'data'
        setInitialChequeData(fetchedChequeData);

        setData({
          ...fetchedChequeData,
          // Ensure IDs are strings for ComboBox compatibility if they come as numbers
          from_account_id: fetchedChequeData.from_account_id?.toString() ?? '',
          to_account_id: fetchedChequeData.to_account_id?.toString() ?? '',
          chq_ref_bank_id: fetchedChequeData.chq_ref_bank_id?.toString() ?? '',
          amount: fetchedChequeData.amount?.toString() ?? '', // Amount as string for NumericFormat
        });

        setEntryDate(fetchedChequeData.entry_date ? new Date(fetchedChequeData.entry_date) : undefined);
        setChequeDate(fetchedChequeData.cheque_date ? new Date(fetchedChequeData.cheque_date) : undefined);
        setPostingDate(fetchedChequeData.posting_date ? new Date(fetchedChequeData.posting_date) : undefined);
        setShowPostingDate(fetchedChequeData.entry_type === 'inward');

        setBanks(banksRes.data);
        setAccounts(accountsRes.data);
      } catch (error) {
        console.error('Failed to fetch data:', error);
        toast.error('Failed to load cheque data. Please try refreshing the page.');
        // Optionally redirect or show a more prominent error message
      }
      setLoading(false);
    };

    fetchData();
  }, [chequeId]); // Removed setData from dependency array

  useEffect(() => {
    setShowPostingDate(data.entry_type === 'inward');
  }, [data.entry_type]);

  function handleEntryTypeChange(value: string) {
    setData('entry_type', value);
  }

  function handleEntryDateSelect(date: Date | undefined) {
    setEntryDate(date);
    setData('entry_date', date ? formatDateLocal(date) : '');
  }

  function handleChequeDateSelect(date: Date | undefined) {
    setChequeDate(date);
    setData('cheque_date', date ? formatDateLocal(date) : '');
  }

  function handlePostingDateSelect(date: Date | undefined) {
    setPostingDate(date);
    setData('posting_date', date ? formatDateLocal(date) : '');
  }

  function handleStatusChange(value: string) {
    if (initialChequeData?.status === 'ok' && value === 'pending') {
      toast.error('Cannot change status from OK back to Pending.');
      return;
    }
    if (initialChequeData?.status === 'returned' && value !== 'returned') {
      toast.error('Cannot change status from Returned to other status.');
      return;
    }
    setData('status', value);
  }

  const [submitting, setSubmitting] = useState(false);

  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault();
    if (!data.entry_type || !data.entry_date || !data.cheque_date ||
        !data.from_account_id || !data.to_account_id || data.amount === '' || 
        !data.chq_ref_bank_id || !data.chq_no || !data.status) {
      toast.error('Please fill all required fields.');
      return;
    }
    if (showPostingDate && !data.posting_date) {
      toast.error('Posting date is required for Inward entry');
      return;
    }
    if (isNaN(Number(data.amount)) || Number(data.amount) <= 0) {
      toast.error('Amount must be a positive number');
      return;
    }
  
    setSubmitting(true);
    try {
      const payload = {
        ...data,
        from_account_id: Number(data.from_account_id),
        to_account_id: Number(data.to_account_id),
        chq_ref_bank_id: Number(data.chq_ref_bank_id),
        amount: Number(data.amount),
      };
  
      // Use axios to call the API endpoint
      const response = await axios.put(`/api/cheque-entries/${chequeId}`, payload);
      
      // Show success message
      toast.success(response.data.message || 'Cheque entry updated successfully!');
      
      // Redirect to the list page after successful update
      router.visit('/cheque-list', {
        onSuccess: () => {
          // This will be called after the page loads
        },
        onError: (errors) => {
          console.error('Navigation error:', errors);
        },
        preserveScroll: true,
      });
  
    } catch (error: any) {
      console.error('Failed to update cheque entry:', error);
      if (error.response && error.response.data) {
        if (error.response.data.errors) {
          const validationErrors = Object.values(error.response.data.errors).flat().join(' ');
          toast.error(`Update failed: ${validationErrors}`);
        } else if (error.response.data.message) {
          toast.error(`Update failed: ${error.response.data.message}`);
        } else {
          toast.error('An unexpected error occurred. Please try again.');
        }
      } else {
        toast.error('An unexpected error occurred. Please try again.');
      }
    } finally {
      setSubmitting(false);
    }
  }

  // if (loading) {
  //   return (
  //     <AppLayout breadcrumbs={breadcrumbs}>
  //       <Head><title>Loading Cheque...</title></Head>
  //       <div className="container mx-auto py-6"><p>Loading cheque details...</p></div>
  //     </AppLayout>
  //   );
  // }

  if (!initialChequeData) {
    return (
      <AppLayout breadcrumbs={breadcrumbs}>
        <Head><title>Error</title></Head>
        <div className="container mx-auto py-6"><p>Could not load cheque data. It might have been deleted or an error occurred.</p></div>
      </AppLayout>
    );
  }

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Edit Cheque Entry" />

      <div className="container mx-auto p-6">
        <h1 className="mb-4 text-2xl font-bold">Edit Cheque Entry: {initialChequeData.chq_no}</h1>
          
        <form onSubmit={handleSubmit} className="max-w-2xl space-y-4">
          <div className="space-y-4">
            <div className="grid grid-cols-12 items-center gap-4">
              <label className="col-span-3 text-right">Entry Type</label>
              <div className="col-span-6">
                <ComboBox
                  options={[
                    { value: 'outward', label: 'Outward' },
                    { value: 'inward', label: 'Inward' },
                  ]}
                  value={data.entry_type}
                  onChange={handleEntryTypeChange}
                />
                {errors.entry_type && <p className="text-red-600 text-sm">{errors.entry_type}</p>}
              </div>
            </div>

            <div className="grid grid-cols-12 items-center gap-4">
              <label className="col-span-3 text-right">Entry Date</label>
              <div className="col-span-6 lg:col-span-3">
                <DatePicker selected={entryDate} onSelect={handleEntryDateSelect} />
                {errors.entry_date && <p className="text-red-600 text-sm">{errors.entry_date}</p>}
              </div>
            </div>

            <div className="grid grid-cols-12 items-center gap-4">
              <label className="col-span-3 text-right">Cheque Date</label>
              <div className="col-span-6 lg:col-span-3">
                <DatePicker selected={chequeDate} onSelect={handleChequeDateSelect} />
                {errors.cheque_date && <p className="text-red-600 text-sm">{errors.cheque_date}</p>}
              </div>
            </div>

            {showPostingDate && (
              <div className="grid grid-cols-12 items-center gap-4">
                <label className="col-span-3 text-right">Posting Date</label>
                <div className="col-span-6 lg:col-span-3">
                  <DatePicker selected={postingDate} onSelect={handlePostingDateSelect} />
                  {errors.posting_date && <p className="text-red-600 text-sm">{errors.posting_date}</p>}
                </div>
              </div>
            )}

            <div className="grid grid-cols-12 items-center gap-4">
              <label className="col-span-3 text-right">From Account (Credit)</label>
              <div className="col-span-6">
                <ComboBox
                  options={accounts.map((acc) => ({ value: acc.id.toString(), label: acc.name }))}
                  value={data.from_account_id.toString()} // Ensure value is string
                  onChange={(value: string) => setData('from_account_id', value)}
                  placeholder="Select from account"
                />
                {errors.from_account_id && <p className="text-red-600 text-sm">{errors.from_account_id}</p>}
              </div>
            </div>

            <div className="grid grid-cols-12 items-center gap-4">
              <label className="col-span-3 text-right">To Account (Debit)</label>
              <div className="col-span-6">
                <ComboBox
                  options={accounts.map((acc) => ({ value: acc.id.toString(), label: acc.name }))}
                  value={data.to_account_id.toString()} // Ensure value is string
                  onChange={(value: string) => setData('to_account_id', value)}
                  placeholder="Select to account"
                />
                {errors.to_account_id && <p className="text-red-600 text-sm">{errors.to_account_id}</p>}
              </div>
            </div>

            <div className="grid grid-cols-12 items-center gap-4">
              <label className="col-span-3 text-right">Amount</label>
              <div className="col-span-6">
                <NumericFormat
                  value={data.amount.toString()} // Ensure value is string
                  onValueChange={(values) => setData('amount', values.value)}
                  thousandSeparator={true}
                  decimalScale={0}
                  allowNegative={false}
                  customInput={Input}
                  placeholder="Enter amount"
                  className="w-full"
                />
                {errors.amount && <p className="text-red-600 text-sm">{errors.amount}</p>}
              </div>
            </div>

            <div className="grid grid-cols-12 items-center gap-4">
              <label className="col-span-3 text-right">CHQ Ref Bank</label>
              <div className="col-span-6">
                <ComboBox
                  options={banks.map((bank) => ({ value: bank.id.toString(), label: bank.name }))}
                  value={data.chq_ref_bank_id.toString()} // Ensure value is string
                  onChange={(value: string) => setData('chq_ref_bank_id', value)}
                  placeholder="Select bank"
                />
                {errors.chq_ref_bank_id && <p className="text-red-600 text-sm">{errors.chq_ref_bank_id}</p>}
              </div>
            </div>

            <div className="grid grid-cols-12 items-center gap-4">
              <label className="col-span-3 text-right">CHQ No</label>
              <div className="col-span-6">
                <Input
                  type="text"
                  value={data.chq_no}
                  onChange={(e) => setData('chq_no', e.target.value)}
                  className="w-full"
                />
                {errors.chq_no && <p className="text-red-600 text-sm">{errors.chq_no}</p>}
              </div>
            </div>

            <div className="grid grid-cols-12 items-center gap-4">
              <label className="col-span-3 text-right">Status</label>
              <div className="col-span-6">
                <ComboBox
                  options={[
                    { value: 'ok', label: 'OK' },
                    { value: 'pending', label: 'Pending' },
                    { value: 'returned', label: 'Returned' },
                  ]}
                  value={data.status}
                  onChange={handleStatusChange}
                />
                {errors.status && <p className="text-red-600 text-sm">{errors.status}</p>}
              </div>
            </div>
            
            <div className="grid grid-cols-12 items-center gap-4">
              <div className="col-span-3 text-right" />
              <div className="col-span-6">
                <Button type="submit" disabled={submitting}>
                  {submitting ? 'Updating...' : 'Update Cheque Entry'}
                </Button>
                </div>
            </div>
          </div>
        </form>
          
      </div>
    </AppLayout>
  );
};

