<?php

namespace Database\Seeders;

use App\Models\Account;
use App\Models\AccountType;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class AccountSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // First, get the account types
        $otherType = AccountType::where('name', 'other')->first();
        $incomeType = AccountType::where('name', 'income')->first();

        if (!$otherType || !$incomeType) {
            throw new \Exception('Required account types not found in database');
        }

        // 1. Create OK ACC (Other type)
        Account::create([
            'account_type_id' => $otherType->id,
            'name' => 'OK ACC',
            'description' => 'Khata Checking Account',
            'user_id' => 1,
        ]);

        // 2. Create Profit Transfer Account (Income type)
        Account::create([
            'account_type_id' => $incomeType->id,
            'name' => 'Retained Earnings',
            'description' => 'Retained Earnings Account for transferring profits from P&L to retained earnings',
            'user_id' => 1,
        ]);
    }
}
