# Laravel 12 New Features and Best Practices Guide

## Table of Contents
1. [New Features](#new-features)
2. [Performance Improvements](#performance-improvements)
3. [Security Enhancements](#security-enhancements)
4. [Development Tips](#development-tips)
5. [Best Practices](#best-practices)

## New Features

### 1. Native Type Declarations
Laravel 12 embraces PHP 8.2's native type declarations for enhanced code reliability:
```php
class UserController
{
    public function show(int $id): User
    {
        return User::findOrFail($id);
    }
}
```

### 2. Improved Validation
```php
// New validation features
$request->validate([
    'email' => ['required', 'email', 'unique:users,email'],
    'age' => ['required', 'integer', 'between:18,100'],
    'profile.*.social' => ['array', 'min:1'], // Nested validation
]);
```

### 3. Enhanced Route Groups
```php
Route::prefix('api/v1')->group(function () {
    Route::middleware(['auth:sanctum'])->group(function () {
        Route::resource('users', UserController::class);
        Route::resource('posts', PostController::class);
    });
});
```

## Performance Improvements

### 1. Lazy Collections
```php
// Memory efficient processing of large datasets
$users = User::lazy()
    ->where('active', true)
    ->chunk(1000)
    ->each(function ($user) {
        // Process each user
    });
```

### 2. Query Performance
```php
// New query builder optimizations
User::select(['id', 'name'])
    ->whereHas('posts', function ($query) {
        $query->where('published', true);
    })
    ->lazyById(1000)
    ->each(function ($user) {
        // Process user
    });
```

### 3. Cache Improvements
```php
// Tagged cache with automatic invalidation
Cache::tags(['users', 'posts'])
    ->remember('key', 3600, function () {
        return User::with('posts')->get();
    });
```

## Security Enhancements

### 1. CSRF Protection
```php
// Enhanced CSRF protection
Route::middleware(['web', 'csrf'])->group(function () {
    Route::post('/update-profile', [ProfileController::class, 'update']);
});
```

### 2. Authentication
```php
// New authentication features
class LoginController extends Controller
{
    public function authenticate(Request $request): RedirectResponse
    {
        $credentials = $request->validate([
            'email' => ['required', 'email'],
            'password' => ['required'],
            '2fa_code' => ['required_if:2fa_enabled,true'],
        ]);

        if (Auth::attempt($credentials)) {
            $request->session()->regenerate();
            return redirect()->intended('dashboard');
        }

        return back()->withErrors([
            'email' => 'Invalid credentials.',
        ]);
    }
}
```

## Development Tips

### 1. Artisan Command Improvements
```bash
# New artisan commands
php artisan model:show User
php artisan route:list --except-vendor
php artisan db:monitor
```

### 2. Testing Enhancements
```php
// New testing features
class UserTest extends TestCase
{
    public function test_user_creation()
    {
        $response = $this->post('/api/users', [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
        ]);

        $response->assertCreated()
            ->assertJsonStructure(['id', 'name', 'email']);
    }
}
```

### 3. Error Handling
```php
// Enhanced error handling
class Handler extends ExceptionHandler
{
    public function register(): void
    {
        $this->reportable(function (CustomException $e) {
            // Custom reporting logic
        })->stop();

        $this->renderable(function (CustomException $e) {
            return response()->json([
                'error' => $e->getMessage()
            ], $e->getCode());
        });
    }
}
```

## Best Practices

### 1. Service Pattern
```php
// Using service classes for business logic
class UserService
{
    public function __construct(
        private readonly UserRepository $repository,
        private readonly EventDispatcher $dispatcher
    ) {}

    public function createUser(array $data): User
    {
        $user = $this->repository->create($data);
        $this->dispatcher->dispatch(new UserCreated($user));
        return $user;
    }
}
```

### 2. Repository Pattern
```php
// Using repositories for data access
class UserRepository
{
    public function __construct(
        private readonly User $model
    ) {}

    public function findByEmail(string $email): ?User
    {
        return $this->model->where('email', $email)->first();
    }
}
```

### 3. Action Classes
```php
// Single responsibility action classes
class CreateUserAction
{
    public function __construct(
        private readonly UserService $service,
        private readonly ValidationService $validator
    ) {}

    public function execute(array $data): User
    {
        $this->validator->validate($data, [
            'name' => 'required|string',
            'email' => 'required|email|unique:users'
        ]);

        return $this->service->createUser($data);
    }
}
```

### 4. Form Requests
```php
// Dedicated form request classes
class CreateUserRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'email', 'unique:users'],
            'password' => ['required', 'min:8', 'confirmed'],
        ];
    }

    public function messages(): array
    {
        return [
            'email.unique' => 'This email is already registered.',
        ];
    }
}
```

### 5. API Resources
```php
// Consistent API responses
class UserResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'created_at' => $this->created_at->toISOString(),
            'posts' => PostResource::collection($this->whenLoaded('posts')),
        ];
    }
}
``` 