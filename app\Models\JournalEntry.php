<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class JournalEntry extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'TID',
        'date',
        'payment_type_id',
        'account_id', // Changed from credit_account_id/debit_account_id
        'is_credit',
        'description',
        'chq_no',
        'inv_no',
        'amount',
        'currency_amount',
        'exchange_rate',
        'formula_type',
        'avg_rate',
        'running_currency_balance',
        'running_pkr_balance',
        'created_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'date' => 'datetime',
        'amount' => 'decimal:2',
    ];

    /**
     * Get the payment type that owns the journal entry.
     */
    public function paymentType(): BelongsTo
    {
        return $this->belongsTo(PaymentType::class);
    }

    /**
     * Get the account associated with the journal entry line.
     */
    public function account(): BelongsTo
    {
        return $this->belongsTo(Account::class, 'account_id');
    }


    /**
     * Get the user that created the journal entry.
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by')->withDefault();
    }
    
    /**
     * Scope a query to find journal entries that are part of sale transactions
     * involving the specified currency account.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $currencyAccountId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSaleTransaction($query, $currencyAccountId)
    {
        return $query->where('TID', function($subquery) use ($currencyAccountId) {
            $subquery->select('TID')
                ->from('journal_entries')
                ->where('account_id', $currencyAccountId)
                ->where('payment_type_id', 11) // Sale
                ->where('is_credit', 1);
        });
    }
} 