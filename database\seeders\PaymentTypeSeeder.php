<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Carbon;

class PaymentTypeSeeder extends Seeder
{
    public function run(): void
    {
        $now = Carbon::now();
        $types = [
            ['name' => 'cust-to-cust', 'label' => 'Cust-to-Cust'],
            ['name' => 'jv-payment', 'label' => 'JV Payment'],
            ['name' => 'bank-payment', 'label' => 'Bank Payment'],
            ['name' => 'bank-receipt', 'label' => 'Bank Receipt'],
            ['name' => 'cash-payment', 'label' => 'Cash Payment'],
            ['name' => 'cash-receipt', 'label' => 'Cash Receipt'],
            ['name' => 'checked', 'label' => 'Checked'],
            ['name' => 'cheque', 'label' => 'Cheque'],
            ['name' => 'cheque_returned', 'label' => 'Cheque Returned'],
            ['name' => 'purchase', 'label' => 'Purchase'],
            ['name' => 'sale', 'label' => 'Sale'],
        ];
        foreach ($types as $type) {
            DB::table('payment_types')->updateOrInsert(
                ['name' => $type['name']],
                [
                    'label' => $type['label'],
                    'created_at' => $now,
                    'updated_at' => $now,
                ]
            );
        }
    }
} 