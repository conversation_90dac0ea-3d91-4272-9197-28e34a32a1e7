<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('cheque_entries', function (Blueprint $table) {
            $table->id();
            $table->enum('entry_type', ['inward', 'outward']);
            $table->date('entry_date');
            $table->date('cheque_date');
            $table->date('posting_date')->nullable();
            $table->foreignId('from_account_id')->constrained('accounts')->cascadeOnDelete();
            $table->foreignId('to_account_id')->constrained('accounts')->cascadeOnDelete();
            $table->foreignId('chq_ref_bank_id')->constrained('chq_ref_banks')->cascadeOnDelete();
            $table->string('chq_no', 100);
            $table->decimal('amount', 18, 2);
            $table->enum('status', ['pending', 'ok', 'returned'])->default('pending');
            $table->timestamps();

            $table->unique(['cheque_date', 'chq_ref_bank_id',  'chq_no', 'amount']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('cheque_entries');
    }
};
