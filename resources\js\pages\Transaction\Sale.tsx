import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import axios from 'axios';
import { NumericFormat, type NumberFormatValues } from 'react-number-format';

import AppLayout from "@/layouts/app-layout";
import { BreadcrumbItem } from "@/types"; 
import { Head, usePage } from "@inertiajs/react"; // Keep usePage for errors

// ShadCN UI Components from Purchase.tsx
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ComboBox } from '@/components/ui/combobox'; // Using ComboBox
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'; // Re-add Select for P&L
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { toast } from 'sonner';
import { CalendarIcon, Loader2, Save } from 'lucide-react'; // Icons

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Dashboard', href: route('dashboard') },
    { title: 'Sale Entry', href: route('sale.create') }, 
];

// Mirrored from Purchase.tsx
const parseFormattedNumber = (value: string | number): number => {
    if (typeof value === 'number') return value;
    if (!value) return 0;
    return parseFloat(String(value).replace(/,/g, '')) || 0;
};

const formatNumber = (value: number | string, options?: Intl.NumberFormatOptions) => {
    const num = typeof value === 'string' ? parseFormattedNumber(value) : value;
    if (isNaN(num)) return '';
    return num.toLocaleString(undefined, options);
};

interface Account {
    id: number;
    name: string;
    currency_code?: string;
    formula?: 'm' | 'd';
}

interface SaleDetailsResponse {
    transaction_number: string; // S-No
    currency_id: number;
    currency_code: string;
    currency_balance: number;
    pkr_balance: number; // PKR value of currency stock
    average_rate: number; // Average cost rate
    formula_type: 'm' | 'd';
    pnl_account_id?: number;
    pnl_account_name?: string;
}

interface SaleFormData {
    date: string;
    selectCr: string; // Currency Account ID (From Account - CREDIT)
    selectDr: string; // Customer Account ID (To Account - DEBIT)
    currency_amount: string;
    rate: string;          // Selling Rate
    pkr_amount: string;    // PKR amount at selling rate (calculated, display)
    avg_rate: string;      // Average rate of the currency being sold (fetched, hidden, sent for validation)
    pnl_account_id: string;   // P&L Account for profit/loss (fetched or selected, sent)
    profit_loss_amount: string; // Calculated profit or loss (display, sent for validation)
    description: string;
    refno: string;
    orderid: string;        // S-NO (fetched, sent)
}

// For submission to backend
interface SaleFormSubmitData {
    date: string;
    selectDr: number | null; // Customer Account ID
    selectCr: number | null; // Currency Account ID
    currency_amount: number;
    rate: number;
    pkr_amount: number;    
    avg_rate: number;      
    pnl_account_id: number | null;
    profit_loss_amount: number; 
    description: string;
    refno: string;
    orderid: string;       
    // is_credit_jama?: '0' | '1'; // For future use
}

const initialFormData: SaleFormData = {
    date: new Date().toISOString().split('T')[0],
    selectDr: '',
    selectCr: '',
    currency_amount: '',
    rate: '',
    pkr_amount: '',
    avg_rate: '',
    pnl_account_id: '',
    profit_loss_amount: '',
    description: '',
    refno: '',
    orderid: '',
};

const SaleEntry: React.FC = () => {
  const { errors: pageErrors, flash } = usePage().props as any; // For displaying submission errors and flash messages

  const { control, handleSubmit, watch, setValue, reset, formState: { errors: formHookErrors }, clearErrors } = useForm<SaleFormData>({
    defaultValues: initialFormData
  });

  const [currencyAccounts, setCurrencyAccounts] = useState<Account[]>([]);
  const [customerAccounts, setCustomerAccounts] = useState<Account[]>([]);
  const [pnlAccounts, setPnlAccounts] = useState<Account[]>([]); 
  
  const [selectedCurrencyDetails, setSelectedCurrencyDetails] = useState<SaleDetailsResponse | null>(null);
  const [isLoadingDetails, setIsLoadingDetails] = useState(false);
  const [isLoadingAccounts, setIsLoadingAccounts] = useState(true); // Start true
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [datePickerOpen, setDatePickerOpen] = useState(false);

  // Fetch initial data: currency accounts, customer accounts, P&L accounts
  useEffect(() => {
    setIsLoadingAccounts(true);
    const fetchAccounts = async () => {
        try {
            const currencyParams = { params: { account_type_id: 5 } }; 
            const customerParams = { params: { exclude_account_type_ids: '4,5,7' } }; 
            const pnlParams = { params: { account_type_name: 'Income'} };

            const [currencyRes, customerRes, pnlRes] = await Promise.all([
                axios.get('/api/accounts', currencyParams),
                axios.get('/api/accounts', customerParams),
                axios.get('/api/accounts', pnlParams) 
            ]);
            setCurrencyAccounts(currencyRes.data || []);
            setCustomerAccounts(customerRes.data || []);
            setPnlAccounts(pnlRes.data || []);
        } catch (error) {
            console.error('Error fetching accounts:', error);
            toast.error('Failed to load accounts.');
        }
        setIsLoadingAccounts(false);
    };
    fetchAccounts();
  }, []);

  const currencyAccountIdFromSelectCr = watch('selectCr');

  useEffect(() => {
    const fetchSaleDetails = async () => {
        if (!currencyAccountIdFromSelectCr) {
            setSelectedCurrencyDetails(null);
            setValue('avg_rate', '');
            setValue('pnl_account_id', '');
            setValue('orderid', '');
            setValue('currency_amount', '');
            setValue('rate', '');
            setValue('pkr_amount', '');
            setValue('profit_loss_amount', '');
            return;
        }
        setIsLoadingDetails(true);
        try {
            const response = await axios.get<SaleDetailsResponse>('/api/sale-entries/get-details', { 
                params: { currency_account_id: currencyAccountIdFromSelectCr }
            });
            const data = response.data;
            setSelectedCurrencyDetails(data);
            setValue('orderid', data.transaction_number); 
            setValue('avg_rate', data.average_rate.toString()); 
            if (data.pnl_account_id) {
                setValue('pnl_account_id', data.pnl_account_id.toString());
            }
        } catch (error) {
            console.error('Error fetching sale entry details:', error);
            setSelectedCurrencyDetails(null);
            toast.error('Failed to fetch S-NO and currency details.');
        }
        setIsLoadingDetails(false);
    };

    if (currencyAccountIdFromSelectCr) fetchSaleDetails();
    else {
        setSelectedCurrencyDetails(null);
        setValue('orderid', '');
        setValue('avg_rate', '');
        setValue('profit_loss_amount', '');
    }
  }, [currencyAccountIdFromSelectCr, setValue]);

  const watchedCurrencyAmount = watch('currency_amount');
  const watchedRate = watch('rate');

  useEffect(() => {
    const ca = parseFormattedNumber(watchedCurrencyAmount);
    const sr = parseFormattedNumber(watchedRate); 
    
    if (!selectedCurrencyDetails || isNaN(ca) || ca <= 0 || isNaN(sr) || sr <= 0) {
      setValue('pkr_amount', '0.00');
      setValue('profit_loss_amount', '0.00');
      return;
    }

    const ar = selectedCurrencyDetails.average_rate; 
    const formula = selectedCurrencyDetails.formula_type ? selectedCurrencyDetails.formula_type.toLowerCase() : 'm';

    if (isNaN(ar)) { 
        setValue('pkr_amount', '0.00');
        setValue('profit_loss_amount', '0.00');
        return;
    }

    let pkrAtSellingRateFloat = 0;
    let pkrAtAverageRateFloat = 0;

    if (formula === 'd') {
      pkrAtSellingRateFloat = sr !== 0 ? ca / sr : 0;
      pkrAtAverageRateFloat = ar !== 0 ? ca / ar : 0;
    } else { 
      pkrAtSellingRateFloat = ca * sr;
      pkrAtAverageRateFloat = ca * ar;
    }
    const profitLossFloat = pkrAtSellingRateFloat - pkrAtAverageRateFloat;

    const pkrAtSellingRateForState = parseFloat(pkrAtSellingRateFloat.toFixed(2));
    const profitLossForState = parseFloat(profitLossFloat.toFixed(2));

    setValue('pkr_amount', formatNumber(pkrAtSellingRateForState, {minimumFractionDigits: 2, maximumFractionDigits: 2}));
    setValue('profit_loss_amount', formatNumber(profitLossForState, {minimumFractionDigits: 2, maximumFractionDigits: 2}));

  }, [watchedCurrencyAmount, watchedRate, selectedCurrencyDetails, setValue]);

  const onSubmit = async (formData: SaleFormData) => {
    clearErrors();
    setIsSubmitting(true);

    const submissionData: SaleFormSubmitData = {
      date: formData.date,
      selectDr: formData.selectDr ? Number(formData.selectDr) : null,
      selectCr: formData.selectCr ? Number(formData.selectCr) : null,
      currency_amount: parseFormattedNumber(formData.currency_amount),
      rate: parseFormattedNumber(formData.rate),
      pkr_amount: parseFormattedNumber(formData.pkr_amount),
      avg_rate: parseFormattedNumber(formData.avg_rate), 
      pnl_account_id: formData.pnl_account_id ? Number(formData.pnl_account_id) : null,
      profit_loss_amount: parseFormattedNumber(formData.profit_loss_amount),
      description: formData.description,
      refno: formData.refno,
      orderid: formData.orderid,
    };
    
    try {
        const response = await axios.post('/api/sale-entries', submissionData); 
        toast.success(response.data.message || 'Sale recorded successfully!');
        reset(initialFormData); 
        setValue('date', new Date().toISOString().split('T')[0]); 
        setSelectedCurrencyDetails(null); 
    } catch (error: any) {
        console.error("Submission error:", error.response?.data);
        const errorsFromServer = error.response?.data?.errors;
        let errorMessage = error.response?.data?.message || 'An unknown error occurred.';

        if (errorsFromServer) {
            Object.keys(errorsFromServer).forEach((key) => {
                // Implement more specific error handling if needed, e.g., using setError from react-hook-form
            });
            const firstKey = Object.keys(errorsFromServer)[0];
            errorMessage = firstKey ? errorsFromServer[firstKey][0] : errorMessage;
        }
        toast.error('Sale entry failed!', { description: errorMessage });
    } finally {
        setIsSubmitting(false);
    }
  };
  
  const handleNumericValueChange = (field: keyof SaleFormData, values: NumberFormatValues) => {
    setValue(field, values.value); 
  };

  const handleAccountChange = (field: 'selectDr' | 'selectCr' | 'pnl_account_id', accountId: string) => {
      setValue(field, accountId);
  };

  const calculateMargin = () => {
    const sr = parseFormattedNumber(watchedRate);
    if (!selectedCurrencyDetails || isNaN(sr) || sr <=0 ) return '0.00';
    const ar = selectedCurrencyDetails.average_rate;
    const formula = selectedCurrencyDetails.formula_type ? selectedCurrencyDetails.formula_type.toLowerCase() : 'm';
    if(isNaN(ar)) return 'N/A';

    let margin = 0;
    if (formula === 'd') {
         margin = ar - sr; 
    } else { 
        margin = sr - ar; 
    }
    return formatNumber(margin, {minimumFractionDigits: 2, maximumFractionDigits: 5});
  };

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Create Sale Entry" />
      <Card className="max-w-4xl mx-auto"> {/* Increased max-width for better layout */}
        <CardHeader>
          <CardTitle>Create Sale Entry</CardTitle>
        </CardHeader>
        <form onSubmit={handleSubmit(onSubmit)}>
        <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
              <div>
                <Label htmlFor="date">Date</Label>
                <Popover open={datePickerOpen} onOpenChange={setDatePickerOpen}>
                    <PopoverTrigger asChild>
                        <Button
                            variant={"outline"}
                            className={`w-full justify-start text-left font-normal ${
                                !watch('date') && "text-muted-foreground" 
                            }`}
                        >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {watch('date') ? new Date(watch('date')!).toLocaleDateString() : <span>Pick a date</span>}
                        </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                        <Calendar
                            mode="single"
                            selected={watch('date') ? new Date(watch('date')!) : undefined}
                            onSelect={(selectedDate) => {
                                if (selectedDate) {
                                    const year = selectedDate.getFullYear();
                                    const month = String(selectedDate.getMonth() + 1).padStart(2, '0');
                                    const day = String(selectedDate.getDate()).padStart(2, '0');
                                    setValue('date', `${year}-${month}-${day}`);
                                } else {
                                    setValue('date', '');
                                }
                                setDatePickerOpen(false);
                            }}
                            initialFocus
                        />
                    </PopoverContent>
                </Popover>
                {formHookErrors.date && <p className="text-red-500 text-xs mt-1">{formHookErrors.date.message}</p>}
              </div>
              <div>
                <Label htmlFor="orderid">S-No (Order ID)</Label>
                <Input id="orderid" value={watch('orderid')} readOnly className="bg-gray-100 dark:bg-gray-800" />
              </div>
              <div /> {/* Spacer */}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="selectDr">Customer Account (Debit)</Label>
                <Controller
                    name="selectDr"
                    control={control}
                    rules={{ required: 'Customer account is required' }}
                    render={({ field }) => (
                        <ComboBox 
                            value={field.value}
                            onChange={(val) => handleAccountChange('selectDr', val)}
                            options={customerAccounts.map(acc => ({
                                label: acc.name,
                                value: String(acc.id)
                            }))}
                            placeholder="Select Customer Account..."
                        />
                    )}
                />
                {formHookErrors.selectDr && <p className="text-red-500 text-xs mt-1">{formHookErrors.selectDr.message}</p>}
              </div>
              <div>
                <Label htmlFor="selectCr">Currency Account (Credit)</Label>
                 <Controller
                    name="selectCr"
                    control={control}
                    rules={{ required: 'Currency account is required' }}
                    render={({ field }) => (
                        <ComboBox 
                            value={field.value}
                            onChange={(val) => handleAccountChange('selectCr', val)}
                            options={currencyAccounts.map(acc => ({
                                label: `${acc.name} ${acc.currency_code ? `(${acc.currency_code})` : ''}`,
                                value: String(acc.id)
                            }))}
                            placeholder="Select Currency Account..."
                        />
                    )}
                />
                {formHookErrors.selectCr && <p className="text-red-500 text-xs mt-1">{formHookErrors.selectCr.message}</p>}
              </div>
            </div>
            
            {selectedCurrencyDetails && (
                <Alert className="bg-sky-50 dark:bg-sky-900 border-sky-200 dark:border-sky-700 text-sky-800 dark:text-sky-100">
                    <AlertTitle className="font-semibold">Currency Details ({selectedCurrencyDetails.currency_code})</AlertTitle>
                    <AlertDescription className="grid grid-cols-2 md:grid-cols-4 gap-x-4 gap-y-1 text-sm">
                        <span>Balance: {formatNumber(selectedCurrencyDetails.currency_balance, {maximumFractionDigits:2})}</span>
                        <span>PKR Value: {formatNumber(selectedCurrencyDetails.pkr_balance, {maximumFractionDigits:2})}</span>
                        <span>Avg. Cost: {formatNumber(selectedCurrencyDetails.average_rate, {minimumFractionDigits:2, maximumFractionDigits: 5})}</span>
                        <span>Formula: {selectedCurrencyDetails.formula_type?.toUpperCase()}</span>
                    </AlertDescription>
                </Alert>
            )}
            { /* Hidden input for avg_rate from API to be submitted for validation */}
            <Controller name="avg_rate" control={control} render={({ field }) => <Input type="hidden" {...field} />} /> 

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <Label htmlFor="currency_amount">Currency Amount ({selectedCurrencyDetails?.currency_code || '...'})</Label>
                    <Controller 
                        name="currency_amount"
                        control={control} 
                        rules={{ required: 'Currency amount is required', validate: val => parseFormattedNumber(val) > 0 || 'Must be > 0'}}
                        render={({ field }) => (
                            <NumericFormat 
                                {...field}
                                id="currency_amount"
                                value={field.value}
                                onValueChange={(values) => handleNumericValueChange('currency_amount', values)}
                                customInput={Input}
                                thousandSeparator={true}
                                allowNegative={false}
                                decimalScale={2} // Or 0 if only whole numbers for currency units
                                placeholder="e.g., 1000"
                                disabled={!selectedCurrencyDetails || isLoadingDetails || isSubmitting}
                             />
                        )}
                    />
                    {formHookErrors.currency_amount && <p className="text-red-500 text-xs mt-1">{formHookErrors.currency_amount.message}</p>}
                </div>
                <div>
                    <Label htmlFor="rate">Selling Rate</Label>
                     <Controller 
                        name="rate"
                        control={control} 
                        rules={{ required: 'Selling rate is required', validate: val => parseFormattedNumber(val) > 0 || 'Must be > 0'}}
                        render={({ field }) => (
                            <NumericFormat 
                                {...field}
                                id="rate"
                                value={field.value}
                                onValueChange={(values) => handleNumericValueChange('rate', values)}
                                customInput={Input}
                                thousandSeparator={true}
                                allowNegative={false}
                                decimalScale={2} // Changed to 2 decimals
                                fixedDecimalScale // Ensure it always shows 2 decimals
                                placeholder="e.g., 275.50" // Adjusted placeholder
                                disabled={!selectedCurrencyDetails || isLoadingDetails || isSubmitting}
                             />
                        )}
                    />
                    {formHookErrors.rate && <p className="text-red-500 text-xs mt-1">{formHookErrors.rate.message}</p>}
                </div>
            </div>

            {watch('rate') && (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-3 bg-blue-50 dark:bg-blue-900/30 rounded items-center">
                     <div>
                        <Label>Margin:</Label> 
                        <Input value={calculateMargin()} readOnly className="bg-gray-100 dark:bg-gray-800 font-semibold"/>
                     </div>          
                     <div>
                        <Label htmlFor="pkr_amount">PKR Amount:</Label>
                        <Controller 
                            name="pkr_amount"
                            control={control} 
                            render={({ field }) => (
                                <NumericFormat 
                                    {...field}
                                    value={field.value}
                                    readOnly 
                                    customInput={Input} 
                                    thousandSeparator={true}
                                    decimalScale={2}
                                    fixedDecimalScale
                                    className="bg-gray-100 dark:bg-gray-800 font-semibold"
                                 />
                            )}
                        />
                     </div>
                     <div>
                        <Label htmlFor="profit_loss_amount">Profit/Loss:</Label>
                        <Controller 
                            name="profit_loss_amount"
                            control={control} 
                            render={({ field }) => (
                                <NumericFormat 
                                    {...field}
                                    value={field.value}
                                    readOnly 
                                    customInput={Input} 
                                    thousandSeparator={true}
                                    decimalScale={2}
                                    fixedDecimalScale
                                    className={`bg-gray-100 dark:bg-gray-800 font-semibold ${parseFormattedNumber(field.value) < 0 ? 'text-red-600' : 'text-green-600'}`}
                                 />
                            )}
                        />
                    </div>
                </div>
            )}

            <div className="hidden">
                <Label htmlFor="pnl_account_id">P&L Account</Label>
                <Controller
                    name="pnl_account_id"
                    control={control}
                    rules={{ required: 'P&L account is required' }}
                    render={({ field }) => (
                        <Select onValueChange={(value) => handleAccountChange('pnl_account_id', value)} value={field.value} disabled={isLoadingAccounts || !selectedCurrencyDetails || isSubmitting}>
                            <SelectTrigger><SelectValue placeholder="Select P&L Account" /></SelectTrigger>
                            <SelectContent>
                                {selectedCurrencyDetails?.pnl_account_id && (
                                    <SelectItem value={selectedCurrencyDetails.pnl_account_id.toString()} >
                                        {selectedCurrencyDetails.pnl_account_name || `Account ID: ${selectedCurrencyDetails.pnl_account_id}`}
                                    </SelectItem>
                                )}
                                {pnlAccounts
                                    .filter(acc => acc.id.toString() !== selectedCurrencyDetails?.pnl_account_id?.toString())
                                    .map(acc => <SelectItem key={acc.id} value={acc.id.toString()}>{acc.name}</SelectItem>)}
                            </SelectContent>
                        </Select>
                    )}
                />
                {formHookErrors.pnl_account_id && <p className="text-red-500 text-xs mt-1">{formHookErrors.pnl_account_id.message}</p>}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <Label htmlFor="refno">Reference No</Label>
                    <Controller name="refno" control={control} render={({ field }) => <Input type="text" {...field} disabled={isSubmitting}/>} />
                </div>
                <div>
                    <Label htmlFor="description">Description</Label>
                    {/* Changed to Input as requested */}
                    <Controller name="description" control={control} render={({ field }) => <Input type="text" {...field} disabled={isSubmitting}/>} /> 
                </div>
            </div>
            
            {/* Display page-level/general submission errors */}
            {pageErrors && typeof pageErrors === 'string' && (
                 <Alert variant="destructive">
                    <AlertTitle>Error</AlertTitle>
                    <AlertDescription>{pageErrors}</AlertDescription>
                </Alert>
            )}
            {pageErrors && typeof pageErrors === 'object' && Object.keys(pageErrors).length > 0 && (
                <Alert variant="destructive">
                    <AlertTitle>Please correct the following errors:</AlertTitle>
                    <AlertDescription>
                        <ul className="list-disc list-inside">
                            {Object.values(pageErrors as Record<string, string[]>).map((errorArray, index) => (
                                errorArray.map((errorMsg, subIndex) => <li key={`${index}-${subIndex}`}>{errorMsg}</li>)
                            ))}
                        </ul>
                    </AlertDescription>
                </Alert>
            )}

        </CardContent>
        <CardFooter className="flex justify-end">
            <Button type="submit" disabled={isLoadingDetails || isSubmitting || !watch('selectCr')}>
                {isSubmitting ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                    <Save className="mr-2 h-4 w-4" />
                )}
                Save Sale Entry
            </Button>
        </CardFooter>
        </form>
      </Card>
    </AppLayout>
  );
};

export default SaleEntry; 
