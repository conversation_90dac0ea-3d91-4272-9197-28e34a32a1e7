import { NavFooter } from '@/components/nav-footer';
import { NavMain } from '@/components/nav-main';
import { NavUser } from '@/components/nav-user';
import { NavCurrency } from '@/components/nav-currency';
import { <PERSON>bar, SidebarContent, SidebarFooter, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { type NavItem } from '@/types';
import { Link } from '@inertiajs/react';
import {
    LayoutDashboard, // Alternative or keep PanelsTopLeftIcon
    Users,
    FileText, // Good for general entries/documents
    BookOpen, // Good for a single journal entry
    Send, // Good for payments/sending money
    Inbox, // Good for receives/receiving money
    FilePlus, // Maybe for multiple entries?
    CheckSquare, // Could represent a cheque
    CreditCard, // Another option for payments/banking
    ShoppingCart, // Good for Purchase Entry
    ShoppingBag, // Good for Sale Entry
    BarChart2, // Good for general reports/charts
    Scale, // Could represent Balance Sheet or accounting
    DollarSign, // Or other currency symbols for Ledgers PKR
    LineChart, // Another option for reports like Income Statement
    ListChecks, // Could represent multiple entries or a ledger list
    Banknote, // Another option for cash/bank entries
    ChevronsRightLeft, // For Cheque Inward/Outward if needed, though Send/Inbox might be clearer
    ClipboardList, // Good for ledgers or reports
    ChartBarIncreasing,
    Folder,
    CalendarDays,
    ScrollText,
    Currency,
    Laptop
} from 'lucide-react';
import AppLogo from './app-logo';

const mainNavItems: NavItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
        // Keep PanelsTopLeftIcon if you like it, or use a more general dashboard icon
        icon: LayoutDashboard, // Suggestion: A common dashboard icon
    },
    {
        title: 'Accounts',
        href: '/accounts',
        // Users is okay if it's about user accounts in the system,
        // but if it's accounting chart of accounts, something else might be better.
        icon: Users, // Assuming user accounts for login/management
        // If Chart of Accounts: icon: Scale or icon: BookOpen
    },
    {
        title: 'Journal Entries',
        // Redo isn't very intuitive for Journal Entries.
        // FileText or BookOpen as a parent icon could work.
        icon: FileText, // Suggestion: Represents a document/entry list
        children: [
            {
                title: 'Journal Entry',
                href: 'journal-entry',
                // BookOpen is a good choice here.
                icon: BookOpen, // Keep: Represents a single entry/record
            },
            {
                title: 'Bank Payment Entry',
                href: 'bank-payment',
                // SendIcon is okay, but maybe something more specific to payment?
                icon: Send, // Keep or use CreditCard or Banknote
            },
            {
                title: 'Bank Receive Entry',
                href: 'bank-receive',
                // Inbox is okay, but maybe something more specific to receiving?
                icon: Inbox, // Keep or use Banknote or ChevronsRightLeft (receiving side)
            },
            {
                title: 'Cash Payment Entry',
                href: 'cash-payment',
                // SendIcon is okay.
                icon: Send, // Keep or use CreditCard or Banknote
            },
            {
                title: 'Cash Receive Entry',
                href: 'cash-receive',
                // Inbox is okay.
                icon: Inbox, // Keep or use Banknote or ChevronsRightLeft (receiving side)
            },
        ],
    },
    {
        title: 'Journal Multiple Entry',
        href: 'journal-multiple-entry',
        // Laptop doesn't seem relevant. Something indicating multiple documents or entry.
        icon: FilePlus, // Suggestion: Adding multiple files/entries
        // Or icon: ListChecks or icon: ClipboardList
    },
    {
        title: 'Cheque Entry',
        // Redo isn't intuitive. Something representing a cheque or banking.
        icon: CheckSquare, // Suggestion: Represents a cheque/check
        // Or icon: Banknote
        children: [
            {
                title: 'Cheque Entry',
                href: 'cheque-entry',
                // SendIcon works, or something indicating direction.
                icon: Send, // Keep or use ChevronsRightLeft (outward)
            },
            {
                title: 'Cheque List',
                href: 'cheque-list',
                
                icon: ClipboardList, 
            },
            {
                title: 'Cheque Multiple Entry',
                href: 'cheque-multiple',
                // Laptop doesn't fit.
                icon: FilePlus, // Suggestion: Adding multiple files/entries (similar to Journal)
                // Or icon: ListChecks or icon: ClipboardList
            },
        ],
    },
    {
        title: 'Purchase / Sale',
        href: 'purchase-sale',
        icon: ShoppingCart,
        children: [
            {
                title: 'Purchase Entry',
                href: 'purchase-entry',
                // ShoppingCart is a good fit.
                icon: ShoppingCart, // Keep
            },
            {
                title: 'Purchase List',
                href: 'purchase-list',
                icon: ClipboardList,
            },
            {
                title: 'Sale Entry',
                href: 'sale-entry',
                // ShoppingBag is a good fit.
                icon: ShoppingBag, // Keep
            },
            {
                title: 'Sale List',
                href: 'sale-list',
                icon: ClipboardList,
            }
        ]
    },
    {
        title: 'All Reports',
        // FileText is okay. Could also use a chart or document icon.
        icon: FileText, // Keep or use BarChart2 or ClipboardList
        children: [
            {
                title: 'Daily Book',
                href: 'daily-book',
                // BarChart2 is okay, but maybe a specific book/list icon?
                icon: CalendarDays, // Suggestion: Represents a list/ledger
                // Or icon: BookOpen
            },
            {
                title: 'Ledgers PKR',
                href: 'ledgers-pkr',
                // BarChart2 is more for charts. Ledgers are typically lists or books.
                icon: ClipboardList, // Suggestion: Represents a list/ledger
                 // Or icon: BookOpen or even DollarSign for PKR
            },
            {
                title: 'Balance Sheet',
                href: 'balance-sheet',
                // BarChart2 is okay if showing a visual balance sheet, but Scale is more symbolic.
                icon: Scale, // Suggestion: Represents balance/equilibrium
                // Or icon: BarChart2 if primarily visual
            },
            {
                title: 'Buying Book',
                href: 'buying-book',
                icon: CalendarDays, // Suggestion: List/Ledger related
                 // Or icon: ShoppingCart (related to buying) + BookOpen/ClipboardList
            },
            {
                title: 'Sale Book',
                href: 'sale-book',
                icon: CalendarDays, // Suggestion: List/Ledger related
                 // Or icon: ShoppingBag (related to selling) + BookOpen/ClipboardList
            },
            {
                title: 'Purchase Ledger',
                href: 'purchase-ledger',
                icon: ShoppingCart, // Suggestion: Ledger related
                 // Or icon: ShoppingCart + ClipboardList
            },
            {
                title: 'Sale Ledger',
                href: 'sale-ledger',
                icon: ShoppingBag, // Suggestion: Ledger related
                 // Or icon: ShoppingBag + ClipboardList
            },
            {
                title: 'CHQ Inward Ledger',
                href: 'chq-inward-ledger',
                icon: ClipboardList, // Suggestion: Ledger related
                 // Or icon: CheckSquare + ClipboardList + Inbox
            },
            {
                title: 'CHQ Outward Ledger',
                href: 'chq-outward-ledger',
                icon: ClipboardList, // Suggestion: Ledger related
                 // Or icon: CheckSquare + ClipboardList + Send
            },
            {
                title: 'Income Statement',
                href: 'income-statement',
                // ChartBarIncreasing is a good fit for an income statement (showing performance).
                icon: ChartBarIncreasing, // Keep or use LineChart
            },
        ],
    },
];

const currencyNavItems: NavItem[] = [
    {
        title: 'IRAN TMN',
        icon: Currency,
        children: [
            {
                title: 'TMN Entry',
                href: 'tmn-entry',
                icon: FileText,
            },
            {
                title: 'TMN Multiple Entry',
                href: 'tmn-multiple-entry',
                icon: Laptop,
            },
            {
                title: 'Daily TMN Book',
                href: 'daily-tmn-book',
                icon: CalendarDays,
            },
            {
                title: 'TMN Ledgers',
                href: 'tmn-ledgers',
                icon: ClipboardList,
            },
            {
                title: 'TMN Balance Sheet',
                href: 'tmn-balance-sheet',
                icon: Scale,
            },
        ],
    },
    {
        title: 'DUBAI DHM',
        icon: Currency,
        children: [
            {
                title: 'DHM Entry',
                href: 'dhm-entry',
                icon: FileText,
            },
            {
                title: 'DHM Multiple Entry',
                href: 'dhm-multiple-entry',
                icon: Laptop,
            },
            {
                title: 'Daily DHM Book',
                href: 'daily-dhm-book',
                icon: CalendarDays,
            },
            {
                title: 'DHM Ledgers',
                href: 'dhm-ledgers',
                icon: ClipboardList,
            },
            {
                title: 'DHM Balance Sheet',
                href: 'dhm-balance-sheet',
                icon: Scale,
            },
        ],
    },
];

const footerNavItems: NavItem[] = [
    
];

export function AppSidebar() {
    return (
        <Sidebar collapsible="icon" variant="inset">
            <SidebarHeader>
                <SidebarMenu>
                    <SidebarMenuItem>
                        <SidebarMenuButton size="lg" asChild>
                            <Link href="/dashboard" prefetch>
                                <AppLogo />
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                </SidebarMenu>
            </SidebarHeader>

            <SidebarContent>
                <NavMain items={mainNavItems} />
                <NavCurrency items={currencyNavItems} />
            </SidebarContent>

            <SidebarFooter>
                <NavFooter items={footerNavItems} className="mt-auto" />
                <NavUser />
            </SidebarFooter>
        </Sidebar>
    );
}
