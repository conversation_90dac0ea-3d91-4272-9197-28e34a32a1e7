<?php

namespace App\Http\Controllers\JournalEntry;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use App\Models\JournalEntry;
use App\Models\PaymentType;
use App\Models\Account;
use Barryvdh\DomPDF\Facade\Pdf; 
use App\Models\Cheque;

class JournalEntryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(): JsonResponse
    {
        $entries = JournalEntry::with(['paymentType', 'account', 'createdBy'])
            ->latest()
            ->paginate(10);

        return response()->json($entries);
    }

    /**
     * Get ledger data for specified account and date range.
     */
    public function getLedgerData(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'fromDate' => 'required|date',
                'tillDate' => 'required|date',
                'accountId' => 'required|integer'
            ]);

            // Get the account for which we're generating the ledger
            $account = Account::findOrFail($request->accountId);

            // Get journal entries with running balance
            $ledgerData = $this->getJournalEntriesWithRunningBalance($request);
            
            // Get pending cheques data
            $pendingCheques = $this->getPendingChequesData($request->accountId, $request->fromDate, $request->tillDate);

            return response()->json([
                'ledgerData' => $ledgerData,
                'pendingCheques' => $pendingCheques,
                'account' => $account
            ]);
        } catch (\Exception $e) {
            \Log::error('Error fetching ledger data: ' . $e->getMessage(), ['exception' => $e]);
            return response()->json(['error' => 'Failed to fetch ledger data'], 500);
        }
    }
    
    private function getJournalEntriesWithRunningBalance($request)
    {
        $ledgerData = JournalEntry::where('account_id', $request->accountId)
            ->whereBetween('date', [$request->fromDate, $request->tillDate])
            ->with(['paymentType', 'account'])
            ->orderBy('date')
            ->orderBy('TID') // Order by TID or another sequential field to ensure correct running balance order
            ->get();

        $initialBalance = JournalEntry::where('account_id', $request->accountId)
        ->where('date', '<', $request->fromDate)
        ->sum('amount');

        $runningBalance = $initialBalance;
        $processedData = [];

        // Initial balance is calculated and used to initialize runningBalance.
        // The explicit "Initial Balance" row is no longer added to processedData.
        // if ($initialBalance != 0) {
        //     $processedData[] = [
        //         'TID' => null, // No transaction ID for initial balance
        //         'date' => \Carbon\Carbon::parse($request->fromDate)->subDay()->format('Y-m-d'), // Day before fromDate
        //         'payment_type' => null, // No payment type
        //         'description' => 'Initial Balance',
        //         'inv_no' => null,
        //         'chq_no' => null,
        //         'amount' => 0, // Initial balance is not a transaction amount
        //         'is_credit' => $initialBalance < 0, // Mark as credit if initial balance is negative
        //         'balance' => $initialBalance, // The initial balance itself
        //         'absolute_balance' => abs($initialBalance), // Absolute value for the second balance
        //     ];
        //      // Note: runningBalance is already initialized with initialBalance, so no need to adjust it here.
        // }

        // Process data to calculate running balance and group by date
        $ledgerData->each(function ($entry) use (&$runningBalance, &$processedData) {
            $runningBalance += $entry->amount;
            $processedData[] = [
                'id' => $entry->id,
                'TID' => $entry->TID,
                'date' => $entry->date->format('Y-m-d'),
                'payment_type' => $entry->paymentType,
                'description' => $entry->description,
                'inv_no' => $entry->inv_no,
                'chq_no' => $entry->chq_no,
                'amount' => $entry->amount,
                'is_credit' => $entry->is_credit,
                'balance' => $runningBalance,
                'absolute_balance' => abs($runningBalance),
            ];
        });

        // Group the processed data by date for the frontend
        $groupedData = collect($processedData)->groupBy('date');

        return response()->json($groupedData);
    }

    /**
     * Get pending cheques data for specified account and date range.
     */
    private function getPendingChequesData($accountId, $fromDate, $tillDate)
    {
        // Get inward pending cheques (where the account is the to_account)
        $inwardCheques = \App\Models\Cheque::where('from_account_id', $accountId)
            ->where('status', 'pending')
            ->where('cheque_date', '<=', $tillDate)
            ->get();
        
        // Get outward pending cheques (where the account is the from_account)
        $outwardCheques = \App\Models\Cheque::where('to_account_id', $accountId)
            ->where('status', 'pending')
            ->where('cheque_date', '<=', $tillDate)
            ->get();

        return [
            'inward' => [
                'count' => $inwardCheques->count(),
                'total_amount' => $inwardCheques->sum('amount'),
                'cheques' => $inwardCheques
            ],
            'outward' => [
                'count' => $outwardCheques->count(),
                'total_amount' => $outwardCheques->sum('amount'),
                'cheques' => $outwardCheques
            ]
        ];
    }

    /**
     * Get Balance Sheet data with filtering options.
     */
    public function getBalanceSheetData(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'fromDate' => 'required|date',
                'tillDate' => 'required|date|after_or_equal:fromDate',
                'accountTypeIds' => 'nullable|array',
                'accountTypeIds.*' => 'integer|exists:account_types,id',
                'balanceType' => 'nullable|string|in:debit,credit',
            ]);

            $fromDate = $request->fromDate;
            $tillDate = $request->tillDate;
            $accountTypeIds = $request->accountTypeIds;
            $balanceType = $request->balanceType;

            // Start building the query
            $query = JournalEntry::select('account_id', DB::raw('SUM(amount) as balance'))
                ->whereBetween('date', [$fromDate, $tillDate]); // Filter by date range

            // Apply account type filter if provided
            if (!empty($accountTypeIds)) {
                $query->join('accounts', 'journal_entries.account_id', '=', 'accounts.id')
                      ->whereIn('accounts.account_type_id', $accountTypeIds);
            }

            // Apply balance type filter if provided
            if ($balanceType === 'debit') {
                $query->where('amount', '>', 0);
            } elseif ($balanceType === 'credit') {
                $query->where('amount', '<', 0);
            }

            // Group by account and get results with account and account type
            $accountBalances = $query->groupBy('account_id')
                                   ->with('account.accountType')
                                   ->get();

            // Structure data grouped by account type
            $groupedData = $accountBalances->groupBy(function ($item) {
                return $item->account->accountType->name ?? 'Unknown Type';
            })->map(function ($accountList, $accountTypeName) {
                $accountDetails = $accountList->map(function ($item) {
                    return [
                        'account_id' => $item->account_id,
                        'account_name' => $item->account->name ?? 'Unknown Account',
                        'balance' => $item->balance,
                    ];
                });

                $accountTypeTotal = $accountList->sum('balance');

                return [
                    'account_type' => $accountTypeName,
                    'accounts' => $accountDetails,
                    'total_balance' => $accountTypeTotal,
                ];
            })->values(); // Reset keys to be a simple array

            // Calculate overall total balance
            $overallTotal = $accountBalances->sum('balance');

            return response()->json([
                'grouped_data' => $groupedData,
                'overall_total' => $overallTotal,
            ]);

        } catch (\Exception $e) {
            // Log the exception for debugging
            \Log::error('Error fetching balance sheet data: ' . $e->getMessage(), ['exception' => $e]);

            // Return a JSON response with the error message
            return response()->json(['error' => 'An error occurred while fetching balance sheet data.', 'details' => $e->getMessage()], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        // Only allow Admin or Editor
        if (!$request->user() || !in_array($request->user()->role, ['Admin', 'Editor'])) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validated = $request->validate([
            'date' => 'required|date',
            'payment_type_id' => 'required|exists:payment_types,id',
            'credit_account_id' => 'required|exists:accounts,id',
            'debit_account_id' => 'required|exists:accounts,id',
            'description' => 'nullable|string|max:255',
            'chq_no' => 'nullable|string|max:50',
            'amount' => 'required|numeric|min:0',
        ]);
        // Keep credit_account_id and debit_account_id in validated for narration generation

        $userId = $request->user()->id;
        $paymentType = PaymentType::findOrFail($validated['payment_type_id']);
        $debitAccount = Account::findOrFail($validated['debit_account_id']);
        $creditAccount = Account::findOrFail($validated['credit_account_id']);

        $debitNarration = '';
        $creditNarration = '';
        $transactionNumber = '';

        DB::transaction(function () use ($validated, $userId, $paymentType, $debitAccount, $creditAccount, &$debitNarration, &$creditNarration, &$transactionNumber) {
            // Lock the last entry row for update to prevent race conditions
            $lastEntry = JournalEntry::lockForUpdate()
                ->latest()
                ->first();

            $lastSequence = 0;
            if ($lastEntry) {
                $lastTID = $lastEntry->TID;
                // Extract numeric sequence from last TID, ignoring date prefix
                $lastSequence = (int)substr($lastTID, -4);
            }

            $newSequence = $lastSequence + 1;

            // Loop to ensure unique TID (in case of race conditions)
            do {
                $transactionNumber = $this->generateTransactionNumber($newSequence);
                $exists = JournalEntry::where('TID', $transactionNumber)->exists();
                if ($exists) {
                    $newSequence++;
                } else {
                    break;
                }
            } while (true);

            // Generate narrations
            $desc = $validated['description'] ?? '';
            $debitNarration = $this->generateNarration($paymentType, $debitAccount, $creditAccount, $desc, 'debit');
            $creditNarration = $this->generateNarration($paymentType, $debitAccount, $creditAccount, $desc, 'credit');

            // Create credit entry
            JournalEntry::create([
                'date' => $validated['date'],
                'payment_type_id' => $validated['payment_type_id'],
                'TID' => $transactionNumber,
                'account_id' => $validated['credit_account_id'], // Use credit account for credit entry
                'description' => $creditNarration, // Use generated narration
                'chq_no' => $validated['chq_no'],
                'amount' => -$validated['amount'], // Credit amount is negative in this format
                'is_credit' => true, // Mark as credit
                'created_by' => $userId,
            ]);

            // Create debit entry
            JournalEntry::create([
                'date' => $validated['date'],
                'payment_type_id' => $validated['payment_type_id'],
                'TID' => $transactionNumber,
                'account_id' => $validated['debit_account_id'], // Use debit account for debit entry
                'description' => $debitNarration, // Use generated narration
                'chq_no' => $validated['chq_no'],
                'amount' => $validated['amount'], // Debit amount is positive
                'is_credit' => false, // Mark as debit
                'created_by' => $userId,
            ]);
        });

        return response()->json([
            'message' => 'Journal entry created successfully',
            'TID' => $transactionNumber,
            'debit_entry' => JournalEntry::where('TID', $transactionNumber)->where('is_credit', false)->first(),
            'credit_entry' => JournalEntry::where('TID', $transactionNumber)->where('is_credit', true)->first(),
        ], 201);
    }

    /**
     * Store multiple entries in a single transaction.
     */
    public function storeMultiple(Request $request): JsonResponse
    {
        // Only allow Admin or Editor
        if (!$request->user() || !in_array($request->user()->role, ['Admin', 'Editor'])) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validated = $request->validate([
            'date' => 'required|date',
            'payment_type_id' => 'required|exists:payment_types,id',
            'entries' => 'required|array|min:1',
            'entries.*.credit_account_id' => 'required|exists:accounts,id',
            'entries.*.debit_account_id' => 'required|exists:accounts,id',
            'entries.*.description' => 'nullable|string|max:255',
            'entries.*.chq_no' => 'nullable|string|max:50',
            'entries.*.amount' => 'required|numeric|min:0',
        ]);

        $userId = $request->user()->id;
        $journalEntriesToInsert = [];

        // Fetch payment type once
        $paymentType = PaymentType::findOrFail($validated['payment_type_id']);

        DB::transaction(function () use ($validated, $userId, $paymentType, &$journalEntriesToInsert) {
            // Lock the last entry row for update to prevent race conditions
            $lastEntry = JournalEntry::lockForUpdate()
                ->latest()
                ->first();

            $lastSequence = 0;
            if ($lastEntry) {
                $lastTID = $lastEntry->TID;
                // Extract numeric sequence from last TID, ignoring date prefix
                $lastSequence = (int)substr($lastTID, -4);
            }

            $newSequence = $lastSequence;

            // Pre-generate all transaction numbers to minimize DB queries
            $transactionNumbers = [];
            $currentSequence = $newSequence;
            foreach ($validated['entries'] as $entry) {
                $currentSequence++;
                $transactionNumbers[] = $this->generateTransactionNumber($currentSequence);
            }

            // Check for existing TIDs in bulk to avoid duplicates
            $existingTIDs = JournalEntry::whereIn('TID', $transactionNumbers)->pluck('TID')->toArray();

            // Adjust sequences if duplicates found
            foreach ($transactionNumbers as &$tid) {
                while (in_array($tid, $existingTIDs)) {
                    $currentSequence++;
                    $tid = $this->generateTransactionNumber($currentSequence);
                }
                $existingTIDs[] = $tid; // Add to existing to prevent duplicates in this batch
            }
            unset($tid); // break reference

            // Now assign transaction numbers to entries
            $index = 0;
            foreach ($validated['entries'] as $entry) {
                $transactionNumber = $transactionNumbers[$index];
                $index++;

                // Fetch related models for narration generation for the current entry
                $debitAccount = Account::findOrFail($entry['debit_account_id']);
                $creditAccount = Account::findOrFail($entry['credit_account_id']);

                // Generate narrations for the current entry
                $desc = $entry['description'] ?? '';
                $debitNarration = $this->generateNarration($paymentType, $debitAccount, $creditAccount, $desc, 'debit');
                $creditNarration = $this->generateNarration($paymentType, $debitAccount, $creditAccount, $desc, 'credit');

                // Create credit entry
                $journalEntriesToInsert[] = [
                    'date' => $validated['date'],
                    'payment_type_id' => $validated['payment_type_id'],
                    'TID' => $transactionNumber,
                    'account_id' => $entry['credit_account_id'], // Use credit account for credit entry
                    'description' => $creditNarration, // Use generated narration
                    'chq_no' => $entry['chq_no'],
                    'amount' => -$entry['amount'], // Credit amount is negative
                    'is_credit' => true, // Mark as credit
                    'created_by' => $userId,
                    'created_at' => now(), // Add timestamps for bulk insert
                ];

                // Create debit entry
                $journalEntriesToInsert[] = [
                    'date' => $validated['date'],
                    'payment_type_id' => $validated['payment_type_id'],
                    'TID' => $transactionNumber,
                    'account_id' => $entry['debit_account_id'], // Use debit account for debit entry
                    'description' => $debitNarration, // Use generated narration
                    'chq_no' => $entry['chq_no'],
                    'amount' => $entry['amount'], // Debit amount is positive
                    'is_credit' => false, // Mark as debit
                    'created_by' => $userId,
                    'created_at' => now(), // Add timestamps for bulk insert
                ];
            }

            // Insert all collected entries
            JournalEntry::insert($journalEntriesToInsert);
        });


        $entriesCreatedCount = count($validated['entries']) * 2;
        return response()->json([
            'message' => 'Journal entries created successfully',
            'entries_created_count' => $entriesCreatedCount,
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(JournalEntry $journalEntry): JsonResponse
    {
        // Get the paired entry with the same TID
        $pairedEntry = JournalEntry::where('TID', $journalEntry->TID)
            ->where('id', '!=', $journalEntry->id)
            ->with(['paymentType', 'account', 'createdBy'])
            ->first();

        if (!$pairedEntry) {
            return response()->json([
                'message' => 'Paired entry not found',
                'entry' => $journalEntry->load(['paymentType', 'account', 'createdBy'])
            ], 404);
        }

        // Strip narration from description for both entries
        $stripNarration = function ($description, $paymentTypeName, $type) {
            // This function inverses the generateNarration logic to extract the original description
            // from the generated narration string.

            switch ($paymentTypeName) {
                case 'cust-to-cust':
                    if ($type === 'credit') {
                        // narration: "online paid in {description} deposit to {debitAccount->name}"
                        if (preg_match('/^online paid in (.*) deposit to /', $description, $matches)) {
                            return $matches[1];
                        }
                    } elseif ($type === 'debit') {
                        // narration: "online deposit in {description} by {creditAccount->name}"
                        if (preg_match('/^online deposit in (.*) by /', $description, $matches)) {
                            return $matches[1];
                        }
                    }
                    break;
                case 'jv-payment':
                    if ($type === 'credit') {
                        // narration: "Received {description} from {debitAccount->name}"
                        if (preg_match('/^Received (.*) from /', $description, $matches)) {
                            return $matches[1];
                        }
                    } elseif ($type === 'debit') {
                        // narration: "Transfered {description} to {creditAccount->name}"
                        if (preg_match('/^Transfered (.*) to /', $description, $matches)) {
                            return $matches[1];
                        }
                    }
                    break;
                case 'bank-payment':
                    if ($type === 'credit') {
                        // narration: "Bank payment {description} to {debitAccount->name}"
                        if (preg_match('/^Bank payment (.*) to /', $description, $matches)) {
                            return $matches[1];
                        }
                    } elseif ($type === 'debit') {
                        // narration: "Bank payment {description} from {creditAccount->name}"
                        if (preg_match('/^Bank payment (.*) from /', $description, $matches)) {
                            return $matches[1];
                        }
                    }
                    break;
                case 'bank-receipt':
                    if ($type === 'credit') {
                        // narration: "Bank receipt {description} from {debitAccount->name}"
                        if (preg_match('/^Bank receipt (.*) from /', $description, $matches)) {
                            return $matches[1];
                        }
                    } elseif ($type === 'debit') {
                        // narration: "Bank receipt {description} to {creditAccount->name}"
                        if (preg_match('/^Bank receipt (.*) to /', $description, $matches)) {
                            return $matches[1];
                        }
                    }
                    break;
                case 'cash-payment':
                    if ($type === 'credit') {
                        // narration: "Cash payment {description} to {debitAccount->name}"
                        if (preg_match('/^Cash payment (.*) to /', $description, $matches)) {
                            return $matches[1];
                        }
                    } elseif ($type === 'debit') {
                        // narration: "Cash payment {description} from {creditAccount->name}"
                        if (preg_match('/^Cash payment (.*) from /', $description, $matches)) {
                            return $matches[1];
                        }
                    }
                    break;
                case 'cash-receipt':
                    if ($type === 'credit') {
                        // narration: "Cash receipt {description} from {debitAccount->name}"
                        if (preg_match('/^Cash receipt (.*) from /', $description, $matches)) {
                            return $matches[1];
                        }
                    } elseif ($type === 'debit') {
                        // narration: "Cash receipt {description} to {creditAccount->name}"
                        if (preg_match('/^Cash receipt (.*) to /', $description, $matches)) {
                            return $matches[1];
                        }
                    }
                    break;
                case 'checked':
                    if ($type === 'credit') {
                        // narration: "The Account Balance is OK {description}."
                        if (preg_match('/^The Account Balance is OK (.*)\.$/', $description, $matches)) {
                            return $matches[1];
                        }
                    } elseif ($type === 'debit') {
                        // narration: "The Account Balance is {description} of {creditAccount->name}."
                        if (preg_match('/^The Account Balance is (.*) of /', $description, $matches)) {
                            return $matches[1];
                        }
                    }
                    break;
                case 'purchase':
                    if ($type === 'credit') {
                        // narration: "Purchase {description} from {debitAccount->name}"
                        if (preg_match('/^Purchase (.*) from /', $description, $matches)) {
                            return $matches[1];
                        }
                    } elseif ($type === 'debit') {
                        // narration: "Purchase {description} to {creditAccount->name}"
                        if (preg_match('/^Purchase (.*) to /', $description, $matches)) {
                            return $matches[1];
                        }
                    }
                    break;
                case 'sale':
                    if ($type === 'credit') {
                        // narration: "Sales {description} from {debitAccount->name}"
                        if (preg_match('/^Sales (.*) from /', $description, $matches)) {
                            return $matches[1];
                        }
                    } elseif ($type === 'debit') {
                        // narration: "Sales {description} to {creditAccount->name}"
                        if (preg_match('/^Sales (.*) to /', $description, $matches)) {
                            return $matches[1];
                        }
                    }
                    break;
                default:
                    // Default fallback: return original description
                    return $description;
            }

            // Default fallback: return original description
            return $description;
        };

        $entryDescription = $stripNarration($journalEntry->description, $journalEntry->paymentType->name, $journalEntry->is_credit ? 'credit' : 'debit');
        $pairedDescription = $stripNarration($pairedEntry->description, $pairedEntry->paymentType->name, $pairedEntry->is_credit ? 'credit' : 'debit');

        // Clone entries to avoid modifying original models
        $entryClone = $journalEntry->replicate();
        $entryClone->description = $entryDescription;

        $pairedClone = $pairedEntry->replicate();
        $pairedClone->description = $pairedDescription;

        return response()->json([
            'entry' => $entryClone->load(['paymentType', 'account', 'createdBy']),
            'paired_entry' => $pairedClone->load(['paymentType', 'account', 'createdBy'])
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id): JsonResponse
    {
        // Only allow Admin or Editor
        if (!$request->user() || !in_array($request->user()->role, ['Admin', 'Editor'])) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validated = $request->validate([
            'date' => 'required|date',
            'payment_type_id' => 'required|exists:payment_types,id',
            'credit_account_id' => 'required|exists:accounts,id',
            'debit_account_id' => 'required|exists:accounts,id',
            'description' => 'nullable|string|max:255',
            'chq_no' => 'nullable|string|max:50',
            'amount' => 'required|numeric|min:0',
        ]);

        $userId = $request->user()->id;
        $paymentType = PaymentType::findOrFail($validated['payment_type_id']);
        $debitAccount = Account::findOrFail($validated['debit_account_id']);
        $creditAccount = Account::findOrFail($validated['credit_account_id']);

        DB::transaction(function () use ($validated, $userId, $paymentType, $debitAccount, $creditAccount, $id) {
            // Get both entries
            $entries = JournalEntry::where('TID', function($query) use ($id) {
                $query->select('TID')->from('journal_entries')->where('id', $id);
            })->get();

            if ($entries->count() !== 2) {
                throw new \Exception('Invalid journal entry pair');
            }

            // Generate new narrations
            $desc = $validated['description'] ?? '';
            $debitNarration = $this->generateNarration($paymentType, $debitAccount, $creditAccount, $desc, 'debit');
            $creditNarration = $this->generateNarration($paymentType, $debitAccount, $creditAccount, $desc, 'credit');

            // Update both entries
            foreach ($entries as $entry) {
                $updateData = [
                    'date' => $validated['date'],
                    'payment_type_id' => $validated['payment_type_id'],
                    'chq_no' => $validated['chq_no'],
                    'amount' => $entry->is_credit ? -$validated['amount'] : $validated['amount'],
                    'updated_at' => now(),
                ];

                if ($entry->is_credit) {
                    $updateData['account_id'] = $validated['credit_account_id'];
                    $updateData['description'] = $creditNarration;
                } else {
                    $updateData['account_id'] = $validated['debit_account_id'];
                    $updateData['description'] = $debitNarration;
                }

                $entry->update($updateData);
            }
        });

        return response()->json([
            'message' => 'Journal entry updated successfully',
        ]);
    }


    /**
     * Remove the specified resource from storage.
     */
    public function destroy(JournalEntry $journalEntry): JsonResponse
    {
        $journalEntry->delete();
        return response()->json(null, 204);
    }

    /**
     * Get the last sequence number for today's transactions.
     */
    private function getLastSequenceNumberForToday(): int
    {
        $lastEntry = JournalEntry::whereDate('created_at', today())
            ->latest()
            ->first();

        return $lastEntry ? (int)substr($lastEntry->TID, -4) : 0;
    }

    /**
     * Generate a unique transaction number based on a sequence number.
     */
    private function generateTransactionNumber(int $sequence): string
    {
        // Remove 'JE' prefix
        // Use last two digits of year and month only, no day
        $date = now()->format('ym'); // 'y' = two digit year, 'm' = month with leading zero

        return $date . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Generate dynamic narration based on payment type and accounts.
     */
    private function generateNarration(PaymentType $paymentType, Account $debitAccount, Account $creditAccount, string $description, string $type): string
    {
        $narration = '';
        // Example narration logic based on payment type name
        switch ($paymentType->name) {
            case 'cust-to-cust':
                if ($type === 'credit') {
                    $narration = "online paid in {$description} deposit to {$debitAccount->name}";
                } elseif ($type === 'debit') {
                    $narration = "online deposit in {$description} by {$creditAccount->name}";
                }
                break;
            // Add cases for other payment types here
            case 'jv-payment':
                if ($type === 'credit') {
                    $narration = "Received {$description} from {$debitAccount->name}";
                } elseif ($type === 'debit') {
                    $narration = "Transfered {$description} to {$creditAccount->name}";
                }
                break;
            case 'bank-payment':
                if ($type === 'credit') {
                    $narration = "Bank payment {$description} to {$debitAccount->name}";
                } elseif ($type === 'debit') {
                    $narration = "Bank payment {$description} from {$creditAccount->name}";
                }
                break;
            case 'bank-receipt':
                if ($type === 'credit') {
                    $narration = "Bank receipt {$description} from {$debitAccount->name}";
                } elseif ($type === 'debit') {
                    $narration = "Bank receipt {$description} to {$creditAccount->name}";
                }
                break;
            case 'cash-payment':
                if ($type === 'credit') {
                    $narration = "Cash payment {$description} to {$debitAccount->name}";
                } elseif ($type === 'debit') {
                    $narration = "Cash payment {$description} from {$creditAccount->name}";
                }
                break;
            case 'cash-receipt':
                if ($type === 'credit') {
                    $narration = "Cash receipt {$description} from {$debitAccount->name}";
                } elseif ($type === 'debit') {
                    $narration = "Cash receipt {$description} to {$creditAccount->name}";
                }
                break;
            case 'checked':
                if ($type === 'credit') {
                    $narration = "The Account Balance is OK {$description}.";
                } elseif ($type === 'debit') {
                    $narration = "The Account Balance is {$description} of {$creditAccount->name}.";
                }
                break;
            case 'purchase':
                if ($type === 'credit') {
                    $narration = "Purchase {$description} from {$debitAccount->name}";
                } elseif ($type === 'debit') {
                    $narration = "Purchase {$description} to {$creditAccount->name}";
                }
                break;
            case 'sale':
                if ($type === 'credit') {
                    $narration = "Sales {$description} from {$debitAccount->name}";
                } elseif ($type === 'debit') {
                    $narration = "Sales {$description} to {$creditAccount->name}";
                }
                break;
            default:
                // Default narration if no specific template is found
                if ($type === 'debit') {
                    $narration = "Debit transaction: {$description} (from {$debitAccount->name} to {$creditAccount->name})";
                } elseif ($type === 'credit') {
                    $narration = "Credit transaction: {$description} (from {$debitAccount->name} to {$creditAccount->name})";
                }
                break;
        }

        return $narration;
    }

    /**
     * Fetch journal entries filtered by date and grouped by transaction.
     */
    public function getEntriesByDate(Request $request): JsonResponse
    {
        // Only allow Admin or Editor
        if (!$request->user() || !in_array($request->user()->role, ['Admin', 'Editor'])) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $request->validate([
            'date' => 'required|date_format:Y-m-d',
        ]);

        $date = $request->query('date');

        // Fetch all entries for the date and eager load necessary relationships
        $entries = JournalEntry::with(['paymentType', 'account', 'createdBy'])
            ->whereDate('date', $date)
            ->orderBy('created_at') // Order by creation date for chronological display
            ->get()
            ->map(function ($entry) {
                $entry->created_by_user = $entry->createdBy; // Add this for frontend compatibility
                return $entry;
            });

        // Group entries by TID to form transactions
        // COMMENTING OUT GROUPING LOGIC TO RETURN INDIVIDUAL ENTRIES FOR DAILY BOOK REPORT
        /*
        $transactions = $entries->groupBy('TID')->map(function ($transactionEntries) {
            $debitEntry = $transactionEntries->firstWhere('is_credit', false);
            $creditEntry = $transactionEntries->firstWhere('is_credit', true);

            // Ensure both debit and credit entries exist for a complete transaction
            if (!$debitEntry || !$creditEntry) {
                // Log or handle incomplete transactions if necessary
                return null;
            }

            // Construct the transaction object for the frontend
            return [
                'id' => $debitEntry->id, // Using debit entry ID as transaction ID for now
                'date' => $debitEntry->date,
                'TID' => $debitEntry->TID,
                // account_id is not directly used in frontend transaction structure
                'account_id' => null,
                'description' => $debitEntry->description, // Assuming debit description is the main one
                'chq_no' => $debitEntry->chq_no ?? $creditEntry->chq_no,
                'inv_no' => $debitEntry->inv_no ?? $creditEntry->inv_no,
                'amount' => $debitEntry->amount, // Amount should be the same positive value
                'is_credit' => null, // Not applicable at transaction level
                'created_by' => $debitEntry->created_by,
                'payment_type_id' => $debitEntry->payment_type_id,
                'created_at' => $debitEntry->created_at,
                'updated_at' => $debitEntry->updated_at,
                'payment_type' => $debitEntry->paymentType,
                'credit_account' => $creditEntry->account, // Eager loaded account model
                'debit_account' => $debitEntry->account,   // Eager loaded account model
                'created_by_user' => $debitEntry->createdBy,
            ];
        })->filter()->values(); // Filter out nulls (incomplete transactions) and reset keys
        */

        // Return the flat collection of journal entries
        return response()->json($entries);
    }

    /**
     * Generate PDF for ledger data.
     */
    public function generateLedgerPdf(Request $request)
    {
        $user = $request->user();
        if (!$user || !in_array($user->role, ['Admin', 'Editor'])) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        try {
            $validated = $request->validate([
                'fromDate' => 'required|date',
                'tillDate' => 'required|date|after_or_equal:fromDate',
                'accountId' => 'required|integer|exists:accounts,id',
                'withPartyName' => 'required|in:"true","false"', // Accept "true" or "false" strings
            ]);

            $fromDate = $validated['fromDate'];
            $tillDate = $validated['tillDate'];
            $accountId = $validated['accountId'];
            $withPartyName = filter_var($validated['withPartyName'], FILTER_VALIDATE_BOOLEAN);

            $account = Account::findOrFail($accountId);

            // Fetch ledger data (similar to getLedgerData but adapted for PDF)
            $initialBalance = JournalEntry::where('account_id', $accountId)
                ->where('date', '<', $fromDate)
                ->sum('amount');

            $ledgerEntries = JournalEntry::where('account_id', $accountId)
                ->whereBetween('date', [$fromDate, $tillDate])
                ->with(['paymentType']) // Eager load paymentType
                ->orderBy('date')
                ->orderBy('TID')
                ->get();

            $runningBalance = $initialBalance;
            $processedEntries = [];

            // Remove opening balance row but keep initialBalance for running balance calculation
            // Group entries by date with date header rows
            $groupedEntries = [];

            foreach ($ledgerEntries as $entry) {
                $runningBalance += $entry->amount;
                $descriptionToUse = $entry->description; // Default to full description
                if (!$withPartyName && $entry->paymentType) {
                    // If withPartyName is false and paymentType exists, strip the narration
                    $descriptionToUse = $this->stripNarrationFromDescription(
                        $entry->description,
                        $entry->paymentType->name,
                        $entry->is_credit
                    );
                }

                $entryObj = (object)[
                    'date' => $entry->date->format('Y-m-d'),
                    'TID' => $entry->TID,
                    'payment_type_name' => $entry->paymentType->name ?? '',
                    'description' => $descriptionToUse, // Use the potentially stripped description
                    'inv_no' => $entry->inv_no,
                    'chq_no' => $entry->chq_no,
                    'debit' => !$entry->is_credit && $entry->amount > 0 ? $entry->amount : 0,
                    'credit' => $entry->is_credit && $entry->amount < 0 ? abs($entry->amount) : 0,
                    'balance' => $runningBalance,
                    'is_opening_balance' => false,
                ];

                // Add date header if not already added for this date
                if (!isset($groupedEntries[$entryObj->date])) {
                    $groupedEntries[$entryObj->date] = [
                        (object)[
                            'is_date_header' => true,
                            'date' => $entryObj->date,
                        ]
                    ];
                }
                $groupedEntries[$entryObj->date][] = $entryObj;
            }

            // Flatten grouped entries preserving order
            foreach ($groupedEntries as $dateGroup) {
                foreach ($dateGroup as $item) {
                    $processedEntries[] = $item;
                }
            }

            // Calculate totals similar to the React component
            $totalDr = 0;
            $totalCr = 0;
            $balance = 0;
            
            // Get the last entry for final balance
            $lastEntry = collect($processedEntries)
                ->filter(function($entry) {
                    return !isset($entry->is_date_header) && isset($entry->balance);
                })
                ->last();
                
            $balance = $lastEntry ? $lastEntry->balance : $initialBalance;
            
            // Calculate total debits and credits
            foreach ($processedEntries as $entry) {
                if (!isset($entry->is_date_header)) {
                    if ($entry->debit > 0) {
                        $totalDr += $entry->debit;
                    }
                    if ($entry->credit > 0) {
                        $totalCr += $entry->credit;
                    }
                }
            }
            
            // Get pending cheques
            $pendingCheques = $this->getPendingCheques($accountId);
            
            // Calculate cheque totals
            $chequeInwardTotal = -($pendingCheques['inward']['total_amount'] ?? 0);
            $chequeOutwardTotal = $pendingCheques['outward']['total_amount'] ?? 0;
            
            // Calculate final total
            $finalTotal = $balance + $chequeInwardTotal + $chequeOutwardTotal;
            
            // Prepare totals array
            $totals = [
                'totalDr' => $totalDr,
                'totalCr' => $totalCr,
                'balance' => $balance,
                'chequeInwardTotal' => $chequeInwardTotal,
                'chequeOutwardTotal' => $chequeOutwardTotal,
                'finalTotal' => $finalTotal,
                'chequeInwardCount' => $pendingCheques['inward']['count'] ?? 0,
                'chequeOutwardCount' => $pendingCheques['outward']['count'] ?? 0
            ];

            $companyName = config('app.name', 'AccSystem'); // Get company name from config or default

            $data = [
                'ledgerEntries' => $processedEntries,
                'accountName' => $account->name,
                'fromDate' => \Carbon\Carbon::parse($fromDate)->format('d-M-Y'),
                'tillDate' => \Carbon\Carbon::parse($tillDate)->format('d-M-Y'),
                'withPartyName' => $withPartyName,
                'companyName' => $companyName,
                'reportDate' => now()->format('d-M-Y H:i:s'),
                'totals' => $totals,
                'watermark' => true, // Enable watermark for PDF
            ];

            $pdf = Pdf::loadView('pdf.ledger_pkr', $data);
            
            // Option to set paper size and orientation
            // $pdf->setPaper('a4', 'landscape');

            return $pdf->stream('ledger_report_' . $account->name . '_' . time() . '.pdf');

        } catch (\Illuminate\Validation\ValidationException $e) {
            // Log validation errors or return them appropriately
            \Log::error('PDF Generation Validation Error: ' . $e->getMessage(), ['errors' => $e->errors()]);
            // You might want to redirect back with errors or show an error page
            return response()->json(['error' => 'Validation failed.', 'details' => $e->errors()], 422);
        } catch (\Exception $e) {
            \Log::error('Error generating ledger PDF: ' . $e->getMessage(), ['exception' => $e]);
            // Return a user-friendly error page or response
            // For now, returning a JSON error, but a redirect or error view might be better for web route
            return response()->json(['error' => 'An error occurred while generating the PDF.', 'details' => $e->getMessage()], 500);
        }
    }

    /**
     * Strip party names from journal entry description based on payment type and entry type.
     * This is an adaptation of the logic found in the show() method's $stripNarration closure.
     *
     * @param string $description The full narration string.
     * @param string $paymentTypeName The name of the payment type (e.g., 'online', 'cash').
     * @param bool $isCredit True if the entry is a credit, false if debit.
     * @return string The stripped description or the original if no pattern matches.
     */
    private function stripNarrationFromDescription(string $description, string $paymentTypeName, bool $isCredit): string
    {
        $type = $isCredit ? 'credit' : 'debit';
        // Normalize paymentTypeName to match keys in $patterns (e.g., 'bank-payment' -> 'bank_payment')
        $normalizedPaymentTypeName = strtolower(str_replace('-', '_', $paymentTypeName));

        // Define patterns for different payment types and entry types
        // These patterns aim to extract the user-entered part of the description.
        $patterns = [
            // Specific patterns from the show() method, adapted and prioritized
            'cust_to_cust' => [ // often 'online'
                // Modified to capture prefix and description separately for custom output
                'credit' => '/^(online paid in )(.*?) deposit to .*/i',
                'debit'  => '/^(online deposit in )(.*?) by .*/i',      // This pattern remains as is, returning only description
            ],
            'jv_payment' => [
                'credit' => '/^(Received )(.*?) from .*/i',
                'debit'  => '/^(Transfered )(.*?) to .*/i',
            ],
            'bank_payment' => [
                'credit' => '/^(Bank payment )(.*?) to .*/i',
                'debit'  => '/^(Bank payment )(.*?) from .*/i',
            ],
            'bank_receipt' => [
                'credit' => '/^(Bank receipt )(.*?) from .*/i',
                'debit'  => '/^(Bank receipt )(.*?) to .*/i',
            ],
            'cash_payment' => [
                'credit' => '/^(Cash payment )(.*?) to .*/i',
                'debit'  => '/^(Cash payment )(.*?) from .*/i',
            ],
            'cash_receipt' => [
                'credit' => '/^(Cash receipt )(.*?) from .*/i',
                'debit'  => '/^(Cash receipt )(.*?) to .*/i',
            ],
            'purchase' => [
                'credit' => '/^(Purchase )(.*?) from .*/i',
                'debit'  => '/^(Purchase )(.*?) to .*/i',
            ],
            'sale' => [
                'credit' => '/^(Sales )(.*?) from .*/i',
                'debit'  => '/^(Sales )(.*?) to .*/i',
            ],
            'checked' => [ // From show() method
                'credit' => '/^(The Account Balance is OK )(.*)\.$/i',
                'debit'  => '/^(The Account Balance is )(.*) of .*/i',
            ],
            // General patterns for common payment types if specific ones don't match
            // These are broader and should catch common narration structures.
            'cheque' => [
                'credit' => '/^cheque (?:received|credited|deposited) (?:in|as)?\s*(.*?)\s*(?:from|by|deposit from)\s*.*/i',
                'debit'  => '/^cheque (?:paid|debited|transferred) (?:in|as)?\s*(.*?)\s*(?:to|deposit to)\s*.*/i',
            ],
        ];

        // Attempt to find a pattern for the specific normalized payment type name
        $pattern = $patterns[$normalizedPaymentTypeName][$type] ?? null;

        if ($pattern && preg_match($pattern, $description, $matches)) {
            // Check if the pattern resulted in two primary capturing groups (prefix + description)
            // This specifically handles the modified 'cust_to_cust' credit pattern or similar future patterns.
            if (isset($matches[1], $matches[2]) && trim($matches[2]) !== '') {
                // $matches[1] is the prefix, $matches[2] is the core description
                return trim($matches[1]) . trim($matches[2]);
            } elseif (isset($matches[1]) && trim($matches[1]) !== '') {
                // Standard case: $matches[1] is the core description
                return trim($matches[1]);
            }
        }
        
        // Fallback: if the normalizedPaymentTypeName was something like 'bank_payment' and didn't match,
        // try the generic 'bank' pattern if it exists.
        $genericPaymentTypeName = explode('_', $normalizedPaymentTypeName)[0]; // e.g., 'bank_payment' -> 'bank'
        if ($genericPaymentTypeName !== $normalizedPaymentTypeName && isset($patterns[$genericPaymentTypeName][$type])) {
            $pattern = $patterns[$genericPaymentTypeName][$type];
            if (preg_match($pattern, $description, $matches)) {
                if (isset($matches[1]) && trim($matches[1]) !== '') {
                    return trim($matches[1]);
                }
            }
        }

        // If no pattern matches or captured group is empty, return the original description
        return $description;
    }

    /**
     * Generate a printable HTML view for ledger data.
     */
    public function generateLedgerPrint(Request $request)
    {
        $user = $request->user();
        if (!$user || !in_array($user->role, ['Admin', 'Editor'])) {
            // For web routes, returning a view or redirecting might be better than JSON
            abort(403, 'Unauthorized');
        }

        try {
            $validated = $request->validate([
                'fromDate' => 'required|date',
                'tillDate' => 'required|date|after_or_equal:fromDate',
                'accountId' => 'required|integer|exists:accounts,id',
                'withPartyName' => 'required|in:"true","false"',
            ]);

            $fromDate = $validated['fromDate'];
            $tillDate = $validated['tillDate'];
            $accountId = $validated['accountId'];
            $withPartyName = filter_var($validated['withPartyName'], FILTER_VALIDATE_BOOLEAN);

            $account = Account::findOrFail($accountId);

            $initialBalance = JournalEntry::where('account_id', $accountId)
                ->where('date', '<', $fromDate)
                ->sum('amount');

            $ledgerEntries = JournalEntry::where('account_id', $accountId)
                ->whereBetween('date', [$fromDate, $tillDate])
                ->with(['paymentType'])
                ->orderBy('date')
                ->orderBy('TID')
                ->get();

            $runningBalance = $initialBalance;
            $processedEntries = [];

            // Remove opening balance row but keep initialBalance for running balance calculation
            // Group entries by date with date header rows
            $groupedEntries = [];

            foreach ($ledgerEntries as $entry) {
                $runningBalance += $entry->amount;
                $descriptionToUse = $entry->description;
                if (!$withPartyName && $entry->paymentType) {
                    $descriptionToUse = $this->stripNarrationFromDescription(
                        $entry->description,
                        $entry->paymentType->name,
                        $entry->is_credit
                    );
                }

                $entryObj = (object)[
                    'date' => $entry->date->format('Y-m-d'),
                    'TID' => $entry->TID,
                    'payment_type_name' => $entry->paymentType->name ?? '',
                    'description' => $descriptionToUse,
                    'inv_no' => $entry->inv_no,
                    'chq_no' => $entry->chq_no,
                    'debit' => !$entry->is_credit && $entry->amount > 0 ? $entry->amount : 0,
                    'credit' => $entry->is_credit && $entry->amount < 0 ? abs($entry->amount) : 0,
                    'balance' => $runningBalance,
                    'is_opening_balance' => false,
                ];

                // Add date header if not already added for this date
                if (!isset($groupedEntries[$entryObj->date])) {
                    $groupedEntries[$entryObj->date] = [
                        (object)[
                            'is_date_header' => true,
                            'date' => $entryObj->date,
                        ]
                    ];
                }
                $groupedEntries[$entryObj->date][] = $entryObj;
            }

            // Flatten grouped entries preserving order
            foreach ($groupedEntries as $dateGroup) {
                foreach ($dateGroup as $item) {
                    $processedEntries[] = $item;
                }
            }
            
            // Calculate totals similar to the React component
            $totalDr = 0;
            $totalCr = 0;
            $balance = 0;
            
            // Get the last entry for final balance
            $lastEntry = collect($processedEntries)
                ->filter(function($entry) {
                    return !isset($entry->is_date_header) && isset($entry->balance);
                })
                ->last();
                
            $balance = $lastEntry ? $lastEntry->balance : $initialBalance;
            
            // Calculate total debits and credits
            foreach ($processedEntries as $entry) {
                if (!isset($entry->is_date_header)) {
                    if ($entry->debit > 0) {
                        $totalDr += $entry->debit;
                    }
                    if ($entry->credit > 0) {
                        $totalCr += $entry->credit;
                    }
                }
            }
            
            // Get pending cheques
            $pendingCheques = $this->getPendingCheques($accountId);
            
            // Calculate cheque totals
            $chequeInwardTotal = -($pendingCheques['inward']['total_amount'] ?? 0);
            $chequeOutwardTotal = $pendingCheques['outward']['total_amount'] ?? 0;
            
            // Calculate final total
            $finalTotal = $balance + $chequeInwardTotal + $chequeOutwardTotal;
            
            // Prepare totals array
            $totals = [
                'totalDr' => $totalDr,
                'totalCr' => $totalCr,
                'balance' => $balance,
                'chequeInwardTotal' => $chequeInwardTotal,
                'chequeOutwardTotal' => $chequeOutwardTotal,
                'finalTotal' => $finalTotal,
                'chequeInwardCount' => $pendingCheques['inward']['count'] ?? 0,
                'chequeOutwardCount' => $pendingCheques['outward']['count'] ?? 0
            ];

            $companyName = config('app.name', 'AccSystem');

            $data = [
                'ledgerEntries' => $processedEntries,
                'accountName' => $account->name,
                'fromDate' => \Carbon\Carbon::parse($fromDate)->format('d-M-Y'),
                'tillDate' => \Carbon\Carbon::parse($tillDate)->format('d-M-Y'),
                'withPartyName' => $withPartyName,
                'companyName' => $companyName,
                'reportDate' => now()->format('d-M-Y H:i:s'),
                'totals' => $totals,
            ];

            // Return a Blade view instead of a PDF stream
            return view('print.ledger_pkr', $data);

        } catch (\Illuminate\Validation\ValidationException $e) {
            \Log::error('Printable Ledger Validation Error: ' . $e->getMessage(), ['errors' => $e->errors()]);
            // For a web route, redirecting back with errors is common
            return redirect()->back()->withErrors($e->errors())->withInput();
        } catch (\Exception $e) {
            \Log::error('Error generating printable ledger: ' . $e->getMessage(), ['exception' => $e]);
            // Show a generic error view or redirect
            // For simplicity, returning a basic error response, but a dedicated error view is better
            return response("Error generating printable ledger: " . $e->getMessage(), 500);
        }
    }

    /**
     * Helper method to get pending cheques for an account
     */
    private function getPendingCheques($accountId)
    {
        // Get pending inward cheques
        $pendingInwardCheques = Cheque::where('from_account_id', $accountId)
            ->where('status', 'pending')
            ->get();
            
        $inwardTotal = $pendingInwardCheques->sum('amount');
        $inwardCount = $pendingInwardCheques->count();
        
        // Get pending outward cheques
        $pendingOutwardCheques = Cheque::where('to_account_id', $accountId)
            ->where('status', 'pending')
            ->get();
            
        $outwardTotal = $pendingOutwardCheques->sum('amount');
        $outwardCount = $pendingOutwardCheques->count();
        
        return [
            'inward' => [
                'total_amount' => $inwardTotal,
                'count' => $inwardCount,
                'cheques' => $pendingInwardCheques
            ],
            'outward' => [
                'total_amount' => $outwardTotal,
                'count' => $outwardCount,
                'cheques' => $pendingOutwardCheques
            ]
        ];
    }
}
