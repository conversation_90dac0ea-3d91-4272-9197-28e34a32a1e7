import React, { useState, useEffect, useCallback } from 'react';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import axios from 'axios';
import { NumericFormat, type NumberFormatValues } from 'react-number-format';
import AppLayout from "@/layouts/app-layout";
import { BreadcrumbItem } from "@/types";       
import { Head, usePage, router } from "@inertiajs/react"; 
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ComboBox, type ComboBoxOption } from '@/components/ui/combobox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { toast } from 'sonner';
import { CalendarIcon, Loader2, Save } from 'lucide-react';

// Interfaces (similar to Sale.tsx)
interface Account {
    id: number;
    name: string;
    currency_code?: string;
    formula?: 'm' | 'd';
}

interface SaleDetailsForDisplay { // For the alert box, showing current state of currency
    currency_code: string;
    currency_balance: number;
    pkr_balance: number; // PKR value of current currency stock
    current_average_rate: number; // Current average cost rate of the stock
    formula_type: 'm' | 'd';
}

interface SaleFormData {
  date: string;
  selectDr: string; // Customer Account ID (DEBIT)
  selectCr: string; // Currency Account ID (CREDIT)
  currency_amount: string;
  rate: string;          // Selling Rate
  pkr_amount: string;    // PKR amount at selling rate (calculated, display)
  avg_rate: string;      // HISTORICAL Average rate of the currency sold (fetched, hidden, sent for validation)
  pnl_account_id: string;   // P&L Account for profit/loss (fetched or selected, sent)
  profit_loss_amount: string; // Calculated profit or loss (display, sent for validation)
  description: string;
  refno: string;
  orderid: string;        // S-NO (fetched, read-only)
}

interface SaleFormSubmitData {
    date: string;
    selectDr: number | null; // Customer Account ID
    selectCr: number | null; // Currency Account ID
    currency_amount: number;
    rate: number;         
    pkr_amount: number;    
    avg_rate: number;      
    pnl_account_id: number | null;
    profit_loss_amount: number; 
    description: string;
    refno: string;
    // orderid is not sent in update, it's in URL
}

const parseFormattedNumber = (value: string | number): number => {
    if (typeof value === 'number') return value;
    if (!value) return 0;
    return parseFloat(String(value).replace(/,/g, '')) || 0;
};

const formatNumber = (value: number | string, options?: Intl.NumberFormatOptions) => {
    const num = typeof value === 'string' ? parseFormattedNumber(value) : Number(value);
    if (isNaN(num)) return '';
    return num.toLocaleString(undefined, options);
};

interface EditSaleEntryProps {
    saleId: string; // Passed by Inertia from the route parameter
    // TODO: If backend pre-fetches sale data for Inertia render, can add it here.
    // saleData?: SaleFormData; // Example if pre-fetched
}

const EditSaleEntry: React.FC<EditSaleEntryProps> = ({ saleId }) => {
  const { errors: pageErrors } = usePage().props as any; 

  const { control, handleSubmit, watch, setValue, reset, formState: { errors: formHookErrors }, clearErrors } = useForm<SaleFormData>();

  const [currencyAccounts, setCurrencyAccounts] = useState<Account[]>([]);
  const [customerAccounts, setCustomerAccounts] = useState<Account[]>([]);
  const [pnlAccounts, setPnlAccounts] = useState<Account[]>([]); 
  
  const [selectedCurrencyDisplayDetails, setSelectedCurrencyDisplayDetails] = useState<SaleDetailsForDisplay | null>(null);
  const [transactionHistoricalAvgRate, setTransactionHistoricalAvgRate] = useState<number | null>(null);
  
  const [isLoadingPage, setIsLoadingPage] = useState(true); // For initial load
  const [isLoadingCurrencyDetails, setIsLoadingCurrencyDetails] = useState(false); // For dynamic currency detail fetch
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [datePickerOpen, setDatePickerOpen] = useState(false);

  const breadcrumbs: BreadcrumbItem[] = [
      { title: 'Dashboard', href: route('dashboard') },
      { title: 'Sale List', href: route('sale.list') },
      { title: 'Edit Sale', href: route('sale.edit', {saleId: saleId || '0'}) }, // Ensure saleId is available
  ];

  // Fetch current display details for a currency account (balance, current avg rate, formula)
  const fetchCurrencyDisplayDetails = useCallback(async (currencyAccountId: string) => {
    if (!currencyAccountId) {
        setSelectedCurrencyDisplayDetails(null);
        return;
    }
    setIsLoadingCurrencyDetails(true);
    try {
        console.log("Fetching currency details for ID:", currencyAccountId); // This ID is now from selectCr
        
        // This endpoint gives S-No, avg_rate (current), formula etc.
        // We mainly need formula and current stock details for display.
        const response = await axios.get('/api/sale-entries/get-details', { 
            params: { currency_account_id: currencyAccountId }
        });
        
        console.log("Currency details response:", response.data);
        
        const details = response.data;
        if (!details || !details.currency_code) {
            console.error("Invalid currency details response:", details);
            toast.error("Currency details response is missing required data.");
            setIsLoadingCurrencyDetails(false);
            return;
        }
        
        // Use default formula "m" if null - most common in system
        const formulaType = details.formula_type || 'm';
        
        setSelectedCurrencyDisplayDetails({
            currency_code: details.currency_code,
            currency_balance: details.currency_balance,
            pkr_balance: details.pkr_balance,
            current_average_rate: parseFloat(Number(details.average_rate || 0).toFixed(2)),
            formula_type: formulaType as 'm' | 'd',
        });
    } catch (error) {
        console.error('Error fetching currency display details:', error);
        // Try a backup approach with direct path if Ziggy route fails
        try {
            console.log("Trying backup approach with direct URL path");
            const backupResponse = await axios.get(`/api/sale-entries/get-details?currency_account_id=${currencyAccountId}`);
            console.log("Backup currency details response:", backupResponse.data);
            
            const details = backupResponse.data;
            if (!details || !details.currency_code) {
                throw new Error("Invalid currency details in backup response");
            }
            
            // Use default formula "m" if null - most common in system
            const formulaType = details.formula_type || 'm';
            
            setSelectedCurrencyDisplayDetails({
                currency_code: details.currency_code,
                currency_balance: details.currency_balance,
                pkr_balance: details.pkr_balance,
                current_average_rate: parseFloat(Number(details.average_rate || 0).toFixed(2)),
                formula_type: formulaType as 'm' | 'd',
            });
        } catch (backupError) {
            console.error('Backup approach also failed:', backupError);
            setSelectedCurrencyDisplayDetails(null);
            toast.error('Failed to fetch current currency details. Please try refreshing the page.');
        }
    }
    setIsLoadingCurrencyDetails(false);
  }, []);

  // Fetch Account Lists (Currency, Customer, P&L)
  useEffect(() => {
    const fetchAccounts = async () => {
        try {
            const [currencyRes, customerRes, pnlRes] = await Promise.all([
                axios.get('/api/accounts', { params: { account_type_id: 5 } }),
                axios.get('/api/accounts', { params: { exclude_account_type_ids: '4,5,7' } }),
                axios.get('/api/accounts', { params: { account_type_name: 'Income' } })
            ]);
            
            const getSafeData = (res: any) => Array.isArray(res.data) ? res.data : (res.data && Array.isArray(res.data.data)) ? res.data.data : [];
            const fetchedCurrencyAccounts = getSafeData(currencyRes);
            setCurrencyAccounts(fetchedCurrencyAccounts);
            console.log("Fetched Currency Accounts:", fetchedCurrencyAccounts); // Log currency accounts
            setCustomerAccounts(getSafeData(customerRes));
            setPnlAccounts(getSafeData(pnlRes));
        } catch (error) {
            console.error('Error fetching accounts:', error);
            toast.error('Failed to load accounts lists.');
        }
    };
    fetchAccounts();
  }, []);

  // Fetch existing Sale Data
  useEffect(() => {
    if (!saleId) {
        toast.error("Sale ID is missing. Cannot load data.");
        setIsLoadingPage(false);
        return;
    }
    setIsLoadingPage(true);
    axios.get(`/api/sale-entries/${saleId}`)
        .then(response => {
            console.log("Sale data response:", response.data); 
            const data = response.data;
            
            // Backend's `show` method now returns selectDr as Customer ID, selectCr as Currency ID.
            const customerAccountIdFromApi = data.selectDr;
            const currencyAccountIdFromApi = data.selectCr;
            const pnlAccountIdFromApi = data.pnl_account_id;
            // P&L account from API is data.pnl_account_id, which is correct.
            
            const sellingRate = data.rate > 0 ? data.rate : 1; 
            let currencyAmountSold = data.currency_amount;
            let pkrAtSellingRate = data.pkr_amount;

            // If currency_amount is 0 from API, but pkr_amount and rate are present, derive currency_amount
            if (currencyAmountSold <= 0 && pkrAtSellingRate > 0 && sellingRate > 0) {
                // Assuming formula 'm' (PKR per CUR) for this derivation if formula_type is not yet known
                currencyAmountSold = pkrAtSellingRate / sellingRate;
            }

            let historicalAvgRate = data.avg_rate; // Historical avg_rate from the transaction's currency credit leg

            // If historicalAvgRate from API is 0 or invalid, try to derive it from profit/loss
            if ((historicalAvgRate === null || historicalAvgRate <= 0) && data.profit_loss_amount !== 0 && currencyAmountSold > 0) {
                const pkrAtHistoricalCost = pkrAtSellingRate - data.profit_loss_amount;
                if (pkrAtHistoricalCost > 0) { // Ensure cost is positive
                     // Assuming formula 'm' (PKR per CUR) for this derivation
                    historicalAvgRate = pkrAtHistoricalCost / currencyAmountSold;
                }
            }
            
            // If historicalAvgRate is still not positive, fallback (e.g. to selling rate, implies 0 P/L)
            if (historicalAvgRate === null || historicalAvgRate <= 0) {
                historicalAvgRate = sellingRate; 
            }
            
            setTransactionHistoricalAvgRate(historicalAvgRate); 

            const saleDataForForm: SaleFormData = {
                date: data.date,
                selectDr: String(customerAccountIdFromApi),         // Customer Account ID
                selectCr: String(currencyAccountIdFromApi),         // Currency Account ID
                currency_amount: formatNumber(currencyAmountSold, {minimumFractionDigits:2, useGrouping: false}),
                rate: formatNumber(sellingRate, {minimumFractionDigits:2, maximumFractionDigits: 2, useGrouping: false}),
                pkr_amount: formatNumber(pkrAtSellingRate, {minimumFractionDigits:2, useGrouping: false}),
                avg_rate: formatNumber(historicalAvgRate, {minimumFractionDigits:2, maximumFractionDigits: 2, useGrouping: false}), 
                pnl_account_id: String(pnlAccountIdFromApi), // P&L Account ID remains the same logic
                profit_loss_amount: formatNumber(data.profit_loss_amount, {minimumFractionDigits:2, useGrouping: false}),
                description: data.description || '',
                refno: data.refno || '',
                orderid: data.orderid,
            };
            
            console.log("Form data after formatting, derivation, and ID swap:", saleDataForForm);
            reset(saleDataForForm);
            
            if (currencyAccountIdFromApi) { // Call with the correct currency account ID
                fetchCurrencyDisplayDetails(String(currencyAccountIdFromApi));
            }
        })
        .catch(error => {
            console.error('Error fetching sale data:', error);
            toast.error('Failed to load sale data for editing.');
            router.visit(route('sale.list'));
        })
        .finally(() => {
            setIsLoadingPage(false);
        });
  }, [saleId, reset, fetchCurrencyDisplayDetails]);

  // Helper to calculate currency amount from PKR when needed
  const calculateCurrencyAmountFromPkr = (pkrAmount: number, rate: number, avgRate: number): number => {
    // When rates are provided, calculate based on rate
    if (rate > 0) {
      return pkrAmount / rate; // Default to M formula (PKR/CURR) when formula is unknown
    }
    return 0; // Fallback
  };

  const currencyAccountIdFromSelectCr = watch('selectCr'); // Changed from selectDr
  useEffect(() => {
    if (currencyAccountIdFromSelectCr && !isLoadingPage) { // Avoid running on initial load before form is set
        fetchCurrencyDisplayDetails(currencyAccountIdFromSelectCr);
    }
  }, [currencyAccountIdFromSelectCr, isLoadingPage, fetchCurrencyDisplayDetails]);


  // Calculations
  const watchedCurrencyAmount = watch('currency_amount');
  const watchedSellingRate = watch('rate');

  useEffect(() => {
    const ca = parseFormattedNumber(watchedCurrencyAmount);
    const sr = parseFormattedNumber(watchedSellingRate);
    
    // For DISPLAYED Profit/Loss, use the current stock average rate
    const currentStockAr = selectedCurrencyDisplayDetails?.current_average_rate;
    const formula = selectedCurrencyDisplayDetails?.formula_type?.toLowerCase() || 'm';

    // Guard conditions for calculation
    if (isNaN(ca) || ca <= 0 || isNaN(sr) || sr <= 0) {
      setValue('pkr_amount', '0.00');
      // Set P/L to 0.00 if inputs are invalid, rather than currentStockAr check here initially
      setValue('profit_loss_amount', '0.00'); 
      return;
    }

    let pkrAtSellingRateFloat = 0;
    if (formula === 'd') {
      pkrAtSellingRateFloat = sr !== 0 ? ca / sr : 0;
    } else {
      pkrAtSellingRateFloat = ca * sr;
    }
    const pkrAtSellingRateForState = parseFloat(pkrAtSellingRateFloat.toFixed(2));
    setValue('pkr_amount', formatNumber(pkrAtSellingRateForState, {minimumFractionDigits: 2, maximumFractionDigits: 2, useGrouping: false}));

    // Now calculate profit_loss_amount for display using currentStockAr
    if (currentStockAr === undefined || currentStockAr === null || isNaN(currentStockAr)) {
        // If currentStockAr is not available, P/L might be considered N/A or 0 for display
        setValue('profit_loss_amount', '0.00'); // Or 'N/A' or some other placeholder
        return;
    }

    let pkrAtAverageRate_CurrentStockFloat = 0;
    if (formula === 'd') {
      if (currentStockAr === 0) {
        setValue('profit_loss_amount', 'N/A');
        return;
      }
      pkrAtAverageRate_CurrentStockFloat = ca / currentStockAr;
    } else {
      pkrAtAverageRate_CurrentStockFloat = ca * currentStockAr;
    }
    
    const profitLossForDisplayFloat = pkrAtSellingRateFloat - pkrAtAverageRate_CurrentStockFloat;
    const profitLossForDisplayForState = parseFloat(profitLossForDisplayFloat.toFixed(2));
    setValue('profit_loss_amount', formatNumber(profitLossForDisplayForState, {minimumFractionDigits: 2, maximumFractionDigits: 2, useGrouping: false}));

    // Note: formData.avg_rate still holds transactionHistoricalAvgRate.
    // The actual P/L for submission will be recalculated in onSubmit using formData.avg_rate.

  }, [watchedCurrencyAmount, watchedSellingRate, selectedCurrencyDisplayDetails, setValue]);

  const onSubmit = async (formData: SaleFormData) => {
    clearErrors();
    setIsSubmitting(true);

    // --- Recalculate amounts for backend submission based on historical avg_rate ---
    const currencyAmount = parseFormattedNumber(formData.currency_amount);
    const sellingRate = parseFormattedNumber(formData.rate);
    const historicalAvgRate = parseFormattedNumber(formData.avg_rate); // This is the one from initial load
    const formula = selectedCurrencyDisplayDetails?.formula_type?.toLowerCase() || 'm';

    let pkrAtSellingRate_ForSubmission = 0;
    let pkrAtHistoricalAvgRate_ForSubmission = 0;

    if (formula === 'd') {
        pkrAtSellingRate_ForSubmission = sellingRate !== 0 ? currencyAmount / sellingRate : 0;
        pkrAtHistoricalAvgRate_ForSubmission = historicalAvgRate !== 0 ? currencyAmount / historicalAvgRate : 0;
    } else { // 'm'
        pkrAtSellingRate_ForSubmission = currencyAmount * sellingRate;
        pkrAtHistoricalAvgRate_ForSubmission = currencyAmount * historicalAvgRate;
    }
    const profitLoss_ForSubmission = pkrAtSellingRate_ForSubmission - pkrAtHistoricalAvgRate_ForSubmission;
    // --- End recalculation for backend ---

    const submissionData: SaleFormSubmitData = {
      date: formData.date,
      selectDr: formData.selectDr ? Number(formData.selectDr) : null, // Customer ID
      selectCr: formData.selectCr ? Number(formData.selectCr) : null, // Currency ID
      currency_amount: currencyAmount, 
      rate: sellingRate, 
      pkr_amount: parseFloat(pkrAtSellingRate_ForSubmission.toFixed(2)), 
      avg_rate: historicalAvgRate, 
      pnl_account_id: formData.pnl_account_id ? Number(formData.pnl_account_id) : null,
      profit_loss_amount: parseFloat(profitLoss_ForSubmission.toFixed(2)), 
      description: formData.description,
      refno: formData.refno,
    };
    
    console.log("Submitting Data:", submissionData); // Log data being sent

    try {
        await axios.put(`/api/sale-entries/${saleId}`, submissionData); 
        toast.success('Sale transaction updated successfully!');
        router.visit(route('sale.list')); 
    } catch (error: any) {
        console.error("Submission error:", error.response?.data);
        const errorsFromServer = error.response?.data?.errors;
        let errorMessage = error.response?.data?.message || error.response?.data?.error || 'An unknown error occurred.';

        if (errorsFromServer) {
            Object.keys(errorsFromServer).forEach((key: string) => {
                // setValue(key as keyof SaleFormData, formData[key as keyof SaleFormData], {shouldValidate: true});
                // setError(key as keyof SaleFormData, { type: 'server', message: errorsFromServer[key][0]});
            });
            const firstKey = Object.keys(errorsFromServer)[0];
            errorMessage = firstKey ? errorsFromServer[firstKey][0] : errorMessage;
        }
        toast.error('Sale update failed!', { description: errorMessage });
    } finally {
        setIsSubmitting(false);
    }
  };
  
  const handleNumericValueChange = (field: keyof SaleFormData, values: NumberFormatValues) => {
    setValue(field, values.value); 
  };

  const handleAccountChange = (field: 'selectDr' | 'selectCr' | 'pnl_account_id', accountId: string) => {
      setValue(field, accountId);
      // fetchCurrencyDisplayDetails is now triggered by useEffect watching selectCr
  };
  
  const calculateMarginDisplay = () => { 
    const sr = parseFormattedNumber(watchedSellingRate);
    // Use current average rate from selected currency display details, similar to Sale.tsx
    const currentStockAvgCost = selectedCurrencyDisplayDetails?.current_average_rate;

    if (
        isNaN(sr) || sr <= 0 ||
        currentStockAvgCost === undefined || currentStockAvgCost === null || isNaN(currentStockAvgCost) // Allow currentStockAvgCost to be 0 or less for calculation if formula handles it
    ) {
        return '0.00';
    }
    
    // Ensure cost is not zero if formula is 'd' to prevent division by zero for avgCostPKRPerUnit
    // This check might be too restrictive if currentStockAvgCost can legitimately be zero.
    // However, for margin display, if cost is zero and formula is 'd', selling at any sr > 0 is infinite margin in terms of FCU/PKR.
    // The original check: if (currentStockAvgCost === 0 && selectedCurrencyDisplayDetails?.formula_type?.toLowerCase() === 'd') {
    //     return 'N/A'; 
    // }
    
    const formula = selectedCurrencyDisplayDetails?.formula_type?.toLowerCase() || 'm';
    let margin = 0;

    if (formula === 'd') {
         // Corrected: currentStockAvgCost (FCU per PKR cost) - sr (FCU per PKR selling price)
         margin = currentStockAvgCost - sr; 
    } else { // formula === 'm' or any other default
        margin = sr - currentStockAvgCost; 
    }
    return formatNumber(margin, {minimumFractionDigits: 2, maximumFractionDigits: 2});
  };

  if (isLoadingPage) {
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Loading Edit Sale..." />
            <div className="flex justify-center items-center h-64">
                <Loader2 className="h-16 w-16 animate-spin text-primary" />
            </div>
        </AppLayout>
    );
  }

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title={`Edit Sale S-No: ${watch('orderid') || ''}`} />
      <Card className="max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle>Edit Sale Entry (S-No: {watch('orderid')})</CardTitle>
        </CardHeader>
        <form onSubmit={handleSubmit(onSubmit)}>
        <CardContent className="space-y-6">
            {/* Date and Order ID (Read-only) */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
                <div>
                    <Label htmlFor="date">Date</Label>
                    <Popover open={datePickerOpen} onOpenChange={setDatePickerOpen}>
                        <PopoverTrigger asChild>
                            <Button
                                variant={"outline"}
                                className={`w-full justify-start text-left font-normal ${!watch('date') && "text-muted-foreground"}`}
                                disabled={isSubmitting}
                            >
                                <CalendarIcon className="mr-2 h-4 w-4" />
                                {watch('date') ? new Date(watch('date') + 'T00:00:00').toLocaleDateString() : <span>Pick a date</span>}
                            </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                            <Calendar
                                mode="single"
                                selected={watch('date') ? new Date(watch('date') + 'T00:00:00') : undefined}
                                onSelect={(selectedDate) => {
                                    if (selectedDate) {
                                        const year = selectedDate.getFullYear();
                                        const month = String(selectedDate.getMonth() + 1).padStart(2, '0');
                                        const day = String(selectedDate.getDate()).padStart(2, '0');
                                        setValue('date', `${year}-${month}-${day}`);
                                    } else {
                                        setValue('date', '');
                                    }
                                    setDatePickerOpen(false);
                                }}
                                initialFocus
                            />
                        </PopoverContent>
                    </Popover>
                    {formHookErrors.date && <p className="text-red-500 text-xs mt-1">{formHookErrors.date.message}</p>}
                </div>
                <div>
                    <Label htmlFor="orderid">S-No (Order ID)</Label>
                    <Input id="orderid" {...control.register("orderid")} readOnly className="bg-gray-100 dark:bg-gray-800" />
                </div>
            </div>

            {/* Account Selection */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <Label htmlFor="selectDr">Customer Account (Debit)</Label>
                    <Controller
                        name="selectDr"
                        control={control}
                        rules={{ required: 'Customer account is required' }}
                        render={({ field }) => (
                            <ComboBox 
                                value={field.value}
                                onChange={(val) => handleAccountChange('selectDr', val)}
                                options={customerAccounts.map(acc => ({ // Swapped: uses customerAccounts
                                    label: acc.name,
                                    value: String(acc.id)
                                }))}
                                placeholder="Select Customer Account..."
                            />
                        )}
                    />
                    {formHookErrors.selectDr && <p className="text-red-500 text-xs mt-1">{formHookErrors.selectDr.message}</p>}
                </div>
                <div>
                    <Label htmlFor="selectCr">Currency Account (Credit)</Label>
                    <Controller
                        name="selectCr"
                        control={control}
                        rules={{ required: 'Currency account is required' }}
                        render={({ field }) => (
                            <ComboBox 
                                value={field.value}
                                onChange={(val) => handleAccountChange('selectCr', val)}
                                options={currencyAccounts.map(acc => ({ // Swapped: uses currencyAccounts
                                    label: `${acc.name}${acc.currency_code ? ` (${acc.currency_code})` : ''}`.trim(),
                                    value: String(acc.id)
                                }))}
                                placeholder="Select Currency Account..."
                            />
                        )}
                    />
                    {formHookErrors.selectCr && <p className="text-red-500 text-xs mt-1">{formHookErrors.selectCr.message}</p>}
                </div>
            </div>
        
            {/* Currency Display Details Alert */}
            {selectedCurrencyDisplayDetails && (
                <Alert className="bg-sky-50 dark:bg-sky-900 border-sky-200 dark:border-sky-700 text-sky-800 dark:text-sky-100">
                    <AlertTitle className="font-semibold">
                        Selected Currency: {selectedCurrencyDisplayDetails.currency_code} (Current Stock Info)
                        {isLoadingCurrencyDetails && <Loader2 className="ml-2 h-4 w-4 animate-spin inline-block" />}
                    </AlertTitle>
                    <AlertDescription className="grid grid-cols-2 md:grid-cols-4 gap-x-4 gap-y-1 text-sm">
                        <span>Balance: {formatNumber(selectedCurrencyDisplayDetails.currency_balance || 0, {maximumFractionDigits:2})}</span>
                        <span>PKR Value: {formatNumber(selectedCurrencyDisplayDetails.pkr_balance || 0, {maximumFractionDigits:2})}</span>
                        <span>Current Avg. Cost: { (selectedCurrencyDisplayDetails.current_average_rate !== undefined && selectedCurrencyDisplayDetails.current_average_rate !== null) ? formatNumber(selectedCurrencyDisplayDetails.current_average_rate, {minimumFractionDigits:2, maximumFractionDigits: 2}) : 'N/A' }</span>
                        <span>Formula: {selectedCurrencyDisplayDetails.formula_type?.toUpperCase() || 'M'}</span>
                    </AlertDescription>
                    {transactionHistoricalAvgRate && transactionHistoricalAvgRate > 0 && (
                        <AlertDescription className="mt-2 text-sm font-medium">
                            Historical Average Rate: {formatNumber(transactionHistoricalAvgRate, {minimumFractionDigits:2, maximumFractionDigits: 2})} 
                            (Used for P&L calculation)
                        </AlertDescription>
                    )}
                </Alert>
            )}
             {/* Hidden input for historical avg_rate from API to be submitted for validation */}
             <Controller name="avg_rate" control={control} render={({ field }) => <Input type="hidden" {...field} />} />


            {/* Amounts and Rates */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <Label htmlFor="currency_amount">Currency Amount ({selectedCurrencyDisplayDetails?.currency_code || '...'})</Label>
                    <Controller 
                        name="currency_amount"
                        control={control} 
                        rules={{ required: 'Currency amount is required', validate: val => parseFormattedNumber(val) > 0 || 'Must be > 0'}}
                        render={({ field }) => (
                            <NumericFormat 
                                {...field}
                                id="currency_amount"
                                value={field.value}
                                onValueChange={(values) => handleNumericValueChange('currency_amount', values)}
                                customInput={Input}
                                thousandSeparator={true}
                                allowNegative={false}
                                decimalScale={2}
                                placeholder="e.g. 1000"
                                disabled={!selectedCurrencyDisplayDetails || isLoadingCurrencyDetails || isSubmitting}
                             />
                        )}
                    />
                    {formHookErrors.currency_amount && <p className="text-red-500 text-xs mt-1">{formHookErrors.currency_amount.message}</p>}
                </div>
                <div>
                    <Label htmlFor="rate">Selling Rate</Label>
                     <Controller 
                        name="rate"
                        control={control} 
                        rules={{ required: 'Selling rate is required', validate: val => parseFormattedNumber(val) > 0 || 'Must be > 0'}}
                        render={({ field }) => (
                            <NumericFormat 
                                {...field}
                                id="rate"
                                value={field.value}
                                onValueChange={(values) => handleNumericValueChange('rate', values)}
                                customInput={Input}
                                thousandSeparator={true}
                                allowNegative={false}
                                decimalScale={2}
                                fixedDecimalScale
                                placeholder="e.g. 275.50"
                                disabled={!selectedCurrencyDisplayDetails || isLoadingCurrencyDetails || isSubmitting}
                             />
                        )}
                    />
                    {formHookErrors.rate && <p className="text-red-500 text-xs mt-1">{formHookErrors.rate.message}</p>}
                </div>
            </div>
        
            {/* Calculated Amounts */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-3 bg-blue-50 dark:bg-blue-900/30 rounded items-center">
                <div>
                    <Label>Calculated Margin:</Label> 
                    <Input value={calculateMarginDisplay()} readOnly className="bg-gray-100 dark:bg-gray-800 font-semibold"/>
                </div>
                <div>
                    <Label htmlFor="pkr_amount">PKR Amount:</Label>
                    <Controller 
                        name="pkr_amount"
                        control={control} 
                        render={({ field }) => (
                            <NumericFormat 
                                {...field}
                                value={field.value}
                                readOnly 
                                customInput={Input} 
                                thousandSeparator={true}
                                decimalScale={2}
                                fixedDecimalScale
                                className="bg-gray-100 dark:bg-gray-800 font-semibold"
                             />
                        )}
                    />
                </div>
                <div>
                    <Label htmlFor="profit_loss_amount">Profit/Loss Amount:</Label>
                    <Controller 
                        name="profit_loss_amount"
                        control={control} 
                        render={({ field }) => (
                            <NumericFormat 
                                {...field}
                                value={field.value}
                                readOnly 
                                customInput={Input} 
                                thousandSeparator={true}
                                decimalScale={2}
                                fixedDecimalScale
                                className={`bg-gray-100 dark:bg-gray-800 font-semibold ${parseFormattedNumber(field.value) < 0 ? 'text-red-600' : 'text-green-600'}`}
                             />
                        )}
                    />
                </div>
            </div>
        
            {/* P&L Account, Ref No, Description */}
            <div>
                <Label htmlFor="pnl_account_id">P&L Account</Label>
                <Controller
                    name="pnl_account_id"
                    control={control}
                    rules={{ required: 'P&L account is required' }}
                    render={({ field }) => (
                        <Select 
                            onValueChange={(value) => handleAccountChange('pnl_account_id', value)} 
                            value={field.value}
                            disabled={isSubmitting || isLoadingPage || !selectedCurrencyDisplayDetails}
                        >
                            <SelectTrigger><SelectValue placeholder="Select P&L Account" /></SelectTrigger>
                            <SelectContent>
                                {pnlAccounts.map(acc => <SelectItem key={acc.id} value={String(acc.id)}>{acc.name}</SelectItem>)}
                            </SelectContent>
                        </Select>
                    )}
                />
                {formHookErrors.pnl_account_id && <p className="text-red-500 text-xs mt-1">{formHookErrors.pnl_account_id.message}</p>}
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <Label htmlFor="refno">Reference No</Label>
                    <Controller name="refno" control={control} render={({ field }) => <Input type="text" {...field} disabled={isSubmitting}/>} />
                </div>
                <div>
                    <Label htmlFor="description">Description</Label>
                    <Controller name="description" control={control} render={({ field }) => <Input type="text" {...field} disabled={isSubmitting}/>} /> 
                </div>
            </div>

            {pageErrors && typeof pageErrors === 'string' && (
                 <Alert variant="destructive"><AlertTitle>Error</AlertTitle><AlertDescription>{pageErrors}</AlertDescription></Alert>
            )}
            {pageErrors && typeof pageErrors === 'object' && Object.keys(pageErrors).length > 0 && (
                <Alert variant="destructive">
                    <AlertTitle>Please correct the following errors:</AlertTitle>
                    <AlertDescription>
                        <ul className="list-disc list-inside">
                            {Object.values(pageErrors as Record<string, string[]>).map((errorArray, index) => (
                                errorArray.map((errorMsg, subIndex) => <li key={`${index}-${subIndex}`}>{errorMsg}</li>)
                            ))}
                        </ul>
                    </AlertDescription>
                </Alert>
            )}

        </CardContent>
        <CardFooter className="flex justify-end">
            <Button type="button" variant="outline" onClick={() => router.visit(route('sale.list'))} className="mr-2" disabled={isSubmitting}>
                Cancel
            </Button>
            <Button type="submit" disabled={isLoadingCurrencyDetails || isSubmitting || !watch('selectCr')}> {/* Changed from !watch('selectDr') */}
                {isSubmitting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Save className="mr-2 h-4 w-4" />}
                Update Sale Entry
            </Button>
        </CardFooter>
        </form>
      </Card>
    </AppLayout>
  );
};

export default EditSaleEntry; 
