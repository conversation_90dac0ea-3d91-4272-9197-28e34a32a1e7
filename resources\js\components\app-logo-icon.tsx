import { SVGAttributes } from 'react';

export default function AppLogoIcon(props: SVGAttributes<SVGElement>) {
    return (
        <svg {...props} width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect width="32" height="32" rx="8" fill="#F53003" className="dark:fill-[#F8B803]" />
            <text x="50%" y="57%" textAnchor="middle" fill="#fff" fontSize="15" fontWeight="bold" dy=".3em">AS</text>
        </svg>
    );
}
