# Laravel 12 Financial Double Accounting Application Implementation Guide

This practical guide walks through implementing a financial double accounting system using Laravel 12, React, and MySQL optimization techniques.

## Getting Started

### Project Setup

```bash
# Create a new Laravel 12 project
composer create-project laravel/laravel financial-accounting-app

# Navigate to project directory
cd financial-accounting-app

# Install React starter kit
composer require laravel/breeze --dev
php artisan breeze:install react

# Install financial accounting package (using Eloquent IFRS as example)
composer require ekmungai/eloquent-ifrs

# Publish package assets
php artisan vendor:publish --provider="Ekmungai\IFRS\IFRSServiceProvider"

# Run migrations
php artisan migrate

# Install NPM dependencies
npm install

# Add React Query for efficient data fetching
npm install @tanstack/react-query
```

### Database Configuration

Optimize your MySQL configuration for financial data in `.env`:

```
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=financial_app
DB_USERNAME=root
DB_PASSWORD=

# Connection pooling for production
DB_POOL=true
DB_POOL_MIN=5
DB_POOL_MAX=20
```

Update `config/database.php` for production:

```php
'mysql' => [
    // ...existing config
    'sticky' => true,
    'pool' => [
        'enabled' => env('DB_POOL', false),
        'min' => env('DB_POOL_MIN', 2),
        'max' => env('DB_POOL_MAX', 10),
    ],
],
```

## Database Schema Design

Create optimized migrations for financial data:

```php
// Create accounting_entities table
Schema::create('accounting_entities', function (Blueprint $table) {
    $table->id();
    $table->string('name');
    $table->timestamps();
    $table->index('name');
});

// Create chart_of_accounts table
Schema::create('chart_of_accounts', function (Blueprint $table) {
    $table->id();
    $table->foreignId('entity_id')->constrained('accounting_entities');
    $table->string('name');
    $table->timestamps();
    $table->index(['entity_id', 'name']);
});

// Create accounts table with optimized data types
Schema::create('accounts', function (Blueprint $table) {
    $table->id();
    $table->foreignId('chart_id')->constrained('chart_of_accounts');
    $table->string('code', 20);
    $table->string('name');
    $table->string('type', 20); // asset, liability, equity, revenue, expense
    $table->foreignId('parent_id')->nullable()->constrained('accounts');
    $table->timestamps();
    
    // Add indexes for frequent queries
    $table->index('code');
    $table->index(['chart_id', 'type']);
    $table->index(['chart_id', 'parent_id']);
});

// Create journals table with date partitioning
Schema::create('journals', function (Blueprint $table) {
    $table->id();
    $table->foreignId('entity_id')->constrained('accounting_entities');
    $table->string('reference', 50);
    $table->string('description');
    $table->date('journal_date');
    $table->timestamps();
    
    // Add indexes for reporting queries
    $table->index('reference');
    $table->index(['entity_id', 'journal_date']);
});

// Create journal_entries table with optimal indexing
Schema::create('journal_entries', function (Blueprint $table) {
    $table->id();
    $table->foreignId('journal_id')->constrained()->onDelete('cascade');
    $table->foreignId('account_id')->constrained('accounts');
    $table->decimal('amount', 13, 4);
    $table->boolean('is_credit')->default(false);
    $table->timestamps();
    
    // Add indexes for balance calculations
    $table->index(['account_id', 'is_credit']);
    $table->index(['journal_id', 'account_id']);
});
```

## Model Definitions

Create optimized Eloquent models:

```php
// app/Models/AccountingEntity.php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class AccountingEntity extends Model
{
    protected $fillable = ['name'];
    
    public function chartOfAccounts()
    {
        return $this->hasOne(ChartOfAccounts::class, 'entity_id');
    }
    
    public function journals()
    {
        return $this->hasMany(Journal::class, 'entity_id');
    }
}

// app/Models/ChartOfAccounts.php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ChartOfAccounts extends Model
{
    protected $fillable = ['entity_id', 'name'];
    
    public function entity()
    {
        return $this->belongsTo(AccountingEntity::class, 'entity_id');
    }
    
    public function accounts()
    {
        return $this->hasMany(Account::class, 'chart_id');
    }
}

// app/Models/Account.php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;

class Account extends Model
{
    protected $fillable = ['chart_id', 'code', 'name', 'type', 'parent_id'];
    
    public function chart()
    {
        return $this->belongsTo(ChartOfAccounts::class, 'chart_id');
    }
    
    public function parent()
    {
        return $this->belongsTo(Account::class, 'parent_id');
    }
    
    public function children()
    {
        return $this->hasMany(Account::class, 'parent_id');
    }
    
    public function entries()
    {
        return $this->hasMany(JournalEntry::class, 'account_id');
    }
    
    // Efficiently calculate balance
    public function balance($startDate = null, $endDate = null)
    {
        $query = $this->entries();
        
        if ($startDate) {
            $query->whereHas('journal', function ($q) use ($startDate) {
                $q->where('journal_date', '>=', $startDate);
            });
        }
        
        if ($endDate) {
            $query->whereHas('journal', function ($q) use ($endDate) {
                $q->where('journal_date', '<=', $endDate);
            });
        }
        
        $credits = (clone $query)->where('is_credit', true)->sum('amount');
        $debits = (clone $query)->where('is_credit', false)->sum('amount');
        
        // Calculate based on account type
        if (in_array($this->type, ['asset', 'expense'])) {
            return $debits - $credits;
        } else {
            return $credits - $debits;
        }
    }
    
    // Eager load balance for multiple accounts efficiently
    public static function withBalances($chartId, $date = null)
    {
        return static::where('chart_id', $chartId)
            ->withSum(['entries as credits' => function ($query) use ($date) {
                $query->where('is_credit', true);
                if ($date) {
                    $query->whereHas('journal', function ($q) use ($date) {
                        $q->where('journal_date', '<=', $date);
                    });
                }
            }], 'amount')
            ->withSum(['entries as debits' => function ($query) use ($date) {
                $query->where('is_credit', false);
                if ($date) {
                    $query->whereHas('journal', function ($q) use ($date) {
                        $q->where('journal_date', '<=', $date);
                    });
                }
            }], 'amount');
    }
}

// app/Models/Journal.php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class Journal extends Model
{
    protected $fillable = ['entity_id', 'reference', 'description', 'journal_date'];
    
    public function entity()
    {
        return $this->belongsTo(AccountingEntity::class, 'entity_id');
    }
    
    public function entries()
    {
        return $this->hasMany(JournalEntry::class);
    }
    
    // Create a balanced journal entry
    public function addEntries(array $entries)
    {
        // Validate that entries balance
        $total = 0;
        foreach ($entries as $entry) {
            $amount = $entry['amount'];
            if (!empty($entry['is_credit'])) {
                $total += $amount;
            } else {
                $total -= $amount;
            }
        }
        
        if (abs($total) > 0.0001) {
            throw new \Exception("Journal entries must balance. Current imbalance: {$total}");
        }
        
        // Create entries in a transaction
        return DB::transaction(function () use ($entries) {
            foreach ($entries as $entry) {
                $this->entries()->create($entry);
            }
            return $this;
        });
    }
    
    // Helper to post a balanced transaction
    public static function postTransaction($entityId, $reference, $description, $date, array $entries)
    {
        return DB::transaction(function () use ($entityId, $reference, $description, $date, $entries) {
            $journal = static::create([
                'entity_id' => $entityId,
                'reference' => $reference,
                'description' => $description,
                'journal_date' => $date,
            ]);
            
            $journal->addEntries($entries);
            
            return $journal;
        });
    }
}

// app/Models/JournalEntry.php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class JournalEntry extends Model
{
    protected $fillable = ['journal_id', 'account_id', 'amount', 'is_credit'];
    
    public function journal()
    {
        return $this->belongsTo(Journal::class);
    }
    
    public function account()
    {
        return $this->belongsTo(Account::class);
    }
}
```

## Service Layer

Create a service layer to encapsulate accounting logic:

```php
// app/Services/AccountingService.php
namespace App\Services;

use App\Models\Account;
use App\Models\Journal;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;

class AccountingService
{
    /**
     * Create a new transaction with balanced entries
     */
    public function createTransaction(int $entityId, string $reference, string $description, 
                                     string $date, array $entries)
    {
        return Journal::postTransaction(
            $entityId, 
            $reference, 
            $description, 
            Carbon::parse($date),
            $entries
        );
    }
    
    /**
     * Get account balance with caching for performance
     */
    public function getAccountBalance(int $accountId, ?string $date = null)
    {
        $cacheKey = "account_balance_{$accountId}_" . ($date ?? 'all');
        
        return Cache::remember($cacheKey, now()->addHours(1), function () use ($accountId, $date) {
            $account = Account::findOrFail($accountId);
            return $account->balance($date ? null : Carbon::parse($date));
        });
    }
    
    /**
     * Generate a trial balance
     */
    public function generateTrialBalance(int $entityId, ?string $date = null)
    {
        $cacheKey = "trial_balance_{$entityId}_" . ($date ?? 'current');
        $cacheTime = now()->addMinutes(30);
        
        return Cache::remember($cacheKey, $cacheTime, function () use ($entityId, $date) {
            $entity = AccountingEntity::findOrFail($entityId);
            $chartId = $entity->chartOfAccounts->id;
            
            $accounts = Account::withBalances($chartId, $date ? Carbon::parse($date) : null)
                ->get()
                ->map(function ($account) {
                    // Calculate balance based on account type
                    if (in_array($account->type, ['asset', 'expense'])) {
                        $balance = $account->debits - $account->credits;
                    } else {
                        $balance = $account->credits - $account->debits;
                    }
                    
                    return [
                        'id' => $account->id,
                        'code' => $account->code,
                        'name' => $account->name,
                        'type' => $account->type,
                        'balance' => $balance,
                    ];
                });
                
            return [
                'date' => $date ?? now()->toDateString(),
                'entity' => $entity->name,
                'accounts' => $accounts,
                'total_debits' => $accounts->where('balance', '>', 0)->sum('balance'),
                'total_credits' => abs($accounts->where('balance', '<', 0)->sum('balance')),
            ];
        });
    }
    
    /**
     * Generate an income statement
     */
    public function generateIncomeStatement(int $entityId, string $startDate, string $endDate)
    {
        $cacheKey = "income_statement_{$entityId}_{$startDate}_{$endDate}";
        $cacheTime = now()->addMinutes(30);
        
        return Cache::remember($cacheKey, $cacheTime, function () use ($entityId, $startDate, $endDate) {
            $entity = AccountingEntity::findOrFail($entityId);
            $chartId = $entity->chartOfAccounts->id;
            
            $start = Carbon::parse($startDate);
            $end = Carbon::parse($endDate);
            
            $revenueAccounts = Account::where('chart_id', $chartId)
                ->where('type', 'revenue')
                ->get()
                ->map(function ($account) use ($start, $end) {
                    return [
                        'id' => $account->id,
                        'code' => $account->code,
                        'name' => $account->name,
                        'balance' => $account->balance($start, $end),
                    ];
                });
                
            $expenseAccounts = Account::where('chart_id', $chartId)
                ->where('type', 'expense')
                ->get()
                ->map(function ($account) use ($start, $end) {
                    return [
                        'id' => $account->id,
                        'code' => $account->code,
                        'name' => $account->name,
                        'balance' => $account->balance($start, $end),
                    ];
                });
                
            $totalRevenue = $revenueAccounts->sum('balance');
            $totalExpenses = $expenseAccounts->sum('balance');
            $netIncome = $totalRevenue - $totalExpenses;
            
            return [
                'start_date' => $startDate,
                'end_date' => $endDate,
                'entity' => $entity->name,
                'revenue_accounts' => $revenueAccounts,
                'expense_accounts' => $expenseAccounts,
                'total_revenue' => $totalRevenue,
                'total_expenses' => $totalExpenses,
                'net_income' => $netIncome,
            ];
        });
    }
    
    /**
     * Generate a balance sheet
     */
    public function generateBalanceSheet(int $entityId, ?string $date = null)
    {
        $cacheKey = "balance_sheet_{$entityId}_" . ($date ?? 'current');
        $cacheTime = now()->addMinutes(30);
        
        return Cache::remember($cacheKey, $cacheTime, function () use ($entityId, $date) {
            $entity = AccountingEntity::findOrFail($entityId);
            $chartId = $entity->chartOfAccounts->id;
            $balanceDate = $date ? Carbon::parse($date) : now();
            
            $assetAccounts = Account::where('chart_id', $chartId)
                ->where('type', 'asset')
                ->get()
                ->map(function ($account) use ($balanceDate) {
                    return [
                        'id' => $account->id,
                        'code' => $account->code,
                        'name' => $account->name,
                        'balance' => $account->balance(null, $balanceDate),
                    ];
                });
                
            $liabilityAccounts = Account::where('chart_id', $chartId)
                ->where('type', 'liability')
                ->get()
                ->map(function ($account) use ($balanceDate) {
                    return [
                        'id' => $account->id,
                        'code' => $account->code,
                        'name' => $account->name,
                        'balance' => $account->balance(null, $balanceDate),
                    ];
                });
                
            $equityAccounts = Account::where('chart_id', $chartId)
                ->where('type', 'equity')
                ->get()
                ->map(function ($account) use ($balanceDate) {
                    return [
                        'id' => $account->id,
                        'code' => $account->code,
                        'name' => $account->name,
                        'balance' => $account->balance(null, $balanceDate),
                    ];
                });
                
            $totalAssets = $assetAccounts->sum('balance');
            $totalLiabilities = $liabilityAccounts->sum('balance');
            $totalEquity = $equityAccounts->sum('balance');
            
            return [
                'date' => $balanceDate->toDateString(),
                'entity' => $entity->name,
                'asset_accounts' => $assetAccounts,
                'liability_accounts' => $liabilityAccounts,
                'equity_accounts' => $equityAccounts,
                'total_assets' => $totalAssets,
                'total_liabilities' => $totalLiabilities,
                'total_equity' => $totalEquity,
            ];
        });
    }
}
```

## API Controllers

Create optimized API controllers:

```php
// app/Http/Controllers/Api/AccountController.php
namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Account;
use App\Http\Resources\AccountResource;
use App\Http\Requests\StoreAccountRequest;
use App\Http\Requests\UpdateAccountRequest;
use Illuminate\Http\Request;

class AccountController extends Controller
{
    public function index(Request $request)
    {
        $accounts = Account::query()
            ->when($request->chart_id, function ($query, $chartId) {
                return $query->where('chart_id', $chartId);
            })
            ->when($request->type, function ($query, $type) {
                return $query->where('type', $type);
            })
            ->when($request->parent_id, function ($query, $parentId) {
                return $query->where('parent_id', $parentId);
            })
            ->when($request->has('with_balance') && $request->with_balance, function ($query) use ($request) {
                $date = $request->balance_date ?? null;
                return Account::withBalances($request->chart_id, $date);
            })
            ->paginate($request->per_page ?? 15);
            
        return AccountResource::collection($accounts);
    }
    
    public function store(StoreAccountRequest $request)
    {
        $account = Account::create($request->validated());
        return new AccountResource($account);
    }
    
    public function show(Account $account)
    {
        return new AccountResource($account->load('parent', 'children'));
    }
    
    public function update(UpdateAccountRequest $request, Account $account)
    {
        $account->update($request->validated());
        return new AccountResource($account);
    }
    
    public function destroy(Account $account)
    {
        // Check if account has entries before deleting
        if ($account->entries()->count() > 0) {
            return response()->json([
                'message' => 'Cannot delete account with existing entries'
            ], 422);
        }
        
        $account->delete();
        return response()->json(['message' => 'Account deleted']);
    }
}

// app/Http/Controllers/Api/JournalController.php
namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Journal;
use App\Http\Resources\JournalResource;
use App\Http\Requests\StoreJournalRequest;
use App\Services\AccountingService;
use Illuminate\Http\Request;

class JournalController extends Controller
{
    protected $accountingService;
    
    public function __construct(AccountingService $accountingService)
    {
        $this->accountingService = $accountingService;
    }
    
    public function index(Request $request)
    {
        $journals = Journal::query()
            ->when($request->entity_id, function ($query, $entityId) {
                return $query->where('entity_id', $entityId);
            })
            ->when($request->start_date, function ($query, $startDate) {
                return $query->where('journal_date', '>=', $startDate);
            })
            ->when($request->end_date, function ($query, $endDate) {
                return $query->where('journal_date', '<=', $endDate);
            })
            ->when($request->reference, function ($query, $reference) {
                return $query->where('reference', 'like', "%{$reference}%");
            })
            ->when($request->description, function ($query, $description) {
                return $query->where('description', 'like', "%{$description}%");
            })
            ->orderBy($request->sort_by ?? 'journal_date', $request->sort_dir ?? 'desc')
            ->with('entries.account')
            ->paginate($request->per_page ?? 15);
            
        return JournalResource::collection($journals);
    }
    
    public function store(StoreJournalRequest $request)
    {
        $journal = $this->accountingService->createTransaction(
            $request->entity_id,
            $request->reference,
            $request->description,
            $request->journal_date,
            $request->entries
        );
        
        return new JournalResource($journal->load('entries.account'));
    }
    
    public function show(Journal $journal)
    {
        return new JournalResource($journal->load('entries.account', 'entity'));
    }
}

// app/Http/Controllers/Api/ReportController.php
namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\AccountingService;
use Illuminate\Http\Request;

class ReportController extends Controller
{
    protected $accountingService;
    
    public function __construct(AccountingService $accountingService)
    {
        $this->accountingService = $accountingService;
    }
    
    public function trialBalance(Request $request)
    {
        $request->validate([
            'entity_id' => 'required|exists:accounting_entities,id',
            'date' => 'nullable|date',
        ]);
        
        $trialBalance = $this->accountingService->generateTrialBalance(
            $request->entity_id,
            $request->date
        );
        
        return response()->json($trialBalance);
    }
    
    public function incomeStatement(Request $request)
    {
        $request->validate([
            'entity_id' => 'required|exists:accounting_entities,id',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
        ]);
        
        $incomeStatement = $this->accountingService->generateIncomeStatement(
            $request->entity_id,
            $request->start_date,
            $request->end_date
        );
        
        return response()->json($incomeStatement);
    }
    
    public function balanceSheet(Request $request)
    {
        $request->validate([
            'entity_id' => 'required|exists:accounting_entities,id',
            'date' => 'nullable|date',
        ]);
        
        $balanceSheet = $this->accountingService->generateBalanceSheet(
            $request->entity_id,
            $request->date
        );
        
        return response()->json($balanceSheet);
    }
}
```

## React Frontend with Optimistic UI

Set up the React frontend with optimistic UI updates:

```jsx
// resources/js/services/api.js
import axios from 'axios';

const api = axios.create({
    baseURL: '/api',
    headers: {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
    },
});

export default api;

// resources/js/services/accountingService.js
import api from './api';

export const accountingService = {
    // Accounts
    getAccounts: (params) => api.get('/accounts', { params }),
    getAccount: (id) => api.get(`/accounts/${id}`),
    createAccount: (data) => api.post('/accounts', data),
    updateAccount: (id, data) => api.put(`/accounts/${id}`, data),
    deleteAccount: (id) => api.delete(`/accounts/${id}`),
    
    // Journals
    getJournals: (params) => api.get('/journals', { params }),
    getJournal: (id) => api.get(`/journals/${id}`),
    createJournal: (data) => api.post('/journals', data),
    
    // Reports
    getTrialBalance: (params) => api.get('/reports/trial-balance', { params }),
    getIncomeStatement: (params) => api.get('/reports/income-statement', { params }),
    getBalanceSheet: (params) => api.get('/reports/balance-sheet', { params }),
};

// resources/js/Pages/Journals/CreateJournal.jsx
import React, { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Inertia } from '@inertiajs/inertia';
import { accountingService } from '@/services/accountingService';

export default function CreateJournal({ entityId }) {
    const queryClient = useQueryClient();
    const [formData, setFormData] = useState({
        entity_id: entityId,
        reference: '',
        description: '',
        journal_date: new Date().toISOString().split('T')[0],
        entries: [
            { account_id: '', amount: '', is_credit: false },
            { account_id: '', amount: '', is_credit: true }
        ]
    });
    
    const [errors, setErrors] = useState({});
    
    // React Query mutation with optimistic updates
    const createJournalMutation = useMutation({
        mutationFn: accountingService.createJournal,
        onMutate: async (newJournal) => {
            // Cancel any outgoing refetches
            await queryClient.cancelQueries({ queryKey: ['journals'] });
            
            // Snapshot the previous value
            const previousJournals = queryClient.getQueryData(['journals', { entity_id: entityId }]);
            
            // Optimistically update to the new value
            queryClient.setQueryData(['journals', { entity_id: entityId }], old => {
                const optimisticJournal = {
                    id: 'temp-id-' + Date.now(),
                    ...newJournal,
                    created_at: new Date().toISOString(),
                };
                
                return {
                    ...old,
                    data: [optimisticJournal, ...(old?.data || [])],
                };
            });
            
            // Return a context object with the previous value
            return { previousJournals };
        },
        onError: (err, newJournal, context) => {
            // If there was an error, revert back to the previous value
            queryClient.setQueryData(['journals', { entity_id: entityId }], context.previousJournals);
            
            // Set validation errors if they exist
            if (err.response?.data?.errors) {
                setErrors(err.response.data.errors);
            }
        },
        onSuccess: () => {
            // Invalidate queries to refetch data
            queryClient.invalidateQueries({ queryKey: ['journals'] });
            queryClient.invalidateQueries({ queryKey: ['reports'] });
            
            // Reset form
            setFormData({
                entity_id: entityId,
                reference: '',
                description: '',
                journal_date: new Date().toISOString().split('T')[0],
                entries: [
                    { account_id: '', amount: '', is_credit: false },
                    { account_id: '', amount: '', is_credit: true }
                ]
            });
            
            setErrors({});
        },
    });
    
    const handleSubmit = (e) => {
        e.preventDefault();
        createJournalMutation.mutate(formData);
    };
    
    // Check if entries are balanced
    const entriesBalanced = () => {
        let total = 0;
        formData.entries.forEach(entry => {
            const amount = parseFloat(entry.amount) || 0;
            if (entry.is_credit) {
                total += amount;
            } else {
                total -= amount;
            }
        });
        return Math.abs(total) < 0.0001;
    };
    
    const addEntryRow = () => {
        setFormData({
            ...formData,
            entries: [...formData.entries, { account_id: '', amount: '', is_credit: false }]
        });
    };
    
    const removeEntryRow = (index) => {
        const entries = [...formData.entries];
        entries.splice(index, 1);
        setFormData({ ...formData, entries });
    };
    
    const updateEntry = (index, field, value) => {
        const entries = [...formData.entries];
        entries[index][field] = value;
        setFormData({ ...formData, entries });
    };
    
    return (
        <div className="p-6 bg-white rounded-lg shadow-md">
            <h2 className="text-2xl font-bold mb-6">Create Journal Entry</h2>
            
            <form onSubmit={handleSubmit}>
                {/* Form fields for reference, description, date */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Reference
                        </label>
                        <input
                            type="text"
                            value={formData.reference}
                            onChange={(e) => setFormData({...formData, reference: e.target.value})}
                            className="w-full p-2 border rounded"
                            required
                        />
                        {errors.reference && (
                            <p className="text-red-500 text-sm mt-1">{errors.reference[0]}</p>
                        )}
                    </div>
                    
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Date
                        </label>
                        <input
                            type="date"
                            value={formData.journal_date}
                            onChange={(e) => setFormData({...formData, journal_date: e.target.value})}
                            className="w-full p-2 border rounded"
                            required
                        />
                        {errors.journal_date && (
                            <p className="text-red-500 text-sm mt-1">{errors.journal_date[0]}</p>
                        )}
                    </div>
                    
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Description
                        </label>
                        <input
                            type="text"
                            value={formData.description}
                            onChange={(e) => setFormData({...formData, description: e.target.value})}
                            className="w-full p-2 border rounded"
                            required
                        />
                        {errors.description && (
                            <p className="text-red-500 text-sm mt-1">{errors.description[0]}</p>
                        )}
                    </div>
                </div>
                
                {/* Journal entries table */}
                <div className="mb-6">
                    <h3 className="text-lg font-medium mb-2">Journal Entries</h3>
                    <div className="overflow-x-auto">
                        <table className="w-full border-collapse">
                            <thead>
                                <tr className="bg-gray-100">
                                    <th className="p-2 text-left">Account</th>
                                    <th className="p-2 text-left">Debit</th>
                                    <th className="p-2 text-left">Credit</th>
                                    <th className="p-2 text-left">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {formData.entries.map((entry, index) => (
                                    <tr key={index} className="border-t">
                                        <td className="p-2">
                                            <AccountSelect
                                                value={entry.account_id}
                                                onChange={(value) => updateEntry(index, 'account_id', value)}
                                                entityId={entityId}
                                            />
                                            {errors[`entries.${index}.account_id`] && (
                                                <p className="text-red-500 text-sm mt-1">
                                                    {errors[`entries.${index}.account_id`][0]}
                                                </p>
                                            )}
                                        </td>
                                        <td className="p-2">
                                            {!entry.is_credit && (
                                                <input
                                                    type="number"
                                                    step="0.01"
                                                    value={entry.amount}
                                                    onChange={(e) => updateEntry(index, 'amount', e.target.value)}
                                                    className="w-full p-2 border rounded"
                                                    required
                                                />
                                            )}
                                        </td>
                                        <td className="p-2">
                                            {entry.is_credit && (
                                                <input
                                                    type="number"
                                                    step="0.01"
                                                    value={entry.amount}
                                                    onChange={(e) => updateEntry(index, 'amount', e.target.value)}
                                                    className="w-full p-2 border rounded"
                                                    required
                                                />
                                            )}
                                        </td>
                                        <td className="p-2">
                                            <button
                                                type="button"
                                                onClick={() => updateEntry(index, 'is_credit', !entry.is_credit)}
                                                className="mr-2 px-3 py-1 bg-blue-500 text-white rounded"
                                            >
                                                {entry.is_credit ? 'Make Debit' : 'Make Credit'}
                                            </button>
                                            
                                            {formData.entries.length > 2 && (
                                                <button
                                                    type="button"
                                                    onClick={() => removeEntryRow(index)}
                                                    className="px-3 py-1 bg-red-500 text-white rounded"
                                                >
                                                    Remove
                                                </button>
                                            )}
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                            <tfoot>
                                <tr>
                                    <td colSpan="4" className="p-2">
                                        <button
                                            type="button"
                                            onClick={addEntryRow}
                                            className="px-3 py-1 bg-green-500 text-white rounded"
                                        >
                                            Add Entry
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td colSpan="4" className="p-2">
                                        {!entriesBalanced() && (
                                            <p className="text-red-500">
                                                Warning: Entries are not balanced! Debits must equal credits.
                                            </p>
                                        )}
                                    </td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
                
                <div>
                    <button
                        type="submit"
                        disabled={!entriesBalanced() || createJournalMutation.isPending}
                        className={`px-4 py-2 rounded ${
                            !entriesBalanced() || createJournalMutation.isPending
                                ? 'bg-gray-400'
                                : 'bg-blue-600 hover:bg-blue-700'
                        } text-white`}
                    >
                        {createJournalMutation.isPending ? 'Creating...' : 'Create Journal Entry'}
                    </button>
                </div>
            </form>
        </div>
    );
}

// Account Select Component with React Query
const AccountSelect = ({ value, onChange, entityId }) => {
    const { data, isLoading, error } = useQuery({
        queryKey: ['accounts', { entity_id: entityId }],
        queryFn: () => accountingService.getAccounts({ entity_id: entityId }),
    });
    
    if (isLoading) return <div>Loading accounts...</div>;
    if (error) return <div>Error loading accounts: {error.message}</div>;
    
    return (
        <select
            value={value}
            onChange={(e) => onChange(e.target.value)}
            className="w-full p-2 border rounded"
            required
        >
            <option value="">Select an account</option>
            {data?.data.map((account) => (
                <option key={account.id} value={account.id}>
                    {account.code} - {account.name}
                </option>
            ))}
        </select>
    );
};
```

## MySQL Optimization Strategies

Add these MySQL optimizations to improve performance with large datasets:

```sql
-- Create indexes for frequently accessed data
CREATE INDEX idx_journal_entries_composite ON journal_entries (journal_id, account_id, is_credit);
CREATE INDEX idx_journals_date_entity ON journals (journal_date, entity_id);

-- Optimize MySQL configuration in my.cnf
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT
max_connections = 200
table_open_cache = 2000
query_cache_size = 0

-- Create partitions for large tables
ALTER TABLE journals
PARTITION BY RANGE (YEAR(journal_date)) (
    PARTITION p2022 VALUES LESS THAN (2023),
    PARTITION p2023 VALUES LESS THAN (2024),
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026),
    PARTITION future VALUES LESS THAN MAXVALUE
);

-- Create materialized view for account balances
CREATE TABLE account_balances_materialized (
    account_id BIGINT UNSIGNED PRIMARY KEY,
    debit_total DECIMAL(13, 4) DEFAULT 0,
    credit_total DECIMAL(13, 4) DEFAULT 0,
    balance DECIMAL(13, 4) DEFAULT 0,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create procedure to refresh materialized view
DELIMITER //
CREATE PROCEDURE refresh_account_balances()
BEGIN
    DELETE FROM account_balances_materialized;
    
    INSERT INTO account_balances_materialized(account_id, debit_total, credit_total, balance)
    SELECT 
        account_id,
        SUM(CASE WHEN is_credit = 0 THEN amount ELSE 0 END) as debit_total,
        SUM(CASE WHEN is_credit = 1 THEN amount ELSE 0 END) as credit_total,
        SUM(CASE WHEN is_credit = 0 THEN amount ELSE -amount END) as balance
    FROM journal_entries
    GROUP BY account_id;
    
    UPDATE account_balances_materialized SET last_updated = CURRENT_TIMESTAMP;
END //
DELIMITER ;

-- Schedule the procedure to run regularly
SET GLOBAL event_scheduler = ON;

DELIMITER //
CREATE EVENT refresh_account_balances_daily
ON SCHEDULE EVERY 1 DAY
DO
BEGIN
    CALL refresh_account_balances();
END //
DELIMITER ;
```

## Optimized Background Processing

For large transaction volumes, implement background processing:

```php
// app/Jobs/ProcessBulkTransactionsJob.php
namespace App\Jobs;

use App\Models\Journal;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;

class ProcessBulkTransactionsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $transactions;
    
    public function __construct(array $transactions)
    {
        $this->transactions = $transactions;
    }
    
    public function handle()
    {
        DB::transaction(function () {
            foreach ($this->transactions as $transaction) {
                $journal = Journal::create([
                    'entity_id' => $transaction['entity_id'],
                    'reference' => $transaction['reference'],
                    'description' => $transaction['description'],
                    'journal_date' => $transaction['journal_date'],
                ]);
                
                $journalEntries = [];
                foreach ($transaction['entries'] as $entry) {
                    $journalEntries[] = [
                        'journal_id' => $journal->id,
                        'account_id' => $entry['account_id'],
                        'amount' => $entry['amount'],
                        'is_credit' => $entry['is_credit'],
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];
                }
                
                // Bulk insert for better performance
                DB::table('journal_entries')->insert($journalEntries);
            }
        });
    }
}

// Usage in a controller
public function bulkImport(Request $request)
{
    $transactions = $request->validate([
        'transactions' => 'required|array',
        'transactions.*.entity_id' => 'required|exists:accounting_entities,id',
        'transactions.*.reference' => 'required|string',
        'transactions.*.description' => 'required|string',
        'transactions.*.journal_date' => 'required|date',
        'transactions.*.entries' => 'required|array|min:2',
        'transactions.*.entries.*.account_id' => 'required|exists:accounts,id',
        'transactions.*.entries.*.amount' => 'required|numeric|min:0.01',
        'transactions.*.entries.*.is_credit' => 'required|boolean',
    ]);
    
    // Validate that each journal balances
    foreach ($transactions['transactions'] as $transaction) {
        $total = 0;
        foreach ($transaction['entries'] as $entry) {
            if ($entry['is_credit']) {
                $total += $entry['amount'];
            } else {
                $total -= $entry['amount'];
            }
        }
        
        if (abs($total) > 0.0001) {
            return response()->json([
                'message' => "Journal entries for {$transaction['reference']} must balance. Current imbalance: {$total}",
            ], 422);
        }
    }
    
    // Process in background
    ProcessBulkTransactionsJob::dispatch($transactions['transactions']);
    
    return response()->json(['message' => 'Transactions queued for processing']);
}
```

## Deployment Considerations

Optimize your Laravel application for production:

```bash
# Optimize Composer autoloader
composer install --optimize-autoloader --no-dev

# Cache configuration
php artisan config:cache

# Cache routes
php artisan route:cache

# Precompile views
php artisan view:cache

# Optimize asset bundling
npm run build

# MySQL optimizations
sudo sysctl -w vm.swappiness=10

# Set up Redis for caching and queue
composer require predis/predis
```

Update your `.env` file for production:

```
APP_ENV=production
APP_DEBUG=false

CACHE_DRIVER=redis
QUEUE_CONNECTION=redis
SESSION_DRIVER=redis

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379
```

## Monitoring and Optimization

Implement performance monitoring:

```php
// app/Providers/AppServiceProvider.php
namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AppServiceProvider extends ServiceProvider
{
    public function boot()
    {
        if (app()->environment('local')) {
            // Log slow queries
            DB::listen(function ($query) {
                if ($query->time > 100) {
                    Log::channel('slow-queries')->warning(
                        'Slow query detected',
                        [
                            'sql' => $query->sql,
                            'bindings' => $query->bindings,
                            'time' => $query->time,
                        ]
                    );
                }
            });
        }
    }
}
```

## Conclusion

This implementation guide provides a practical approach to building a financial double accounting system using Laravel 12, React, and MySQL optimizations. The solution is designed to handle large volumes of financial data while maintaining performance and user experience.

Key aspects of this implementation include:
1. Optimized database design with proper indexing and partitioning
2. Service-based architecture for better code organization and maintainability
3. Optimistic UI updates for better user experience
4. Background processing for handling large transaction volumes
5. Caching strategies for frequently accessed financial reports
6. MySQL optimization techniques for large datasets

By following these patterns and implementing the code provided, you can create a robust and scalable financial accounting system capable of handling enterprise-level workloads.