<?php

namespace App\Http\Controllers\ChequeEntry;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use App\Models\Account;
use App\Models\Cheque;
use App\Models\ChqRefBank;
use App\Models\JournalEntry;
use App\Models\PaymentType;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;

/**
 * @group Cheque Management
 * 
 * Handles all cheque entry operations including creation, updates, and status changes.
 * 
 * ### Status Flow:
 * - **pending**: Initial status when a cheque is created
 * - **ok**: When cheque is cleared/processed
 * - **returned**: When cheque is returned/bounced
 * 
 * ### Journal Entry Rules:
 * - **pending → ok**: Creates journal entries (debit to_account, credit from_account)
 * - **ok → returned**: Creates return journal entries (reverses original entries)
 * - **pending → returned**: Only updates status, no journal entries
 * 
 * ### Permissions:
 * - Only users with 'Admin' or 'Editor' role can modify cheques
 * - All users can view cheque lists
 * 
 * @authenticated
 */
class ChequeController extends Controller
{
    /**
     * Display the cheque entry form
     * 
     * @response 200 {
     *   "inertia": "cheque/Entry"
     * }
     * 
     * @return \Inertia\Response
     */
    public function entry()
    {
        return inertia('cheque/Entry');
    }

    /**
     * Store a new cheque entry
     * 
     * @bodyParam entry_type string required Entry type (inward/outward)
     * @bodyParam entry_date string required Date of entry (YYYY-MM-DD)
     * @bodyParam cheque_date string required Date on cheque (YYYY-MM-DD)
     * @bodyParam posting_date string Date of posting (YYYY-MM-DD)
     * @bodyParam from_account_id integer required ID of source account
     * @bodyParam to_account_id integer required ID of destination account
     * @bodyParam amount numeric required Cheque amount (must be > 0)
     * @bodyParam chq_ref_bank_id integer required ID of reference bank
     * @bodyParam chq_no string required Cheque number (unique per bank)
     * @bodyParam status string Status (pending/ok/returned), defaults to 'pending'
     * 
     * @response 201 {
     *   "message": "Cheque entry created successfully",
     *   "data": {cheque object}
     * }
     * @response 403 {"message": "Unauthorized"}
     * @response 422 {"message": "Validation errors", "errors": {...}}
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        // Only allow Admin or Editor
        if (!Auth::check() || !in_array(Auth::user()->role, ['Admin', 'Editor'])) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validated = $request->validate([
            'entry_type' => 'required|in:inward,outward',
            'entry_date' => 'required|date',
            'cheque_date' => [
                'required',
                'date',
                function ($attribute, $value, $fail) use ($request) {
                    $exists = Cheque::where('cheque_date', $value)
                        ->where('chq_ref_bank_id', $request->chq_ref_bank_id)
                        ->where('chq_no', $request->chq_no)
                        ->where('amount', $request->amount)
                        ->exists();
                    
                    if ($exists) {
                        $fail('A cheque with this date, bank, number, and amount already exists.');
                    }
                },
            ],
            'posting_date' => 'nullable|date',
            'from_account_id' => 'required|exists:accounts,id',
            'to_account_id' => 'required|exists:accounts,id',
            'amount' => 'required|numeric|min:0.01',
            'chq_ref_bank_id' => 'required|exists:chq_ref_banks,id',
            'chq_no' => 'required|string|max:100',
            'status' => 'nullable|in:pending,ok,returned',
        ]);

        DB::beginTransaction();

        try {
            $cheque = Cheque::create($validated);
            DB::commit();

            return response()->json([
                'message' => 'Cheque entry created successfully.', 
                'data' => $cheque->load(['fromAccount', 'toAccount', 'chqRefBank'])
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Cheque store error: ' . $e->getMessage());

            return response()->json([
                'message' => 'Failed to create cheque entry.', 
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function index(Request $request)
    {
        $query = Cheque::with(['fromAccount', 'toAccount', 'chqRefBank']);

        if ($request->filled('from_cheque_date')) {
            $query->where('cheque_date', '>=', $request->from_cheque_date);
        }

        if ($request->filled('to_cheque_date')) {
            $query->where('cheque_date', '<=', $request->to_cheque_date);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('entry_type')) {
            $query->where('entry_type', $request->entry_type);
        }

        if ($request->filled('from_account_id')) {
            $query->where('from_account_id', $request->from_account_id);
        }

        if ($request->filled('to_account_id')) {
            $query->where('to_account_id', $request->to_account_id);
        }

        $cheques = $query->orderBy('cheque_date')->get();

        // Calculate days remaining
        $cheques->transform(function ($cheque) {
            $chequeDate = \Carbon\Carbon::parse($cheque->cheque_date)->startOfDay();
            $currentDate = now()->startOfDay();

            // Calculate as cheque_date - current_date
            // Positive: days remaining in future
            // Negative: days past (overdue)
            $cheque->days_remaining = (int) $chequeDate->diffInDays($currentDate, false);

            return $cheque;
        });

        if ($request->wantsJson()) {
            return response()->json($cheques);
        }

        // For Inertia request, just render the page. Data will be fetched via API.
        return inertia('cheque/List');
    }

    public function show(Request $request, Cheque $cheque_entry): JsonResponse
    {
        // Ensure relations are loaded if needed by the API response
        $cheque_entry->load(['fromAccount', 'toAccount', 'chqRefBank']);

        return response()->json($cheque_entry);
    }

    public function edit(Cheque $cheque) // This $cheque is from the web route parameter
    {
        // Only allow Admin or Editor
        if (!Auth::check() || !in_array(Auth::user()->role, ['Admin', 'Editor'])) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        // Pass only the ID; frontend will fetch details via API
        return inertia('cheque/Edit', ['chequeId' => $cheque->id]);
    }

    public function update(Request $request, Cheque $cheque_entry)
    {
        // Only allow Admin or Editor
        if (!Auth::check() || !in_array(Auth::user()->role, ['Admin', 'Editor'])) {
            if ($request->wantsJson()) {
                return response()->json(['message' => 'Unauthorized'], 403);
            }
            return redirect()->back()->with('error', 'Unauthorized');
        }

        $validated = $request->validate([
            'entry_type' => 'required|in:inward,outward',
            'entry_date' => 'required|date',
            'cheque_date' => [
                'required',
                'date',
                function ($attribute, $value, $fail) use ($request, $cheque_entry) {
                    $exists = Cheque::where('cheque_date', $value)
                        ->where('chq_ref_bank_id', $request->chq_ref_bank_id)
                        ->where('chq_no', $request->chq_no)
                        ->where('amount', $request->amount)
                        ->where('id', '!=', $cheque_entry->id)
                        ->exists();
                    
                    if ($exists) {
                        $fail('A cheque with this date, bank, number, and amount already exists.');
                    }
                },
            ],
            'posting_date' => 'nullable|date',
            'from_account_id' => 'required|exists:accounts,id',
            'to_account_id' => 'required|exists:accounts,id',
            'amount' => 'required|numeric|min:0.01',
            'chq_ref_bank_id' => 'required|exists:chq_ref_banks,id',
            'chq_no' => 'required|string|max:100',
            'status' => 'required|in:pending,ok,returned',
        ]);

        // Validate status transitions
        if ($cheque_entry->status === 'ok' && $validated['status'] === 'pending') {
            // For API, return JSON error
            if ($request->wantsJson()) {
                return response()->json(['message' => 'Cannot change status from OK back to Pending.'], 422);
            }
            return back()->withErrors('Cannot change status from OK back to Pending.')->withInput();
        }

        if ($cheque_entry->status === 'returned' && $validated['status'] !== 'returned') {
            // For API, return JSON error
            if ($request->wantsJson()) {
                return response()->json(['message' => 'Cannot change status from Returned to other status.'], 422);
            }
            return back()->withErrors('Cannot change status from Returned to other status.')->withInput();
        }

        DB::beginTransaction();
        
        try {

            // Update the cheque entry
            $oldStatus = $cheque_entry->status;
            $cheque_entry->update($validated);

            // Handle status changes and journal entries
            if ($validated['status'] === 'ok' && $oldStatus === 'pending') {
                // Only create journal entries when changing from pending to ok
                $this->createJournalEntries($cheque_entry);
            } elseif ($validated['status'] === 'returned' && $oldStatus === 'ok') {
                // Only create return journal entries when changing from ok to returned
                $this->createReturnJournalEntries($cheque_entry);
            }
            // No journal entries for pending -> returned transition


            DB::commit();

            // Load relationships for response
            $cheque_entry->load(['fromAccount', 'toAccount', 'chqRefBank']);
            
            // Return appropriate response based on request type
            if ($request->wantsJson()) {
                return response()->json([
                    'message' => 'Cheque entry updated successfully.', 
                    'data' => $cheque_entry
                ]);
            }
            
            // For web requests, redirect back to list with success message
            return redirect()->route('cheque.list')
                ->with('success', 'Cheque entry updated successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Cheque update error: ' . $e->getMessage());
            
            if ($request->wantsJson()) {
                return response()->json([
                    'message' => 'Failed to update cheque entry.', 
                    'error' => $e->getMessage()
                ], 500);
                return response()->json(['message' => 'Failed to update cheque entry.', 'error' => $e->getMessage()], 500);
            }

            return back()->withErrors('Failed to update cheque entry.')->withInput();
        }
    }

    public function destroy(Request $request, Cheque $cheque_entry) // Added Request $request for wantsJson()
    {
        // Only allow Admin
        if (!Auth::check() || !in_array(Auth::user()->role, ['Admin'])) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        DB::beginTransaction();
        try {
            // Add logic here if journal entries need to be reversed or handled upon deletion
            // For example, if a 'ok' cheque is deleted, should its journal entries be voided?
            // This depends on business logic. For now, just deleting the cheque.

            $cheque_entry->delete();
            DB::commit();
    
            return response()->json(null, 204);
    
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Cheque delete error: ' . $e->getMessage());
            
            return response()->json([
                'message' => 'Failed to delete cheque entry.', 
                'error' => $e->getMessage()
            ], 500);
        }
    }

    

    /**
     * Get the last sequence number for today's transactions.
     */
    private function getLastSequenceNumber(): int
    {
        $lastEntry = JournalEntry::latest('id')->first();
        
        if (!$lastEntry) {
            return 0;
        }
        
        // Extract the numeric part from the TID (last 4 digits)
        $lastTID = $lastEntry->TID;
        $sequence = (int) substr($lastTID, -4);
        
        return $sequence;
    }

    /**
     * Generate a unique transaction number based on a sequence number.
     */
    private function generateTransactionNumber(int $sequence): string
    {
        return now()->format('ym') . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    // Create journal entries for a cheque when status is updated to 'ok'
    /**
     * Create journal entries for a cleared cheque
     * 
     * Creates debit and credit entries with the following details:
     * - Debits the to_account (money received)
     * - Credits the from_account (money taken from)
     * - Uses 'cheque' as payment type
     * - Includes cheque reference in description
     * 
     * @param Cheque $cheque_entry The cheque entry to create journal entries for
     * @throws \Exception If journal entry creation fails
     * @return void
     */
    private function createJournalEntries(Cheque $cheque_entry)
    {
        try {
            // Get the last sequence number and generate TID
            $lastSequence = $this->getLastSequenceNumber();
            $transactionNumber = $this->generateTransactionNumber($lastSequence + 1);
            
            // Get the payment type 'cheque'
            $paymentType = PaymentType::where('name', 'cheque')->firstOrFail();
            
            // Get chq_ref_bank details for the description
            $chqRefBank = ChqRefBank::findOrFail($cheque_entry->chq_ref_bank_id);
            
            // Create description with chq_ref_bank name
            $description = "Cheque #{$cheque_entry->chq_no} - {$chqRefBank->name}";
            
            // Use cheque_date as specified in requirements
            $journalDate = $cheque_entry->cheque_date;

            // First entry: Credit the from_account
            JournalEntry::create([
                'TID' => $transactionNumber,
                'date' => $journalDate,
                'payment_type_id' => $paymentType->id,
                'account_id' => $cheque_entry->from_account_id,
                'is_credit' => true,
                'description' => $description,
                'chq_no' => $cheque_entry->chq_no,
                'amount' => -$cheque_entry->amount, // Negative amount for credit
                'created_by' => auth()->id(),
            ]);

            // Second entry: Debit the to_account
            JournalEntry::create([
                'TID' => $transactionNumber,
                'date' => $journalDate,
                'payment_type_id' => $paymentType->id,
                'account_id' => $cheque_entry->to_account_id,
                'is_credit' => false,
                'description' => $description,
                'chq_no' => $cheque_entry->chq_no,
                'amount' => $cheque_entry->amount, // Positive amount for debit
                'created_by' => auth()->id(),
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to create journal entries for cheque: ' . $e->getMessage(), [
                'cheque_id' => $cheque_entry->id,
                'error' => $e
            ]);
            throw $e; // This will trigger the transaction rollback
        }
    }

    // Create journal entries for a cheque when status is updated to 'returned'
    /**
     * Create return journal entries for a bounced/returned cheque
     * 
     * Creates entries that reverse the original journal entries:
     * - Credits the to_account (money returned)
     * - Debits the from_account (money given back)
     * - Uses 'cheque_returned' as payment type
     * 
     * @param Cheque $cheque_entry The cheque entry to create return entries for
     * @throws \Exception If journal entry creation fails
     * @return void
     */
    private function createReturnJournalEntries(Cheque $cheque_entry)
    {
        DB::beginTransaction();
        
        try {
            // Get the last sequence number and generate TID
            $lastSequence = $this->getLastSequenceNumber();
            $transactionNumber = $this->generateTransactionNumber($lastSequence + 1);
            
            // Get the payment type 'cheque_returned'
            $paymentType = PaymentType::where('name', 'cheque_returned')->firstOrFail();
            
            // Get chq_ref_bank details for the description
            $chqRefBank = ChqRefBank::findOrFail($cheque_entry->chq_ref_bank_id);
            
            // Create description with chq_ref_bank name
            $description = "Cheque Returned #{$cheque_entry->chq_no} - {$chqRefBank->name}";
            
            // Use cheque_date as specified in requirements
            $journalDate = $cheque_entry->cheque_date;

            // First entry: Debit the from_account (money is being returned to this account)
            JournalEntry::create([
                'TID' => $transactionNumber,
                'date' => $journalDate,
                'payment_type_id' => $paymentType->id,
                'account_id' => $cheque_entry->from_account_id,
                'is_credit' => false, // Debit
                'description' => $description,
                'chq_no' => $cheque_entry->chq_no,
                'amount' => $cheque_entry->amount, // Positive for debit
                'created_by' => auth()->id(),
            ]);

            // Second entry: Credit the to_account (money is being taken back from this account)
            JournalEntry::create([
                'TID' => $transactionNumber,
                'date' => $journalDate,
                'payment_type_id' => $paymentType->id,
                'account_id' => $cheque_entry->to_account_id,
                'is_credit' => true, // Credit
                'description' => $description,
                'chq_no' => $cheque_entry->chq_no,
                'amount' => -$cheque_entry->amount, // Negative for credit
                'created_by' => auth()->id(),
            ]);

            DB::commit();

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to create return journal entries for cheque: ' . $e->getMessage(), [
                'cheque_id' => $cheque_entry->id,
                'error' => $e
            ]);
            throw $e;
        }
    }

    /**
     * Store multiple cheque entries
     * 
     * @bodyParam entries array required Array of cheque entries
     * @bodyParam entries.*.entry_type string required Entry type (inward/outward)
     * @bodyParam entries.*.entry_date string required Date of entry (YYYY-MM-DD)
     * @bodyParam entries.*.cheque_date string required Date on cheque (YYYY-MM-DD)
     * @bodyParam entries.*.from_account_id integer required ID of source account
     * @bodyParam entries.*.to_account_id integer required ID of destination account
     * @bodyParam entries.*.amount numeric required Cheque amount (must be > 0)
     * @bodyParam entries.*.chq_ref_bank_id integer required ID of reference bank
     * @bodyParam entries.*.chq_no string required Cheque number (unique per bank)
     * @bodyParam entries.*.status string Status (pending/ok/returned), defaults to 'pending'
     * 
     * @response 201 {
     *   "message": "Cheque entries created successfully",
     *   "data": {count: number, entries: array}
     * }
     * @response 403 {"message": "Unauthorized"}
     * @response 422 {"message": "Validation errors", "errors": {...}}
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function storeMultiple(Request $request): JsonResponse
    {
        // Only allow Admin or Editor
        if (!Auth::check() || !in_array(Auth::user()->role, ['Admin', 'Editor'])) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validated = $request->validate([
            'entries' => 'required|array|min:1',
            'entries.*.entry_type' => 'required|in:inward,outward',
            'entries.*.entry_date' => 'required|date',
            'entries.*.cheque_date' => 'required|date',
            'entries.*.from_account_id' => 'required|exists:accounts,id',
            'entries.*.to_account_id' => 'required|exists:accounts,id',
            'entries.*.amount' => 'required|numeric|min:0.01',
            'entries.*.chq_ref_bank_id' => 'required|exists:chq_ref_banks,id',
            'entries.*.chq_no' => 'required|string|max:100',
            'entries.*.status' => 'nullable|in:pending,ok,returned',
        ]);

        // Additional validation for unique cheque per date, bank, and number
        $entries = collect($validated['entries']);
        $duplicateCheques = [];
        $seenCheques = [];
        
        // First check for duplicates within the current request
        foreach ($entries as $index => $entry) {
            $key = $entry['cheque_date'] . '|' . $entry['chq_ref_bank_id'] . '|' . $entry['chq_no'] . '|' . $entry['amount'];
            
            if (in_array($key, $seenCheques)) {
                $duplicateCheques[] = "Row " . ($index + 1) . ": Duplicate cheque #{$entry['chq_no']} for the same date and bank with the same amount in this request";
            } else {
                $seenCheques[] = $key;
            }
        }
        
        if (count($duplicateCheques) > 0) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => ['entries' => $duplicateCheques]
            ], 422);
        }
        
        // Then check for duplicates in the database
        $duplicateCheques = [];
        
        foreach ($entries as $index => $entry) {
            $exists = Cheque::where('cheque_date', $entry['cheque_date'])
                ->where('chq_ref_bank_id', $entry['chq_ref_bank_id'])
                ->where('chq_no', $entry['chq_no'])
                ->where('amount', $entry['amount'])
                ->exists();
                
            if ($exists) {
                $duplicateCheques[] = "Row " . ($index + 1) . ": Cheque #{$entry['chq_no']} already exists for the selected date and bank with the same amount";
            }
        }
        
        if (count($duplicateCheques) > 0) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => ['entries' => $duplicateCheques]
            ], 422);
        }

        DB::beginTransaction();
        $createdEntries = [];
        
        try {
            foreach ($entries as $entry) {
                // Convert dates to the application's timezone before saving
                $entryDate = \Carbon\Carbon::parse($entry['entry_date'])->setTimezone(config('app.timezone'))->format('Y-m-d');
                $chequeDate = \Carbon\Carbon::parse($entry['cheque_date'])->setTimezone(config('app.timezone'))->format('Y-m-d');
                
                $cheque = Cheque::create([
                    'entry_type' => $entry['entry_type'],
                    'entry_date' => $entryDate,
                    'cheque_date' => $chequeDate,
                    'from_account_id' => $entry['from_account_id'],
                    'to_account_id' => $entry['to_account_id'],
                    'amount' => $entry['amount'],
                    'chq_ref_bank_id' => $entry['chq_ref_bank_id'],
                    'chq_no' => $entry['chq_no'],
                    'status' => 'pending',
                ]);
                
                $createdEntries[] = $cheque->load(['fromAccount', 'toAccount', 'chqRefBank']);
            }
            
            DB::commit();
            
            return response()->json([
                'message' => 'Cheque entries created successfully',
                'data' => [
                    'count' => count($createdEntries),
                    'entries' => $createdEntries
                ]
            ], 201);
            
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to create multiple cheque entries: ' . $e->getMessage(), [
                'error' => $e
            ]);
            
            return response()->json([
                'message' => 'Failed to create cheque entries',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
