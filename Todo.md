## Project Status Summary

- All sensitive pages and API endpoints (Daily Book, Journal Entry, User Management) are now double-protected (frontend and backend role checks).
- Custom 403 and 404 error pages are implemented for both web and API responses.
- Daily Book and Journal Entry (single/multiple) modules are complete and secure.
- User Management is Admin-only and fully protected.
- Table skeleton loading is standardized across all data tables.
- API/auth issues and route order bugs are resolved.

## Current Tasks

- [x] Create API Documentation page (`resources/js/Pages/ApiDocumentation.tsx`)
    - [x] Use `AppLayout`.
    - [x] Implement two-column layout (left nav, right content).
    - [x] Add route in `routes/web.php`.
    - [x] Update link in `user-menu-content.tsx`.
    - [x] Delete `api_documentation.md`.

## Completed Tasks
- Todo List Feature
- Create Account Page
- List Accounts Page
- Edit Account Page
- Account Management


# Todo List

- [x] Create API documentation file and link it in the user menu.

- [x] Fix unique validation rule in AccountController update method.

## Backend - Account Controller Authorization
- [x] Refine `AccountController` authorization logic (`store`, `update`, `destroy`).
- [x] Implement specific JSON error messages for unauthorized actions (401/403).
- [x] Ensure `Viewer` role cannot perform create/update/delete.
- [x] Ensure only `Admin` can delete.
- [x] Ensure `Admin` and `Editor` can create/update.
- [x] Correct unique name validation in `update` method.

## Registration Page - Implement User Roles

- [x] Add a 'role' column to the `users` table in the database.
- [x] Create a database migration (`2025_04_23_154507_add_role_to_users_table.php`) to update the `users` table schema.
- [x] Update the User model (`app/Models/User.php`) to include the 'role' attribute.
- [x] Modify the registration controller (`RegisteredUserController.php`) to assign a default role ('Viewer') on standard registration.
- [x] Create a database seeder (`UserSeeder.php`) to add initial Admin/Editor users.
- [x] Call `UserSeeder` from `DatabaseSeeder.php`.
- [x] Run database seeder.
- [x] Update `RegisteredUserController@create` to pass `canAssignRole` and `availableRoles` props to the view.
- [x] Update `RegisteredUserController@store` to validate the role and assign it based on Admin privileges.
- [x] Modify the registration view (`resources/js/pages/auth/register.tsx`) to conditionally show a role selection dropdown for Admins.
- [x] Consider adding role-based access control (RBAC) middleware or logic for different parts of the application.
- [x] Create an Admin Panel for user management (deferred).

## Feature: Control Public Registration
- [x] Add `ALLOW_PUBLIC_REGISTRATION` variable to `.env` and `.env.example`.
- [x] Update `routes/auth.php` to conditionally disable registration routes based on `ALLOW_PUBLIC_REGISTRATION`.
- [x] Update `RegisteredUserController@create` to check `ALLOW_PUBLIC_REGISTRATION`.

## Feature: Login with Email or Username
- [x] Update `AuthenticatedSessionController@store` to handle login with either email or username.
- [x] Update `resources/js/Pages/Auth/Login.tsx` to reflect the ability to log in with email or username.

## Frontend - Account Error Handling
- [x] Fix display of specific validation errors (e.g., 'name already taken') on account create/update form.
  - [x] Located relevant frontend component (`resources/js/pages/accounts/index.tsx`).
  - [x] Refined `handleSubmit` error handling multiple times to parse Axios errors.
  - [x] Added console logging to inspect received error structure.
  - [x] Verify error structure in browser console and adjust parsing if needed.
  - [x] Ensure `toast.error` displays the extracted specific message.
- [x] Implement table skeleton loader so that table headings always show instantly and skeletons appear only in the table body cells during loading, not over the entire table. Skeleton cells use className="px-4 py-3" and a Skeleton with h-5 w-full rounded. This is now the standard for all data tables.

## Feature: Admin User Creation
- [ ] Add `username` column to `users` table migration and model.
- [ ] Update `RegisteredUserController@store` to handle `username` and `role` fields.
- [ ] Create route for admin user creation page (e.g., `/admin/users/create`).
- [ ] Create `UserController` with `create` and `store` methods for admin user creation.
- [x] Create `resources/js/Pages/Admin/Users/<USER>/users.tsx`)
- [x] Create `create-user-form.tsx` component for the user creation form.
- [x] Create backend route/controller for admin user creation.
- [x] Ensure only Admins can access the user creation route/page.
- [x] Implement toast notifications for user creation (success & error) using Sonner.
- [x] Implement modal close or form clear on successful user creation in settings/users.tsx (admin panel). Modal remains open on error.

## UI/UX Improvements & Admin Features
- [x] Install official shadcn/ui Command and Popover components using CLI
- [x] Create official Combobox implementation using shadcn/ui docs
- [x] Integrate new Combobox into user management forms and test for errors
- [x] Refactor user creation form layout (Name/Username, Email/Role, Password, Confirm Password)
- [x] Implement Optimistic UI Updates for Dashboard Todo List
    - [x] Add Todo
    - [x] Update Todo
    - [x] Toggle Todo Completion
    - [x] Delete Todo
- [ ] Verify all imports and usage match shadcn/ui documentation
- [ ] Refactor or remove any legacy/incorrect component code
- [ ] Replace all dropdowns/selects for roles with a ComboBox (searchable dropdown) component. Make it reusable for the whole project.
- [ ] Profile Page: Show username (read-only), only allow name to be changed.
- [ ] User Management: Show all users in a table/list.
    - [ ] Allow admin to edit user's name, username, and role (ComboBox for role).
    - [ ] Allow admin to change user's password.
    - [ ] Open the create-user form in a dialog/modal (not inline).
- [ ] Use context7 for best practices for ComboBox and editable tables in React.
- [x] Add a flex container to the AppSidebarHeader so the breadcrumb is on the left and the ThemeToggle is on the right in the header bar for all pages using the sidebar layout.
- [x] Add the ThemeToggle to the right side of the header bar for dashboard and settings pages by updating AppSidebarHeader, not by adding extra divs in each page.
- [x] Transform home page into SaaS landing page for Advanced Double Accounting System
- [x] Show 'Get Started Free', 'Start Free Trial', and 'Login' buttons only when user is not logged in; show 'Go to Dashboard' when logged in (in hero and CTA sections)
- [x] Animate Call to Action section (staggered fade/slide-in)
- [x] Animate Footer section (fade/slide-in)
- [x] Animate Header and Hero section (staggered fade/slide-in)

## Sidebar/Nav Refactor
- [x] Refactor NavMain to accept a dynamic items prop and render menus using items.map, removing hardcoded menu logic.
- [ ] Test navigation in the UI to confirm menu renders as expected.
- [ ] If you want to add more menus or sections, simply update the mainNavItems array in AppSidebar.

## User Management Table Enhancements (Settings Page)
- [x] User Management Table: Edit, Delete, and Modal Dialogs
  - [x] Edit modal: Cancel & Update buttons work
  - [x] Instant table refresh after update/delete
  - [x] Error display: field highlighting, toast for errors
  - [x] UI polish: loading spinners, button disable, modal layout
- [x] Display all users in a table with actions.
- [x] Add Edit and Delete icons to each user row.
- [x] Implement inline or modal editor for name, username, role, and password (password change inside editor).
- [x] Use ComboBox for role selection in editor.
- [x] Show toasts for update/delete success or error.
- [x] Confirm before deleting a user.
- [x] Hook up API endpoints for update and delete actions (Inertia PUT/DELETE).
- [x] Disable actions while processing.
- [x] Prevent self-deletion in user management: Show sonner toast 'You cannot delete yourself.' if user attempts to delete themselves.
- [x] Implement Optimistic UI Updates for User Management (Create, Edit, Delete)
    - [x] Refactored `users.tsx` to handle form state and submission logic.
    - [x] Refactored `CreateUserForm` and `UserEditForm` into controlled components.
    - [x] Delete: Optimistic removal, reverts on error.
    - [x] Edit: Optimistic update, reverts on error (keeps modal closed).
    - [x] Create: Optimistic add (temp item), reverts on error (reopens modal).

## Accounts Management
- [x] Create List Accounts page with shadcn DataTable and modal for account creation
- [x] Add Accounts item to sidebar menu
- [x] Add Inertia route for /accounts in Laravel web.php
- [x] Create database tables for account_types and accounts (schema.sql and migration).

## Accounts Backend CRUD Implementation

- [x] Enhance error message for duplicate account names during creation (Handled by default Laravel validation).

- [ ] Create Eloquent models for AccountType and Account, with relationships.
- [x] Create AccountTypeController (index).
- [x] Create AccountController (index, store, update, destroy).
- [x] Add API/web routes for account types and accounts CRUD.
- [x] Add validation for account creation/updating (dynamic per type).
- [x] Migrate database and seed account_types.
- [x] Connect frontend to backend (fetch, create, update, delete).
- [x] Write tests for all endpoints.
- [ ] Update documentation.

## Accounts Page Dynamic UI
- [x] Design a dynamic Accounts page UI with tabs for each account type, dynamic columns, dummy data, and an add-new modal form using shadcn/ui components.
- [x] Integrate accounts page with backend using axios for CRUD (fetch, create, edit, delete).
- [x] Remove dummy data from frontend and use backend data.
- [x] Add loading states and error handling for accounts CRUD UI.
- [x] Implement API/backend logic for CRUD operations on accounts.
- [x] Polish UI/UX and validate with real users.

## Bug Fixes
- [x] ComboBox (role dropdown) now opens and is styled as per shadcn/ui docs. Layout and min-width fixed.
- [x] ThemeToggle in Header and in Settings Page Appearance both conflict with each other, Forexample we choose ligt from Appearance  and then we go any other page and change from Breadcrumb ThemeToggle to dark, then we go back to Settings Page Appearance it changes to light automatically because we chosen Light from there and versa it should work and keep that either we chose from Toggle or Appearance page must be same or both saving seperately I think and then thats why they conflict with each other.
- [x] Move API endpoints for accounts/account-types to api.php and secure with sanctum.
- [x] Remove API endpoints from web.php, keep only Inertia page route for /accounts.
- [x] Fix react-table ColumnDef typing and usage for v8+ (use id/accessorKey).
- [x] Remember to always separate API and Inertia routes and to use correct react-table types in future projects.
- [x] Fix: Make formula field conditionally required only for Currency accounts in AccountController update method (Bank accounts should not require formula)
- [ ] Review all account type-specific validation rules for other possible issues
- [x] Fixed User Management delete action causing full page reload (changed controller response from `back()` to `response()->noContent()`).
- [x] Fixed User Management delete confirmation dialog Cancel button (used `<DialogClose>`).

## [2025-04-27] Currency & Account Modal/Table Refactor

### Goal
- Ensure correct data source and modal behavior for all account types and currency management.

### Tasks

1. [x] **Unify Modal Logic**
    - Use a single modal (triggered by "Create New Account") for all account types, including currency.
    - Modal fields should dynamically adjust based on selected account type.
    - When account type is 'currency', modal fields should match the currency model (name, code only).
    - For all others, modal fields should match the account model.

2. [x] **Correct Data Fetching**
    - For all account types except 'currency', fetch and display data from the accounts table.
    - For 'currency', fetch and display data from the currencies table.
    - [x] Ensure useEffect logic fetches from /api/currencies for 'currency' and /api/accounts for others.
    - [x] Map fetched data to correct display columns.
    - [x] Review and optimize currency_account data display (formula/currency columns).

3. [x] **Correct Data Submission**
    - On modal submit, if account type is 'currency', POST/PUT to `/api/currencies`.
    - For all other types, POST/PUT to `/api/accounts`.
    - [x] Submission logic for both types is handled in handleSubmit.
    - [x] Add validation and user feedback for both flows.
    - [x] Ensure correct payload mapping for all account types (especially currency_account).

4. [x] **Action Buttons Consistency**
    - Ensure edit/delete actions are present in both accounts and currencies tables, using the same modal for editing.

5. [x] **Currency Account Columns**
    - Ensure 'Currency Account' table shows both 'Formula' and 'Currency' columns, with correct values.

6. [x] **Remove Redundant UI**
    - Remove any extra add/edit currency buttons or modals outside the unified modal.

7. [ ] **Updating UI with Async Jobs**
    - Replace the current synchronous account creation with an asynchronous job.
    - This will allow the UI to update immediately after the user clicks "Create" or "Update", improving perceived responsiveness.
    - [ ] Implement an async job for account creation and update.
    - [ ] Update the modal submission logic to dispatch the job.
    - [ ] Update the UI to display a loading spinner or a success message after the job is dispatched.
    - [ ] Handle any errors that may occur during the job execution.
    - [ ] In the TodoController, modify the store method to dispatch a queued job (CreateTodoJob) for creating todos asynchronously instead of creating them synchronously.
    - [ ] Create a new Laravel job class CreateTodoJob that handles the actual creation of the todo in the database.
    - [ ] In the dashboard frontend (resources/js/pages/dashboard.tsx), update the handleAddTodo function to optimistically update the UI by adding a temporary todo item immediately before sending the API request.
    - [ ] The API request will still be sent to the backend, but the UI will not wait for the response to update, improving perceived responsiveness.
    - [ ] Ensure that the UI reflects the final state of the todo after the job is completed (success or error).
    - [ ] - [ ] Investigate ways to notify the frontend when the queued job completes successfully or fails, such as using Laravel event broadcasting or WebSockets.
    - [ ] - [ ] If using event broadcasting, create an event class for todo creation success or failure.
    - [ ] - [ ] If using WebSockets, create a WebSocket channel for todo creation events.
    - [ ] - [ ] Update the frontend to listen for these events and update the UI accordingly.
    - [ ] - [ ] Implement a frontend polling mechanism to check if the new todo has been saved.
    - [ ] - [ ] Modify the frontend to show two notifications: one optimistic on add, and another on actual save success or failure.
    - [ ] - [ ] For an immediate fix, consider returning the created todo immediately from the API without queuing or use synchronous creation.
    - [ ] - [ ] Document the tradeoffs between immediate response and asynchronous queue processing.
    - [ ] - [ ] Investigate ways to notify the frontend when the queued job completes successfully or fails, such as using Laravel event broadcasting or WebSockets.
    - [ ] - [ ] If using event broadcasting, create an event class for todo creation success or failure.
    - [ ] - [ ] If using WebSockets, create a WebSocket channel for todo creation events.
    - [ ] - [ ] Update the frontend to listen for these events and update the UI accordingly.
    - [ ] Modify the CreateTodoJob to broadcast a TodoCreated event upon successful todo creation.
    - [ ] Create a new Laravel event class TodoCreated that implements ShouldBroadcast, broadcasting on a private channel specific to the user.
    - [ ] Update the dashboard frontend to set up Laravel Echo with Pusher for real-time event listening.
    - [ ] In the dashboard's useEffect, subscribe to the private todos channel for the current user and listen for the TodoCreated event.
    - [ ] When the event is received, update the todos state to include the newly created todo and show a success toast notification.
    - [ ] This will allow the frontend to be notified when the queued job completes and the todo is saved in the database, improving notification accuracy and UI consistency.

7. [ ] **Testing**
    - Test all workflows: create, update, delete for both account and currency types.
    - Test display and relations for 'Currency Account' (formula/currency columns).

## Journal Entries (Single & Multiple) Implementation Plan

### Backend
- [x] Create/verify migration for journal_entries and payment_types tables (fields: id, transaction_number, date, payment_type_id, credit_account_id, debit_account_id, description, chq_no, amount, created_by, etc.)
- [x] Create/verify migration and seeder for payment_types table (with all 9 types)
- [x] Create/extend JournalEntry, PaymentType models with relationships
- [x] Implement JournalController with methods:
    - [x] add role based access for admin and editor only to access these pages.
    - [x] entry (show single entry form)
    - [x] store (handle single entry submission)
        - [x] update with custom narration:
            - [x] add narration field to submission with description
            - [x] change credit_account_id and debit_account_id to account_id
            - [x] add is_credit field to determine if it's a credit or debit entry
            - [x] change transaction_number to TID
    - [x] multiple (show multiple entry form)
        - [x] add logic in the form credit, debit oramount field get filled so new row should be added
        - [x] add logic to handle multiple entry submission
        - [x] the last row will be empty so submit button not submit that row. { user enter first row as he enter credit or debit or amount field then new row should be added and the last row will be empty so submit button not submit that row. }
        - [x] add logic to handle get all row entries except last empty row
        - [x] add logic to handle if user select date and payment type then show the form to enter credit, debit, amount and description
        - [x] add logic if credit, debit , amount these fields are required if not entrered then show error message with row number to enter that field, Missing Credit Account in row number this. 
    - [x] storeMultiple (handle multiple entry submission)
        - [x] fix multiple entry form to use the same fields as single entry
        - [x] add multiple entry form with dynamic rows for each entry or iteration
        - [x] add validation for multiple entry form
        - [x] add logic to handle multiple entry submission
        - [x] handling empty rows as we fill any logical cell and empty row shows and deselect it the rows are there now more then one row so need to handle that do not need to submit empty rows, for submiting one row is mendatory.
- [x] Create FormRequest classes for validation:
    - [x] JournalEntrySingleRequest
    - [x] JournalEntryMultipleRequest
- [x] Implement dynamic description sentence logic for each payment type (service/helper)
- [x] Add authorization logic (role-based)
- [ ] Write feature/unit tests for backend logic
- [ ] Daily Book report page
    - [x] Create Daily Book page and Inertia route
    - [x] Implement API endpoint to fetch journal entries by date
    - [x] Fix backend relationships and serialization for account, payment type, and user
    - [x] Display all required columns in frontend table
    - [x] Fix narration logic to use payment type name
    - [x] Fix user column to show user name/role
    - [ ] Implement "View" action button (modal or page)
    - [ ] Implement "Edit" and "Update" functionality for entries
    - [ ] Add error handling and polish UI

### Frontend
- [x] Add sidebar navigation for 'Journal Entries' and 'Journal Multiple Entry'
- [x] Scaffold journal-entry.tsx (Single Entry page)
- [x] Scaffold journal-multiple-entry.tsx (Multiple Entry page)
- [x] Add Inertia routes for 'journal-entry' and 'journal-multiple-entry' pages
- [x] Implement form UI with shadcn/ui components:
    - [x] DatePicker for date
    - [x] ComboBox for payment type, credit account, debit account
    - [x] Text input for description, CHQ NO
    - [x] Numeric input with commas for amount
- [x] Implement table UI for multiple entry (add/remove rows, validation)
- [ ] Add skeleton loaders for loading states
- [ ] Add optimistic UI for submissions
- [x] Add validation and error handling (required fields, numeric, account logic,
debit/credit balance for multiple)
- [x] Implement logic to show form only after date and payment type are selected.
- [x] Remove explicit error messages for date and payment type.
- [x] Improve UI layout for Date and Payment Type selectors to be on the same row and responsive. - FIXED overlapping and sizing issues.

### Integration
- [x] Add web and API routes for journal entry pages and CRUD
- [x] Connect frontend forms to backend endpoints (Inertia/API)
- [ ] Ensure correct data flow and error handling

### Testing & Documentation
- [ ] Write end-to-end tests for all workflows (single/multiple entry, validation, error cases)
- [ ] Update OurProject.md and code comments as features are implemented
- [x] Document API endpoints and frontend usage (See `resources/js/Pages/ApiDocumentation.tsx`)
- [ ] Next Task: (Specify next task here)
- [x] Debugging: Investigate persistent 404 error on Daily Book report page (`/api/journal-entries/by-date`) after route reordering and cache clear the issue was effecting apiResources with custome routes so write custome routes first then apiResource routes.

### Bug Fixes & Improvements
- [x] Fix ComboBox search functionality:
    - [x] Implement case-insensitive search in ComboBox component
    - [x] Add fuzzy search matching for better results
    - [x] Ensure search works with partial text matches
    - [x] Test search functionality with various input scenarios

---

## Journal Entry Edit/Update Functionality

- [x] **Backend - JournalController & Routes**
    - [x] Create `edit` method in `JournalController` to fetch paired entries by `TID`.
        - [x] Process paired entries to extract single transaction view (Date, PaymentType, CreditAcc, DebitAcc, Amount, CHQ_NO).
        - [x] Implement logic to strip narration from description for display in edit form.
    - [x] Create `update` method in `JournalController` to handle entry updates.
        - [x] Find existing paired entries by `TID`.
        - [x] Validate incoming data using a new `JournalEntryUpdateRequest` FormRequest.
        - [x] Update fields in both credit and debit entries.
        - [x] Re-generate narration based on (potentially updated) payment type and append to description.
        - [x] Ensure `transaction_number` (TID) remains consistent.
    - [x] Add web route for edit page: `GET /journal/entry/{tid}/edit`.
    - [x] Add API route for update: `PUT /api/journal-entries/{tid}`.
- [ ] **Frontend - Edit Journal Entry Page**
    - [x] Create `resources/js/Pages/JournalEntry/EditJournalEntry.tsx` page component.
    - [x] Fetch journal entry data by `TID` on page load.
    - [x] Pre-fill form fields (Date, PaymentType, CreditAcc, DebitAcc, Amount, Description (no narration), CHQ_NO).
        - [x] Ensure ComboBoxes for accounts and payment type are correctly populated and selected.
    - [x] Implement form submission logic to `PUT /api/journal-entries/{tid}`.
    - [x] Handle success (e.g., redirect to Daily Book, show toast).
    - [ ] Handle validation and server errors, display toasts.
- [x] **Narration Handling**
    - [x] Ensure narration stripping logic is accurate for various payment types.
    - [x] Ensure narration re-generation logic is consistent with `store` method.
- [ ] **Testing**
    - [x] Test fetching and displaying an entry for edit.
    - [x] Test updating an entry with and without changes to payment type/accounts.
    - [x] Test Verify narration is correctly stripped and re-generated.
    - [x] Test Verify data consistency in the database after update.
    - [x] Test validation errors.
- [ ] **Documentation**
    - [ ] Update `OurProject.md` with details of the edit/update functionality.
    - [ ] Update `ApiDocumentation.tsx` if new API details are relevant.

### Will Add These Features Later
- [ ] Add a "Create Account" button to the Journal Multiple Entry page to create a new account directly from the journal entry form.
- [ ] Add Todo Icon in header bar to create a new todo item from anywhere in the app.
- [ ] Creating a P&L {Currency} account should automatically create when Currency Account is created.