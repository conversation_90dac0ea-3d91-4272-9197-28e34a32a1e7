@php
    $isJson = request()->expectsJson();
@endphp
@if ($isJson)
    @php
        echo response()->json(['message' => 'Unauthorized: You do not have permission to access this page.'], 403);
        exit;
    @endphp
@else
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Unauthorized</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;700&display=swap" rel="stylesheet">
    <style>
        body { background: linear-gradient(135deg, #fff6f6 0%, #fffbe6 100%); color: #1b1b18; font-family: 'Inter', sans-serif; margin: 0; }
        .center { min-height: 100vh; display: flex; flex-direction: column; align-items: center; justify-content: center; }
        .card { background: rgba(255,255,255,0.95); border-radius: 1.5rem; box-shadow: 0 8px 32px rgba(245,48,3,0.08); padding: 2.5rem 2rem; border: 1px solid #ffe0e0; display: flex; flex-direction: column; align-items: center; }
        .icon { width: 5rem; height: 5rem; color: #F53003; margin-bottom: 1.5rem; }
        h1 { font-size: 2.5rem; color: #F53003; margin-bottom: 0.5rem; }
        p { font-size: 1.2rem; color: #706f6c; margin-bottom: 2rem; text-align: center; }
        a.button { display: inline-block; padding: 0.75rem 2rem; border-radius: 0.5rem; background: #F53003; color: #fff; font-weight: 700; text-decoration: none; box-shadow: 0 2px 8px rgba(245,48,3,0.08); transition: background 0.2s; }
        a.button:hover { background: #d91b00; }
        @media (max-width: 600px) { .card { padding: 1.5rem 0.5rem; } h1 { font-size: 2rem; } }
    </style>
</head>
<body>
    <div class="center">
        <div class="card">
            <svg class="icon" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 48 48">
                <rect x="8" y="20" width="32" height="20" rx="4" fill="#fff" stroke="#F53003" stroke-width="2" />
                <circle cx="24" cy="30" r="4" fill="#F53003" />
                <rect x="16" y="12" width="16" height="12" rx="8" fill="#F53003" fill-opacity="0.15" />
            </svg>
            <h1>Unauthorized</h1>
            <p>Sorry, you do not have permission to access this page.<br>Please contact your administrator if you believe this is a mistake.</p>
            <a href="/dashboard" class="button">Go to Dashboard</a>
        </div>
    </div>
</body>
</html>
@endif 