<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use Illuminate\Validation\Rule;
use Inertia\Inertia;

class UserController extends Controller
{
    public function store(Request $request)
    {
        if (!Auth::user() || Auth::user()->role !== 'Admin') {
            abort(403, 'Unauthorized action.');
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'username' => 'required|string|max:255|unique:users',
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            'role' => ['required', Rule::in(['Admin', 'Editor', 'Viewer'])],
        ]);

        $user = User::create([
            'name' => $validated['name'],
            'email' => $validated['email'],
            'username' => $validated['username'],
            'password' => Hash::make($validated['password']),
            'role' => $validated['role'],
        ]);

        return redirect()->back()->with('success', 'User created successfully!');
    }

    public function index()
    {
        if (!Auth::user() || Auth::user()->role !== 'Admin') {
            abort(403, 'Unauthorized action.');
        }
        $users = User::all();
        return Inertia::render('settings/users', [
            'users' => $users,
        ]);
    }

    public function destroy(User $user)
    {
        if (!Auth::user() || Auth::user()->role !== 'Admin') {
            abort(403, 'Unauthorized action.');
        }

        // Prevent self-delete
        if (Auth::id() === $user->getKey()) {
            return back()->withErrors(['error' => 'You cannot delete your own account.']);
        }

        $user->delete();
        return back()->with('success', 'User deleted successfully!');
    }

    public function update(Request $request, User $user)
    {
        if (!Auth::user() || Auth::user()->role !== 'Admin') {
            abort(403, 'Unauthorized action.');
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'username' => ['required', 'string', 'max:255', Rule::unique('users')->ignore($user->getKey())],
            'role' => ['required', Rule::in(['Admin', 'Editor', 'Viewer'])],
            'password' => ['nullable', 'confirmed', Rules\Password::defaults()],
        ]);

        $user->name = $validated['name'];
        $user->username = $validated['username'];
        $user->role = $validated['role'];
        if (!empty($validated['password'])) {
            $user->password = Hash::make($validated['password']);
        }
        $user->save();

        return back()->with('success', 'User updated successfully!');
    }
}
