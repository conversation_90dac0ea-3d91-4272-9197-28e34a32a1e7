<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Model;

class Cheque extends Model
{
    use HasFactory;

    protected $table = 'cheque_entries';

    protected $fillable = [
        'entry_type',
        'entry_date',
        'cheque_date',
        'posting_date',
        'from_account_id',
        'to_account_id',
        'amount',
        'chq_ref_bank_id',
        'chq_no',
        'status',
    ];

    public function fromAccount()
    {
        return $this->belongsTo(Account::class, 'from_account_id');
    }

    public function toAccount()
    {
        return $this->belongsTo(Account::class, 'to_account_id');
    }

    public function chqRefBank()
    {
        return $this->belongsTo(ChqRefBank::class, 'chq_ref_bank_id');
    }
}
