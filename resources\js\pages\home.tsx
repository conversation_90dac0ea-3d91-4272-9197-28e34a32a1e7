import { type SharedData } from '@/types';
import { Head, Link, usePage } from '@inertiajs/react';
import { ThemeToggle } from '@/components/ui/theme-toggle';

const features = [
  {
    title: 'True Double-Entry Ledger',
    desc: 'Every transaction is balanced. Enjoy audit-ready books and bulletproof financial integrity.',
    icon: (
      <svg className="w-8 h-8 text-[#F53003] animate-feature-icon" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><rect x="4" y="4" width="16" height="16" rx="2"/><path d="M8 4v16"/></svg>
    )
  },
  {
    title: 'Multi-User & Role-Based',
    desc: 'Invite your team, assign roles (Admin, Accountant, Viewer), and control access to sensitive data.',
    icon: (
      <svg className="w-8 h-8 text-[#F8B803] animate-feature-icon" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><circle cx="12" cy="7" r="4"/><path d="M5.5 21a7.5 7.5 0 0 1 13 0"/></svg>
    )
  },
  {
    title: 'Real-Time Dashboards',
    desc: 'Visualize balances, cash flow, and profit & loss instantly with beautiful charts and widgets.',
    icon: (
      <svg className="w-8 h-8 text-[#F53003] animate-feature-icon" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><rect x="3" y="12" width="6" height="8"/><rect x="9" y="8" width="6" height="12"/><rect x="15" y="4" width="6" height="16"/></svg>
    )
  },
  {
    title: 'Automated Journal Entries',
    desc: 'Recurring transactions, templates, and smart suggestions to save you time and reduce errors.',
    icon: (
      <svg className="w-8 h-8 text-[#F8B803] animate-feature-icon" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path d="M4 4h16v4H4z"/><path d="M4 8v12h16V8"/><path d="M8 12h8"/></svg>
    )
  },
  {
    title: 'Bank Sync & Reconciliation',
    desc: 'Connect your bank, auto-import transactions, and reconcile with ease.',
    icon: (
      <svg className="w-8 h-8 text-[#F53003] animate-feature-icon" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10"/><path d="M12 6v6l4 2"/></svg>
    )
  },
  {
    title: 'Secure & Compliant',
    desc: 'Your data is encrypted, backed up, and always available. GDPR-ready and privacy-first.',
    icon: (
      <svg className="w-8 h-8 text-[#F8B803] animate-feature-icon" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><rect x="4" y="11" width="16" height="9" rx="2"/><path d="M12 16v-4"/><circle cx="12" cy="13" r="1"/></svg>
    )
  }
];

export default function Home() {
    const { auth } = usePage<SharedData>().props;

    return (
        <>
            <Head title="Home">
                <link rel="preconnect" href="https://fonts.bunny.net" />
                <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet" />
            </Head>
            <div className="flex min-h-screen flex-col items-center bg-[#FDFDFC] p-6 text-[#1b1b18] lg:justify-center lg:p-8 dark:bg-[#0a0a0a] dark:text-[#EDEDEC]">
                {/* Header/Navbar */}
                <header className="mb-8 w-full max-w-6xl text-sm animate-header-fade">
                    <nav className="flex items-center justify-between gap-4">
                        <div className="flex items-center gap-2">
                            <Link href={route('home')} className="font-black text-xl tracking-tight flex items-center gap-2 animate-header-logo">
                                <span className="inline-block align-middle">
                                    <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <rect width="32" height="32" rx="8" fill="#F53003" className="dark:fill-[#F8B803]" />
                                        <text x="50%" y="57%" textAnchor="middle" fill="#fff" fontSize="15" fontWeight="bold" dy=".3em">AS</text>
                                    </svg>
                                </span>
                                <span>AccSystem</span>
                            </Link>
                        </div>
                        <div className="flex items-center gap-2 animate-header-nav">
                            <ThemeToggle />
                            {auth.user ? (
                                <Link
                                    href={route('dashboard')}
                                    className="inline-block rounded-md border border-[#19140035] px-5 py-1.5 text-sm font-semibold leading-normal text-[#1b1b18] hover:border-[#1915014a] dark:border-[#3E3E3A] dark:text-[#EDEDEC] dark:hover:border-[#62605b]"
                                >
                                    Dashboard
                                </Link>
                            ) : (
                                <>
                                    <Link
                                        href={route('login')}
                                        className="inline-block rounded-md border border-transparent px-3 py-1.5 text-sm font-semibold leading-normal text-[#1b1b18] hover:border-[#19140035] dark:text-[#EDEDEC] dark:hover:border-[#3E3E3A]"
                                    >
                                        Log in
                                    </Link>
                                </>
                            )}
                        </div>
                    </nav>
                </header>

                {/* Hero Section */}
                <section className="flex flex-col items-center text-center max-w-3xl mx-auto mb-16 relative overflow-visible animate-hero-fade">
                    {/* Animated SVG Icon - Left (Desktop/Tablet only) */}
                    <div className="hidden sm:block absolute left-[-90px] top-1/2 -translate-y-1/2 z-10 animate-hero-icon-float" aria-hidden="true">
                        <svg width="64" height="64" viewBox="0 0 64 64" fill="none">
                            <circle cx="32" cy="32" r="28" fill="#F8B803" fillOpacity="0.18" />
                            <rect x="16" y="24" width="32" height="16" rx="4" fill="#F53003" fillOpacity="0.7" />
                            <rect x="22" y="30" width="20" height="4" rx="2" fill="#fff" fillOpacity="0.85" />
                        </svg>
                    </div>
                    {/* Animated SVG Icon - Right (Desktop/Tablet only) */}
                    <div className="hidden sm:block absolute right-[-90px] top-1/2 -translate-y-1/2 z-10 animate-hero-icon-bounce" aria-hidden="true">
                        <svg width="64" height="64" viewBox="0 0 64 64" fill="none">
                            <ellipse cx="32" cy="32" rx="28" ry="14" fill="#F53003" fillOpacity="0.14" />
                            <path d="M20 36L32 20L44 36Z" fill="#F8B803" fillOpacity="0.7" />
                            <circle cx="32" cy="32" r="5" fill="#fff" fillOpacity="0.85" />
                        </svg>
                    </div>
                    {/* Modern Silicon Animation - ensure visible */}
                    <div className="absolute z-0 left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 pointer-events-none select-none w-[500px] h-[500px] opacity-80 animate-spin-slow" style={{filter: 'blur(3px)'}}> 
                        <svg width="500" height="500" viewBox="0 0 500 500" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <defs>
                                <radialGradient id="glow" cx="50%" cy="50%" r="50%" fx="50%" fy="50%">
                                    <stop offset="0%" stopColor="#F53003" stopOpacity="0.7" />
                                    <stop offset="80%" stopColor="#F8B803" stopOpacity="0.2" />
                                    <stop offset="100%" stopColor="#F53003" stopOpacity="0" />
                                </radialGradient>
                            </defs>
                            <circle cx="250" cy="250" r="180" fill="url(#glow)" />
                            <ellipse cx="250" cy="250" rx="140" ry="40" fill="#F8B803" fillOpacity="0.18" />
                            <rect x="100" y="100" width="300" height="300" rx="70" stroke="#F53003" strokeWidth="4" fill="none" opacity="0.25" />
                            <rect x="140" y="140" width="220" height="220" rx="50" stroke="#F8B803" strokeWidth="2" fill="none" opacity="0.18" />
                        </svg>
                    </div>
                    <h1 className="text-4xl md:text-5xl font-black mb-4 tracking-tight leading-tight animate-hero-title">
                        The Most Advanced <span className="text-[#F53003] dark:text-[#F8B803]">Double Entry</span> Accounting SaaS
                    </h1>
                    <p className="text-lg md:text-xl text-[#706f6c] dark:text-[#A1A09A] mb-6 animate-hero-desc">
                        Run your business with confidence. Automate, analyze, and grow with our powerful, secure, and easy-to-use double-entry accounting system—built for teams, accountants, and entrepreneurs.
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4 justify-center animate-hero-cta">
                        {auth.user ? (
                            <Link href={route('dashboard')} className="rounded-md bg-[#F53003] px-6 py-3 text-lg font-bold text-white shadow-lg hover:bg-[#d91b00] transition dark:bg-[#F8B803] dark:text-[#1b1b18] dark:hover:bg-[#e0a800]">
                                Go to Dashboard
                            </Link>
                        ) : (
                            <>
                                {route().has('register') ? (
                                    <Link href={route('register')} className="rounded-md bg-[#F53003] px-6 py-3 text-lg font-bold text-white shadow-lg hover:bg-[#d91b00] transition dark:bg-[#F8B803] dark:text-[#1b1b18] dark:hover:bg-[#e0a800]">
                                        Register Now
                                    </Link>
                                ) : null}
                                <Link href={route('login')} className="rounded-md border border-[#19140035] px-6 py-3 text-lg font-semibold text-[#1b1b18] hover:border-[#1915014a] dark:border-[#3E3E3A] dark:text-[#EDEDEC] dark:hover:border-[#62605b]">
                                    Log In
                                </Link>
                            </>
                        )}
                    </div>
                </section>

                {/* Features Section */}
                <section className="w-full max-w-6xl mx-auto mb-16 grid grid-cols-1 md:grid-cols-3 gap-8">
                  {features.map((f, i) => (
                    <div
                      key={f.title}
                      className="rounded-lg bg-white p-6 shadow-md dark:bg-[#161615] flex flex-col items-start animate-feature-fade"
                      style={{ animationDelay: `${0.12 + i * 0.13}s` }}
                    >
                      <div className="mb-3">{f.icon}</div>
                      <h3 className="font-bold text-lg mb-2">{f.title}</h3>
                      <p className="text-[#706f6c] dark:text-[#A1A09A]">{f.desc}</p>
                    </div>
                  ))}
                </section>

                {/* Call to Action Section */}
                <section className="w-full max-w-3xl mx-auto text-center mb-12 animate-cta-fade">
                    <h2 className="text-2xl md:text-3xl font-bold mb-2 animate-cta-title">
                        Ready to Transform Your Accounting?
                    </h2>
                    <p className="mb-6 text-[#706f6c] dark:text-[#A1A09A] animate-cta-desc">
                        Sign up today and experience the next generation of double-entry accounting software.
                    </p>
                    {auth.user ? (
                        <Link href={route('dashboard')} className="rounded-md bg-[#F53003] px-8 py-3 text-lg font-bold text-white shadow-lg hover:bg-[#d91b00] transition dark:bg-[#F8B803] dark:text-[#1b1b18] dark:hover:bg-[#e0a800] animate-cta-btn">
                            Go to Dashboard
                        </Link>
                    ) : (
                        <>
                            {route().has('register') ? (
                                <Link href={route('register')} className="rounded-md bg-[#F53003] px-8 py-3 text-lg font-bold text-white shadow-lg hover:bg-[#d91b00] transition dark:bg-[#F8B803] dark:text-[#1b1b18] dark:hover:bg-[#e0a800] animate-cta-btn">
                                    Register Now
                                </Link>
                            ) : null}
                        </>
                    )}
                </section>

                {/* Footer */}
                <footer className="w-full max-w-6xl mx-auto text-center text-xs text-[#A1A09A] dark:text-[#706f6c] py-8 border-t border-[#e3e3e0] dark:border-[#3E3E3A] animate-footer-fade">
                    &copy; {new Date().getFullYear()} AccSystem. All rights reserved. | Advanced Double-Entry Accounting SaaS
                </footer>
            </div>
            {/* Animations CSS */}
            <style>
                {`
                    @keyframes spin-slow {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                    }
                    .animate-spin-slow {
                    animation: spin-slow 24s linear infinite;
                    }
                    @keyframes feature-fade {
                      0% { opacity: 0; transform: translateY(24px) scale(0.98); }
                      60% { opacity: 1; transform: translateY(-4px) scale(1.03); }
                      100% { opacity: 1; transform: translateY(0) scale(1); }
                    }
                    .animate-feature-fade {
                      opacity: 0;
                      animation: feature-fade 0.9s cubic-bezier(0.4,0,0.2,1) both;
                    }
                    @keyframes feature-icon {
                      0% { transform: scale(0.7) rotate(-12deg); opacity: 0; }
                      60% { transform: scale(1.1) rotate(6deg); opacity: 1; }
                      100% { transform: scale(1) rotate(0deg); opacity: 1; }
                    }
                    .animate-feature-icon {
                      animation: feature-icon 0.9s cubic-bezier(0.4,0,0.2,1) both;
                    }
                    /* Header Animations */
                    @keyframes header-fade {
                      0% { opacity: 0; transform: translateY(-24px) scale(0.98); }
                      60% { opacity: 1; transform: translateY(4px) scale(1.01); }
                      100% { opacity: 1; transform: translateY(0) scale(1); }
                    }
                    .animate-header-fade {
                      opacity: 0;
                      animation: header-fade 1.1s cubic-bezier(0.4,0,0.2,1) 0.15s both;
                    }
                    @keyframes header-logo {
                      0% { opacity: 0; transform: scale(0.8) translateY(-8px); }
                      60% { opacity: 1; transform: scale(1.08) translateY(2px); }
                      100% { opacity: 1; transform: scale(1) translateY(0); }
                    }
                    .animate-header-logo {
                      opacity: 0;
                      animation: header-logo 1.1s cubic-bezier(0.4,0,0.2,1) 0.23s both;
                    }
                    @keyframes header-nav {
                      0% { opacity: 0; transform: translateY(-8px); }
                      60% { opacity: 1; transform: translateY(2px); }
                      100% { opacity: 1; transform: translateY(0); }
                    }
                    .animate-header-nav {
                      opacity: 0;
                      animation: header-nav 1.1s cubic-bezier(0.4,0,0.2,1) 0.42s both;
                    }
                    /* Hero Animations */
                    @keyframes hero-fade {
                      0% { opacity: 0; transform: translateY(32px) scale(0.97); }
                      60% { opacity: 1; transform: translateY(-6px) scale(1.02); }
                      100% { opacity: 1; transform: translateY(0) scale(1); }
                    }
                    .animate-hero-fade {
                      opacity: 0;
                      animation: hero-fade 1.1s cubic-bezier(0.4,0,0.2,1) 0.36s both;
                    }
                    @keyframes hero-title {
                      0% { opacity: 0; transform: translateY(18px) scale(0.96); }
                      60% { opacity: 1; transform: translateY(-2px) scale(1.03); }
                      100% { opacity: 1; transform: translateY(0) scale(1); }
                    }
                    .animate-hero-title {
                      opacity: 0;
                      animation: hero-title 1.1s cubic-bezier(0.4,0,0.2,1) 0.5s both;
                    }
                    @keyframes hero-desc {
                      0% { opacity: 0; transform: translateY(12px); }
                      60% { opacity: 1; transform: translateY(-2px); }
                      100% { opacity: 1; transform: translateY(0); }
                    }
                    .animate-hero-desc {
                      opacity: 0;
                      animation: hero-desc 1.1s cubic-bezier(0.4,0,0.2,1) 0.7s both;
                    }
                    @keyframes hero-cta {
                      0% { opacity: 0; transform: scale(0.92) translateY(12px); }
                      60% { opacity: 1; transform: scale(1.04) translateY(-2px); }
                      100% { opacity: 1; transform: scale(1) translateY(0); }
                    }
                    .animate-hero-cta {
                      opacity: 0;
                      animation: hero-cta 1.1s cubic-bezier(0.4,0,0.2,1) 0.9s both;
                    }
                    /* CTA Animations */
                    @keyframes cta-fade {
                      0% { opacity: 0; transform: translateY(32px) scale(0.96); }
                      60% { opacity: 1; transform: translateY(-4px) scale(1.03); }
                      100% { opacity: 1; transform: translateY(0) scale(1); }
                    }
                    .animate-cta-fade {
                      opacity: 0;
                      animation: cta-fade 1.1s cubic-bezier(0.4,0,0.2,1) 0.7s both;
                    }
                    @keyframes cta-title {
                      0% { opacity: 0; transform: translateY(18px) scale(0.96); }
                      60% { opacity: 1; transform: translateY(-2px) scale(1.03); }
                      100% { opacity: 1; transform: translateY(0) scale(1); }
                    }
                    .animate-cta-title {
                      opacity: 0;
                      animation: cta-title 1.1s cubic-bezier(0.4,0,0.2,1) 0.85s both;
                    }
                    @keyframes cta-desc {
                      0% { opacity: 0; transform: translateY(12px); }
                      60% { opacity: 1; transform: translateY(-2px); }
                      100% { opacity: 1; transform: translateY(0); }
                    }
                    .animate-cta-desc {
                      opacity: 0;
                      animation: cta-desc 1.1s cubic-bezier(0.4,0,0.2,1) 1s both;
                    }
                    @keyframes cta-btn {
                      0% { opacity: 0; transform: scale(0.92) translateY(12px); }
                      60% { opacity: 1; transform: scale(1.04) translateY(-2px); }
                      100% { opacity: 1; transform: scale(1) translateY(0); }
                    }
                    .animate-cta-btn {
                      opacity: 0;
                      animation: cta-btn 1.1s cubic-bezier(0.4,0,0.2,1) 1.18s both;
                    }
                    /* Footer Animation */
                    @keyframes footer-fade {
                      0% { opacity: 0; transform: translateY(24px) scale(0.98); }
                      60% { opacity: 1; transform: translateY(-2px) scale(1.01); }
                      100% { opacity: 1; transform: translateY(0) scale(1); }
                    }
                    .animate-footer-fade {
                      opacity: 0;
                      animation: footer-fade 1.1s cubic-bezier(0.4,0,0.2,1) 1.6s both;
                    }
                    /* Hero Icon Animations */
                    @keyframes hero-icon-float {
                        0% { transform: translateY(-10px) scale(1); }
                        50% { transform: translateY(10px) scale(1.07); }
                        100% { transform: translateY(-10px) scale(1); }
                    }
                    .animate-hero-icon-float {
                        animation: hero-icon-float 3.8s ease-in-out infinite;
                    }
                    @keyframes hero-icon-bounce {
                        0% { transform: scale(1) translateY(0); }
                        30% { transform: scale(1.13) translateY(-8px); }
                        60% { transform: scale(0.94) translateY(6px); }
                        100% { transform: scale(1) translateY(0); }
                    }
                    .animate-hero-icon-bounce {
                        animation: hero-icon-bounce 3.2s cubic-bezier(0.4,0,0.2,1) infinite;
                    }
                `}
            </style>
        </>
    );
}
