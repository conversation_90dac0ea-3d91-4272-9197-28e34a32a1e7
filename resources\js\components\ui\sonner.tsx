import * as React from "react"
import { useTheme } from "next-themes"
import { Toaster as Son<PERSON>, ToasterP<PERSON>, toast as sonnerToast, ExternalToast } from "sonner"
import { CheckCircle, XCircle, AlertCircle, Info, X } from "lucide-react"
import { cn } from "@/lib/utils"

type ToastPosition =
  | "top-left"
  | "top-center"
  | "top-right"
  | "bottom-left"
  | "bottom-center"
  | "bottom-right"

type ToastVariant = "default" | "success" | "error" | "warning" | "info"

interface ToastAction {
  label: string
  onClick: () => void
  variant?: "default" | "destructive"
}

interface CustomToastProps extends ExternalToast {
  variant?: ToastVariant
  title?: string
  description?: string
  action?: ToastAction
  cancel?: {
    label: string
    onClick?: () => void
  }
  richContent?: React.ReactNode
}

interface ToasterProps extends Omit<ToasterProps, 'position'> {
  position?: ToastPosition
  variant?: "default" | "rich"
  animation?: "slide" | "fade" | "scale"
}

const Toaster = ({
  position = "bottom-right",
  variant = "default",
  animation = "slide",
  ...props
}: ToasterProps) => {
  const { theme = "system" } = useTheme()

  const getAnimationClass = () => {
    switch (animation) {
      case "fade":
        return "data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-80 data-[state=open]:fade-in-0"
      case "scale":
        return "data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-80 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95"
      default:
        return "data-[state=open]:slide-in-from-bottom-full data-[state=closed]:slide-out-to-bottom-full"
    }
  }

  return (
    <Sonner
      theme={theme as ToasterProps["theme"]}
      position={position}
      className={cn(
        "toaster group",
        getAnimationClass(),
        variant === "rich" && "rich-toaster"
      )}
      style={
        {
          "--normal-bg": "var(--popover)",
          "--normal-text": "var(--popover-foreground)",
          "--normal-border": "var(--border)",
          "--success-bg": "var(--toast-success-bg)",
          "--success-text": "var(--success)",
          "--success-border": "var(--toast-success-border)",
          "--error-bg": "var(--toast-error-bg)",
          "--error-text": "var(--error)",
          "--error-border": "var(--toast-error-border)",
          "--warning-bg": "var(--toast-warning-bg)",
          "--warning-text": "var(--warning)",
          "--warning-border": "var(--toast-warning-border)",
          "--info-bg": "var(--toast-info-bg)",
          "--info-text": "var(--info)",
          "--info-border": "var(--toast-info-border)",
        } as React.CSSProperties
      }
      toastOptions={{
        className: cn(
          "group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",
          variant === "rich" && "group-[.rich-toaster]:min-h-[4rem] group-[.rich-toaster]:p-4"
        ),
        ...props.toastOptions,
      }}
      {...props}
    />
  )
}

// Custom toast component for rich content
const ToastContent = ({
  variant,
  title,
  description,
  action,
  cancel,
  richContent
}: CustomToastProps) => {
  const getIcon = () => {
    switch (variant) {
      case "success":
        return <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400" />
      case "error":
        return <XCircle className="h-5 w-5 text-red-600 dark:text-red-400" />
      case "warning":
        return <AlertCircle className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
      case "info":
        return <Info className="h-5 w-5 text-blue-600 dark:text-blue-400" />
      default:
        return null
    }
  }

  if (richContent) {
    return <div className="w-full">{richContent}</div>
  }

  return (
    <div className="flex items-start gap-3 w-full">
      {getIcon()}
      <div className="flex-1 space-y-1">
        {title && <div className="font-semibold text-sm">{title}</div>}
        {description && <div className="text-sm text-muted-foreground">{description}</div>}
        {(action || cancel) && (
          <div className="flex items-center gap-2 mt-2">
            {action && (
              <button
                onClick={action.onClick}
                className={cn(
                  "px-3 py-1 text-xs font-medium rounded-md transition-colors border",
                  action.variant === "destructive"
                    ? "bg-red-600 text-white border-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800"
                    : variant === "success"
                    ? "bg-green-600 text-white border-green-600 hover:bg-green-700 dark:bg-green-700 dark:hover:bg-green-800"
                    : variant === "warning"
                    ? "bg-yellow-600 text-white border-yellow-600 hover:bg-yellow-700 dark:bg-yellow-700 dark:hover:bg-yellow-800"
                    : variant === "info"
                    ? "bg-blue-600 text-white border-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800"
                    : "bg-slate-600 text-white border-slate-600 hover:bg-slate-700 dark:bg-slate-700 dark:hover:bg-slate-800"
                )}
              >
                {action.label}
              </button>
            )}
            {cancel && (
              <button
                onClick={cancel.onClick}
                className="px-3 py-1 text-xs font-medium rounded-md transition-colors border bg-white text-slate-700 border-slate-300 hover:bg-slate-50 dark:bg-slate-800 dark:text-slate-300 dark:border-slate-600 dark:hover:bg-slate-700"
              >
                {cancel.label}
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  )
}

// Preset toast functions
const toast = {
  // Basic toast
  message: (message: string, options?: ExternalToast) => {
    return sonnerToast(message, options)
  },

  // Success toast
  success: (message: string | CustomToastProps, options?: ExternalToast) => {
    if (typeof message === "string") {
      return sonnerToast.success(message, {
        icon: <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />,
        className: "border-l-4 border-l-green-500 bg-green-50 text-green-900 dark:bg-green-950/50 dark:text-green-100 dark:border-l-green-400",
        ...options,
      })
    }
    return sonnerToast(<ToastContent {...message} variant="success" />, {
      className: "border-l-4 border-l-green-500 bg-green-50 text-green-900 dark:bg-green-950/50 dark:text-green-100 dark:border-l-green-400",
      ...options,
    })
  },

  // Error toast
  error: (message: string | CustomToastProps, options?: ExternalToast) => {
    if (typeof message === "string") {
      return sonnerToast.error(message, {
        icon: <XCircle className="h-4 w-4 text-red-600 dark:text-red-400" />,
        className: "border-l-4 border-l-red-500 bg-red-50 text-red-900 dark:bg-red-950/50 dark:text-red-100 dark:border-l-red-400",
        ...options,
      })
    }
    return sonnerToast(<ToastContent {...message} variant="error" />, {
      className: "border-l-4 border-l-red-500 bg-red-50 text-red-900 dark:bg-red-950/50 dark:text-red-100 dark:border-l-red-400",
      ...options,
    })
  },

  // Warning toast
  warning: (message: string | CustomToastProps, options?: ExternalToast) => {
    if (typeof message === "string") {
      return sonnerToast.warning(message, {
        icon: <AlertCircle className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />,
        className: "border-l-4 border-l-yellow-500 bg-yellow-50 text-yellow-900 dark:bg-yellow-950/50 dark:text-yellow-100 dark:border-l-yellow-400",
        ...options,
      })
    }
    return sonnerToast(<ToastContent {...message} variant="warning" />, {
      className: "border-l-4 border-l-yellow-500 bg-yellow-50 text-yellow-900 dark:bg-yellow-950/50 dark:text-yellow-100 dark:border-l-yellow-400",
      ...options,
    })
  },

  // Info toast
  info: (message: string | CustomToastProps, options?: ExternalToast) => {
    if (typeof message === "string") {
      return sonnerToast.info(message, {
        icon: <Info className="h-4 w-4 text-blue-600 dark:text-blue-400" />,
        className: "border-l-4 border-l-blue-500 bg-blue-50 text-blue-900 dark:bg-blue-950/50 dark:text-blue-100 dark:border-l-blue-400",
        ...options,
      })
    }
    return sonnerToast(<ToastContent {...message} variant="info" />, {
      className: "border-l-4 border-l-blue-500 bg-blue-50 text-blue-900 dark:bg-blue-950/50 dark:text-blue-100 dark:border-l-blue-400",
      ...options,
    })
  },

  // Loading toast
  loading: (message: string, options?: ExternalToast) => {
    return sonnerToast.loading(message, options)
  },

  // Promise toast
  promise: sonnerToast.promise,

  // Custom toast with rich content
  custom: (content: React.ReactNode, options?: ExternalToast) => {
    return sonnerToast(content, options)
  },

  // Dismiss toast
  dismiss: sonnerToast.dismiss,
}

export {
  Toaster,
  toast,
  ToastContent,
  type ToastPosition,
  type ToastVariant,
  type ToastAction,
  type CustomToastProps,
  type ToasterProps as CustomToasterProps
}
