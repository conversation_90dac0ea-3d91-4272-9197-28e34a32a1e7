<?php

namespace App\Http\Controllers\JournalEntry;

use App\Http\Controllers\Controller;
use App\Models\PaymentType;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class PaymentTypeController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $filterNames = $request->input('filter_names');

        if ($filterNames && is_array($filterNames)) {
            // Create a cache key based on the filtered names
            $cacheKey = 'payment_types_filtered_' . md5(implode(',', $filterNames));

            // Attempt to retrieve from cache
            return Cache::remember($cacheKey, 60 * 60 * 24 * 7, function () use ($filterNames) {
                // Cache for 1 hour (adjust as needed)
                return PaymentType::whereIn('name', $filterNames)->get();
            });
        } else {
            // Return all if no filter is provided (or filter is invalid)
            return PaymentType::all();
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:payment_types',
        ]);

        return PaymentType::create($validated);
    }

    /**
     * Display the specified resource.
     */
    public function show(PaymentType $paymentType)
    {
        return $paymentType;
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, PaymentType $paymentType)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:payment_types,name,' . $paymentType->id,
        ]);

        $paymentType->update($validated);
        return $paymentType;
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(PaymentType $paymentType)
    {
        $paymentType->delete();
        return response()->noContent();
    }
}