# Laravel 12 Async Operations and Broadcasting Guide

## Table of Contents
1. [Async Operations](#async-operations)
2. [Queue System](#queue-system)
3. [Broadcasting](#broadcasting)
4. [WebSockets](#websockets)
5. [Real-time Features](#real-time-features)

## Async Operations
### Job Queues
```php
// Creating a job
php artisan make:job ProcessPodcast

// Dispatching a job
ProcessPodcast::dispatch($podcast);

// Delayed dispatch
ProcessPodcast::dispatch($podcast)->delay(now()->addMinutes(10));
```

### Batch Processing
```php
use Illuminate\Bus\Batch;
use Illuminate\Support\Facades\Bus;

$batch = Bus::batch([
    new ProcessPodcast(1),
    new ProcessPodcast(2),
    new ProcessPodcast(3)
])->then(function (Batch $batch) {
    // All jobs completed successfully...
})->catch(function (Batch $batch, Throwable $e) {
    // First batch job failure detected...
})->finally(function (Batch $batch) {
    // The batch has finished executing...
})->dispatch();
```

## Queue System
### Queue Configuration
```php
// config/queue.php
'connections' => [
    'redis' => [
        'driver' => 'redis',
        'connection' => 'default',
        'queue' => 'default',
        'retry_after' => 90,
        'block_for' => null,
    ],
]
```

### Queue Workers
```bash
# Start a queue worker
php artisan queue:work

# Start a worker with specific options
php artisan queue:work redis --queue=high,default --tries=3
```

## Broadcasting
### Event Broadcasting Setup
```php
// config/broadcasting.php
'pusher' => [
    'driver' => 'pusher',
    'key' => env('PUSHER_APP_KEY'),
    'secret' => env('PUSHER_APP_SECRET'),
    'app_id' => env('PUSHER_APP_ID'),
    'options' => [
        'cluster' => env('PUSHER_APP_CLUSTER'),
        'encrypted' => true,
    ],
],
```

### Creating Broadcast Events
```php
php artisan make:event OrderShipped

// OrderShipped.php
class OrderShipped implements ShouldBroadcast
{
    public $order;

    public function __construct(Order $order)
    {
        $this->order = $order;
    }

    public function broadcastOn()
    {
        return new PrivateChannel('orders.'.$this->order->id);
    }
}
```

### Broadcasting to Channels
```php
// Broadcasting to a private channel
event(new OrderShipped($order));

// Broadcasting to multiple channels
public function broadcastOn()
{
    return [
        new PrivateChannel('orders'),
        new PresenceChannel('admin'),
    ];
}
```

## WebSockets
### Laravel Echo Setup
```javascript
// Install Echo
npm install --save-dev laravel-echo pusher-js

// Bootstrap.js
import Echo from 'laravel-echo';
import Pusher from 'pusher-js';

window.Pusher = Pusher;

window.Echo = new Echo({
    broadcaster: 'pusher',
    key: process.env.MIX_PUSHER_APP_KEY,
    cluster: process.env.MIX_PUSHER_APP_CLUSTER,
    encrypted: true
});
```

### Listening to Events
```javascript
// Listen to public channel
Echo.channel('orders')
    .listen('OrderShipped', (e) => {
        console.log(e.order);
    });

// Listen to private channel
Echo.private('orders.' + orderId)
    .listen('OrderShipped', (e) => {
        console.log(e.order);
    });
```

## Real-time Features
### Presence Channels
```php
// Routes/channels.php
Broadcast::channel('room.{roomId}', function ($user, $roomId) {
    return ['id' => $user->id, 'name' => $user->name];
});

// JavaScript
Echo.join(`room.${roomId}`)
    .here((users) => {
        console.log(users);
    })
    .joining((user) => {
        console.log(user.name);
    })
    .leaving((user) => {
        console.log(user.name);
    });
```

### Client Events
```javascript
// Trigger client event
Echo.private('room.1')
    .whisper('typing', {
        name: user.name
    });

// Listen for client event
Echo.private('room.1')
    .listenForWhisper('typing', (e) => {
        console.log(e.name);
    });
```

### Best Practices
1. Use appropriate channel types:
   - Public channels for public data
   - Private channels for authenticated users
   - Presence channels for user presence features

2. Implement proper authentication:
```php
// Broadcast service provider
Broadcast::channel('orders.{orderId}', function ($user, $orderId) {
    return $user->id === Order::findOrNew($orderId)->user_id;
});
```

3. Handle disconnections gracefully:
```javascript
Echo.connector.pusher.connection.bind('disconnected', () => {
    // Handle disconnection
});
```

4. Implement reconnection strategies:
```javascript
Echo.connector.pusher.connection.bind('connected', () => {
    // Resubscribe to channels
});
``` 