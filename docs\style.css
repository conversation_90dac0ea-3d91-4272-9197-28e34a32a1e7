/* Import a modern sans-serif font */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
    --primary-accent: #3B82F6; /* Brighter tech blue */
    --primary-accent-dark: #2563EB; /* Darker tech blue */
    --secondary-color: #1E293B; /* Darker slate */
    --background-light: #F8FAFC; /* Cleaner white background */
    --text-dark: #0F172A; /* Deeper gray text */
    --text-muted: #64748B; /* More muted gray */
    --border-color: #E2E8F0; /* Lighter border */
    --code-background: #1E293B; /* Darker code background */
    --code-text: #E2E8F0; /* Lighter code text */
    --code-border: #334155; /* Darker code border */
    --heading-color: #0F172A; /* Deeper gray heading */
    --heading-gradient: linear-gradient(90deg, #3B82F6 0%, #8B5CF6 100%); /* More vibrant gradient */
    --tech-glow: 0 0 8px rgba(59, 130, 246, 0.5); /* Tech glow effect */
    --circuit-border: 1px dashed rgba(59, 130, 246, 0.3); /* Circuit-like border */
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.7;
    margin: 0;
    padding: 0;
    background-color: var(--background-light);
    color: var(--text-dark);
    font-weight: 400;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

header {
    background: var(--secondary-color);
    color: #fff;
    padding: 2rem 20px;
    text-align: center;
    margin-bottom: 40px;
    box-shadow: var(--tech-glow);
    border-bottom: var(--circuit-border);
    position: relative;
    overflow: hidden;
}

header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #3B82F6, #8B5CF6);
}

header h1 {
    color: #fff;
    margin: 0;
    font-weight: 700;
    font-size: 2.4em;
}

#table-of-contents {
    position: sticky;
    top: 30px;
    left: 30px;
    width: 280px;
    padding: 25px;
    background: #fff;
    border: var(--circuit-border);
    border-radius: 8px;
    box-shadow: var(--tech-glow);
    float: left;
    margin-right: 40px;
    box-sizing: border-box;
    font-size: 0.95em;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

#table-of-contents:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(59, 130, 246, 0.2);
}

#table-of-contents h2 {
    margin-top: 0;
    color: var(--secondary-color);
    font-size: 1.5em;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 12px;
    margin-bottom: 20px;
    font-weight: 600;
}

#table-of-contents ul {
    list-style: none;
    padding: 0;
}

#table-of-contents ul li {
    margin-bottom: 10px;
}

#table-of-contents ul li a {
    text-decoration: none;
    color: var(--primary-accent);
    display: block;
    padding: 6px 0;
    transition: color 0.2s ease-in-out;
}

#table-of-contents ul li a:hover {
    color: var(--primary-accent-dark);
    text-decoration: underline;
}

main {
    margin-left: 360px;
    padding: 40px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

section {
    margin-bottom: 60px;
    padding-bottom: 40px;
    border-bottom: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

section:hover {
    background: rgba(248, 250, 252, 0.5);
    border-radius: 8px;
}

section:last-child {
    border-bottom: none;
    margin-bottom: 30px;
}

h1, h2, h3, h4, h5, h6 {
    color: var(--heading-color);
    margin-top: 40px;
    margin-bottom: 20px;
    font-weight: 700;
    line-height: 1.25;
    letter-spacing: -0.025em;
}

h1 {
    font-size: 2.8em;
    background: var(--heading-gradient);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    display: inline-block;
}

h2 {
    font-size: 2.2em;
    border-bottom: 2px solid var(--border-color);
    padding-bottom: 12px;
}

h3 { font-size: 1.8em; }
h4 { font-size: 1.5em; }
h5 { font-size: 1.3em; }
h6 { font-size: 1.1em; }

p {
    margin-bottom: 15px;
}

pre {
    background: var(--code-background);
    color: var(--code-text);
    border: 1px solid var(--code-border);
    border-radius: 6px;
    padding: 20px;
    overflow-x: auto;
    margin-bottom: 25px;
    font-size: 0.95em;
    line-height: 1.6;
    font-family: 'Fira Code', Consolas, Monaco, 'Andale Mono', monospace;
    box-shadow: var(--tech-glow);
    position: relative;
}

pre::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #3B82F6, #8B5CF6);
    border-radius: 6px 6px 0 0;
}

code {
    font-family: 'Fira Code', Consolas, Monaco, 'Andale Mono', monospace;
    background: rgba(0, 0, 0, 0.05);
    padding: 0.2em 0.4em;
    border-radius: 3px;
}

footer {
    text-align: center;
    padding: 40px 0;
    margin-top: 50px;
    color: var(--text-muted);
    font-size: 0.9em;
    background: var(--secondary-color);
    color: white;
}

footer a {
    color: var(--primary-accent);
    transition: color 0.2s ease;
}

footer a:hover {
    color: white;
    text-decoration: none;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
    #table-of-contents {
        position: static;
        width: auto;
        float: none;
        margin-right: 0;
        margin-bottom: 30px;
        box-shadow: none;
        border: none;
        border-bottom: 1px solid var(--border-color);
        border-radius: 0;
        padding: 20px;
    }

    #table-of-contents h2 {
        border-bottom: none;
        padding-bottom: 0;
        margin-bottom: 10px;
    }

    main {
        margin-left: 0;
        padding: 20px;
        box-shadow: none;
    }

    header {
        padding: 1rem 15px;
    }

    h1 { font-size: 2em; }
    h2 { font-size: 1.8em; padding-bottom: 5px; }
    h3 { font-size: 1.4em; }
    h4 { font-size: 1.2em; }
    h5 { font-size: 1.1em; }
    h6 { font-size: 1em; }

    pre {
        padding: 15px;
    }
}