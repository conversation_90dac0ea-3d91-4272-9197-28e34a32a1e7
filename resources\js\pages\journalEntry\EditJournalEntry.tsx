import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ComboBox } from '@/components/ui/combobox';
import { DatePicker } from '@/components/ui/date-picker';
import { Input } from '@/components/ui/input';
import AppLayout from '@/layouts/app-layout';
import axios from '@/lib/axios';
import { Head } from '@inertiajs/react';
import { isAxiosError } from 'axios';
import React, { useEffect, useState } from 'react';
import { NumericFormat } from 'react-number-format';
import { toast } from 'sonner';

interface JournalEntry {
    id: number;
    TID: string;
    date: string | Date;
    payment_type_id: number;
    account_id: number;
    is_credit: boolean;
    description: string;
    chq_no: string | null;
    amount: number;
    created_by: {
        id: number;
        name: string;
    };
    payment_type: {
        id: number;
        name: string;
    };
    account: {
        id: number;
        name: string;
    };
    pairedEntry?: JournalEntry;
    creditEntry?: JournalEntry;
    debitEntry?: JournalEntry;
}

interface Account {
    id: number;
    name: string;
}

interface PaymentType {
    id: number;
    name: string;
    label: string;
    slug: string;
}

const isAccount = (obj: unknown): obj is Account => {
    return (
        typeof obj === 'object' &&
        obj !== null &&
        'id' in obj &&
        typeof (obj as Account).id === 'number' &&
        'name' in obj &&
        typeof (obj as Account).name === 'string'
    );
};

const isPaymentType = (obj: unknown): obj is PaymentType => {
    return (
        typeof obj === 'object' &&
        obj !== null &&
        'id' in obj &&
        typeof (obj as PaymentType).id === 'number' &&
        'name' in obj &&
        typeof (obj as PaymentType).name === 'string' &&
        'label' in obj &&
        typeof (obj as PaymentType).label === 'string' &&
        ('slug' in obj
            ? typeof (obj as PaymentType).slug === 'string' || (obj as PaymentType).slug === undefined || (obj as PaymentType).slug === null
            : true) // Allow slug to be optional
    );
};

const EditJournalEntry = ({ entryId }: { entryId: number }) => {
    const [entry, setEntry] = useState<JournalEntry | null>(null);
    const [accounts, setAccounts] = useState<Account[]>([]);
    const [paymentTypes, setPaymentTypes] = useState<PaymentType[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState('');

    useEffect(() => {
        const fetchAllData = async () => {
            setLoading(true);
            setError('');
            try {
                const [entryResponse, accountsResponse, paymentTypesResponse] = await Promise.all([
                    axios.get(`/api/journal-entries/${entryId}`),
                    axios.get('/api/accounts'),
                    axios.get('/api/payment-types', {
                    params: {
                        filter_names: [
                            'cust-to-cust',
                            'jv-payment',
                            'bank-payment',
                            'bank-receipt',
                            'cash-payment',
                            'cash-receipt',
                        ],
                    },
                }),
                ]);

                const { entry: fetchedEntryData, paired_entry: pairedEntryData } = entryResponse.data;
                const creditEntryData = fetchedEntryData.is_credit ? fetchedEntryData : pairedEntryData;
                const debitEntryData = fetchedEntryData.is_credit ? pairedEntryData : fetchedEntryData;

                setEntry({
                    ...fetchedEntryData,
                    date: fetchedEntryData.date ? new Date(fetchedEntryData.date) : new Date(), // Ensure date is a Date object
                    pairedEntry: pairedEntryData,
                    creditEntry: creditEntryData,
                    debitEntry: debitEntryData,
                });

                // Process accounts
                let rawAccountsList: unknown[] = [];
                if (accountsResponse.data) {
                    if (Array.isArray(accountsResponse.data.data)) {
                        rawAccountsList = accountsResponse.data.data;
                    } else if (Array.isArray(accountsResponse.data)) {
                        rawAccountsList = accountsResponse.data;
                    }
                }
                const validAccounts = rawAccountsList.filter(isAccount);
                setAccounts(validAccounts);

                // Process payment types
                let rawPaymentTypesList: unknown[] = [];
                if (paymentTypesResponse.data) {
                    if (Array.isArray(paymentTypesResponse.data.data)) {
                        rawPaymentTypesList = paymentTypesResponse.data.data;
                    } else if (Array.isArray(paymentTypesResponse.data)) {
                        rawPaymentTypesList = paymentTypesResponse.data;
                    }
                }
                const validPaymentTypes = rawPaymentTypesList.filter(isPaymentType);
                setPaymentTypes(validPaymentTypes);
            } catch (err) {
                let detailedErrorMessage = 'An unexpected error occurred while fetching data.';
                if (isAxiosError(err)) {
                    console.error('Axios Error:', err);
                    console.error('API Error Request:', err.request);
                    console.error('API Error Config:', err.config);
                    console.error('API Error isAxiosError:', err.isAxiosError);
                    console.error('API Error toJSON:', err.toJSON ? err.toJSON() : 'toJSON not available');
                    console.error('API Error Status:', err.response?.status);
                    console.error('API Error Data:', err.response?.data);

                    if (err.response) {
                        // Server responded with a status code out of the 2xx range
                        const status = err.response.status;
                        switch (status) {
                            case 401:
                                detailedErrorMessage = 'Unauthorized: You do not have permission to access this resource. Please log in again.';
                                break;
                            case 403:
                                detailedErrorMessage = 'Forbidden: You are not allowed to perform this action.';
                                break;
                            case 404:
                                detailedErrorMessage = 'Not Found: The requested resource could not be found on the server.';
                                break;
                            case 500:
                                detailedErrorMessage = 'Server Error: An internal server error occurred. Please try again later.';
                                break;
                            default:
                                if (err.response.data && typeof err.response.data.message === 'string') {
                                    detailedErrorMessage = err.response.data.message;
                                } else if (typeof err.response.data === 'string') {
                                    detailedErrorMessage = `Server Error (Status: ${status}): ${err.response.statusText || 'An issue occurred with the server response.'}`;
                                } else {
                                    detailedErrorMessage = `An error occurred (Status: ${status}). Please try again.`;
                                }
                                break;
                        }
                    } else if (err.request) {
                        // The request was made but no response was received
                        if (err.code === 'ECONNABORTED') {
                            detailedErrorMessage = 'The request timed out. Please check your network connection and try again.';
                        } else {
                            detailedErrorMessage =
                                'Network Error: No response received from the server. Please check your internet connection and ensure the server is running.';
                        }
                        console.error('Error details (no response):', err.message, err.code);
                    } else {
                        // Something happened in setting up the request that triggered an Error
                        detailedErrorMessage = `Request Error: ${err.message || 'An error occurred while setting up the request.'}`;
                    }
                } else if (err instanceof Error && err.name === 'AbortError') {
                    detailedErrorMessage = 'The data fetching operation was cancelled.';
                    console.warn('Fetch operation aborted:', err.message);
                } else {
                    console.error('Non-API Error:', err);
                    detailedErrorMessage = 'An unexpected error occurred: ' + (err instanceof Error ? err.message : String(err));
                }
                setError(detailedErrorMessage);
                // Ensure accounts and paymentTypes are empty arrays in case of error
                setAccounts([]);
                setPaymentTypes([]);
            } finally {
                setLoading(false);
            }
        };

        if (entryId) {
            fetchAllData();
        }
    }, [entryId]);

    if (!entryId) {
        return (
            <AppLayout>
                <div className="p-4">
                    <div className="rounded border border-red-200 bg-red-50 px-4 py-3 text-red-700">Invalid entry ID</div>
                </div>
            </AppLayout>
        );
    }

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        setLoading(true);
        setError('');

        try {
            if (!entry || !entry.creditEntry || !entry.debitEntry) {
                throw new Error('Invalid entry data');
            }

            const formattedDate = entry.date instanceof Date ? entry.date.toISOString().split('T')[0] : entry.date;
            await axios.put(`/api/journal-entries/${entryId}`, {
                date: formattedDate,
                payment_type_id: entry.payment_type_id,
                description: entry.description,
                chq_no: entry.chq_no,
                amount: Math.abs(entry.amount), // Ensure positive amount for API
                credit_account_id: entry.creditEntry.account_id,
                debit_account_id: entry.debitEntry.account_id,
            });

            toast.success('Journal entry updated successfully');
            // Redirect back to DailyBook after successful update
            window.location.href = '/daily-book';
        } catch (err) {
            if (isAxiosError(err)) {
                setError(err.response?.data?.message || 'Failed to update journal entry');
            } else {
                setError('An unexpected error occurred');
            }
        } finally {
            setLoading(false);
        }
    };

    if (loading) {
        return (
            <AppLayout>
                <div className="flex min-h-screen items-center justify-center">
                    <div className="h-32 w-32 animate-spin rounded-full border-b-2 border-gray-900"></div>
                </div>
            </AppLayout>
        );
    }

    if (error) {
        return (
            <AppLayout>
                <div className="p-4">
                    <div className="rounded border border-red-200 bg-red-50 px-4 py-3 text-red-700">{error}</div>
                </div>
            </AppLayout>
        );
    }

    if (!entry) {
        return (
            <AppLayout>
                <div className="flex min-h-screen items-center justify-center p-4">
                    <div>Loading entry data or entry not found...</div>
                </div>
            </AppLayout>
        );
    }

    return (
        <AppLayout>
            <Head title="Edit Journal Entry" />
            <div className="container mx-auto py-6">
                <Card>
                    <CardHeader>
                        <CardTitle>Edit Journal Entry</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSubmit} className="max-w-2xl space-y-4">
                            <div className="space-y-4">
                                {/* Date */}
                                <div className="grid grid-cols-12 items-center gap-4">
                                    <label className="col-span-3 text-right">Date</label>
                                    <div className="col-span-6 lg:col-span-3">
                                        <DatePicker
                                            selected={entry?.date instanceof Date ? entry.date : undefined}
                                            onSelect={(selectedDate) =>
                                                setEntry((prev) => {
                                                    if (!prev) return prev;
                                                    return {
                                                        ...prev,
                                                        date: selectedDate || new Date(), // Fallback to new Date if undefined
                                                    };
                                                })
                                            }
                                        />
                                    </div>
                                </div>

                                {/* Payment Type */}
                                <div className="grid grid-cols-12 items-center gap-4">
                                    <label className="col-span-3 text-right">Payment Type</label>
                                    <div className="col-span-6">
                                        <ComboBox
                                            value={entry?.payment_type_id?.toString() || ''}
                                            onChange={(value: string) => {
                                                setEntry((prev) => {
                                                    if (!prev) return prev;
                                                    return {
                                                        ...prev,
                                                        payment_type_id: parseInt(value),
                                                    };
                                                });
                                            }}
                                            placeholder="Select payment type"
                                            options={
                                                Array.isArray(paymentTypes)
                                                    ? paymentTypes.map((type) => ({
                                                          value: type.id.toString(),
                                                          label: type.label,
                                                      }))
                                                    : []
                                            }
                                        />
                                    </div>
                                </div>

                                {/* Credit Account */}
                                <div className="grid grid-cols-12 items-center gap-4">
                                    <label className="col-span-3 text-right">Credit Account</label>
                                    <div className="col-span-6">
                                        <ComboBox
                                            value={entry?.creditEntry?.account_id ? entry.creditEntry.account_id.toString() : ''}
                                            onChange={(value: string) => {
                                                setEntry((prev) => {
                                                    if (!prev || !prev.creditEntry) return prev;
                                                    return {
                                                        ...prev,
                                                        creditEntry: {
                                                            ...prev.creditEntry,
                                                            account_id: parseInt(value),
                                                        },
                                                    };
                                                });
                                            }}
                                            placeholder="Select credit account"
                                            options={
                                                Array.isArray(accounts)
                                                    ? accounts.map((account) => ({
                                                          value: account.id.toString(),
                                                          label: account.name,
                                                      }))
                                                    : []
                                            }
                                        />
                                    </div>
                                </div>

                                {/* Debit Account */}
                                <div className="grid grid-cols-12 items-center gap-4">
                                    <label className="col-span-3 text-right">Debit Account</label>
                                    <div className="col-span-6">
                                        <ComboBox
                                            value={entry?.debitEntry?.account_id ? entry.debitEntry.account_id.toString() : ''}
                                            onChange={(value: string) => {
                                                setEntry((prev) => {
                                                    if (!prev || !prev.debitEntry) return prev;
                                                    return {
                                                        ...prev,
                                                        debitEntry: {
                                                            ...prev.debitEntry,
                                                            account_id: parseInt(value),
                                                        },
                                                    };
                                                });
                                            }}
                                            placeholder="Select debit account"
                                            options={
                                                Array.isArray(accounts)
                                                    ? accounts.map((account) => ({
                                                          value: account.id.toString(),
                                                          label: account.name,
                                                      }))
                                                    : []
                                            }
                                        />
                                    </div>
                                </div>

                                {/* Description */}
                                <div className="grid grid-cols-12 items-center gap-4">
                                    <label className="col-span-3 text-right">Description</label>
                                    <div className="col-span-6">
                                        <Input
                                            type="text"
                                            id="description"
                                            value={entry?.description || ''}
                                            onChange={(e) =>
                                                setEntry((prev) => {
                                                    if (!prev) return prev;
                                                    return {
                                                        ...prev,
                                                        description: e.target.value,
                                                    };
                                                })
                                            }
                                            placeholder="Enter description"
                                        />
                                    </div>
                                </div>

                                {/* CHQ NO */}
                                <div className="grid grid-cols-12 items-center gap-4">
                                    <label className="col-span-3 text-right">CHQ NO</label>
                                    <div className="col-span-6">
                                        <Input
                                            type="text"
                                            id="chq_no"
                                            value={entry?.chq_no || ''}
                                            onChange={(e) =>
                                                setEntry((prev) => {
                                                    if (!prev) return prev;
                                                    return {
                                                        ...prev,
                                                        chq_no: e.target.value,
                                                    };
                                                })
                                            }
                                            placeholder="Enter CHQ NO"
                                        />
                                    </div>
                                </div>

                                {/* Amount */}
                                <div className="grid grid-cols-12 items-center gap-4">
                                    <label className="col-span-3 text-right">Amount</label>
                                    <div className="col-span-6">
                                        <NumericFormat
                                            value={Math.abs(entry?.amount || 0)}
                                            onValueChange={(values) =>
                                                setEntry((prev) => {
                                                    if (!prev) return prev;
                                                    return {
                                                        ...prev,
                                                        amount: parseFloat(values.value || '0'),
                                                    };
                                                })
                                            }
                                            thousandSeparator={true}
                                            allowNegative={false}
                                            customInput={Input}
                                            placeholder="Enter amount"
                                        />
                                    </div>
                                </div>

                                {/* Buttons */}
                                <div className="grid grid-cols-12 gap-4">
                                    <div className="col-span-3"></div>
                                    <div className="col-span-9 flex gap-4">
                                        <Button type="submit" disabled={loading}>
                                            {loading ? 'Updating...' : 'Update Entry'}
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
};

export default EditJournalEntry;
