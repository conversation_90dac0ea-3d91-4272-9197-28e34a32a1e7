# Advanced Double Accounting System

A modern, **enterprise-grade** accounting web application built with **<PERSON><PERSON> 12** (PHP) and **React 19** (TypeScript), featuring **asynchronous processing**, **complete audit trails**, and **real-time monitoring**. This system provides lightning-fast user experience with robust financial management, including double-entry accounting, multi-currency support, and advanced inventory recalculation capabilities.

---

## Table of Contents
- [🚀 Key Features](#-key-features)
- [⚡ Performance Highlights](#-performance-highlights)
- [🏗️ Tech Stack](#️-tech-stack)
- [📦 Setup & Installation](#-setup--installation)
- [🔄 Asynchronous Recalculation System](#-asynchronous-recalculation-system)
- [📊 Audit Logging & Compliance](#-audit-logging--compliance)
- [📈 Real-time Monitoring](#-real-time-monitoring)
- [🎨 Frontend (React)](#-frontend-react)
- [🔧 Backend (Laravel)](#-backend-laravel)
- [🔐 Authentication (Sanctum)](#-authentication-sanctum)
- [⚙️ Queue Workers & Background Jobs](#️-queue-workers--background-jobs)
- [🏗️ Project Structure](#️-project-structure)
- [🚀 Development Workflow](#-development-workflow)
- [🤝 Contributing](#-contributing)
- [📄 License](#-license)

---

## 🚀 Key Features

### **Enterprise-Grade Performance**
- **⚡ Lightning-Fast Response**: < 1 second response time for all transaction operations
- **🔄 Asynchronous Processing**: Background jobs handle complex recalculations without blocking UI
- **📊 Real-time Monitoring**: Live status tracking of all background operations
- **🛡️ Robust Error Handling**: Automatic retry mechanisms with exponential backoff

### **Complete Audit & Compliance**
- **📋 Full Audit Trail**: Track who changed what, when, and why for all transactions
- **🔍 Change Detection**: Automatic identification of modified fields with before/after values
- **📍 Location Tracking**: IP address and user agent logging for security
- **📈 Activity Reports**: Comprehensive reporting for compliance and debugging

### **Advanced Accounting Features**
- **💰 Double-Entry Bookkeeping**: Robust financial transaction management
- **🌍 Multi-Currency Support**: Handle multiple currencies with automatic rate calculations
- **📊 Inventory Management**: Advanced inventory tracking with average cost calculations
- **💹 P&L Calculations**: Automatic profit/loss calculations for sales transactions
- **🏦 Account Management**: Comprehensive account types (bank, cash, customer, employee, etc.)

### **Modern User Experience**
- **🎨 Modern SPA**: React 19 + Vite frontend with enhanced shadcn/ui components
- **📱 Responsive Design**: Fully responsive UI that works on all devices
- **🔔 Smart Notifications**: Enhanced toast notifications with rich content and actions
- **⚡ Dynamic Tables**: Fast, filterable, and sortable data tables
- **🎯 Intuitive Forms**: Modern modal dialogs with real-time validation
- **🎛️ Enhanced Components**: Custom shadcn components with comprehensive variants and features

## ⚡ Performance Highlights

| **Metric** | **Before** | **After** | **Improvement** |
|------------|------------|-----------|-----------------|
| **User Wait Time** | 10-30 seconds | < 1 second | **30x faster** |
| **Transaction Processing** | Synchronous blocking | Asynchronous background | **Non-blocking** |
| **Error Recovery** | Manual intervention | Automatic retry | **Self-healing** |
| **Audit Capability** | None | Complete trail | **Full compliance** |
| **System Monitoring** | None | Real-time status | **Live visibility** |

---

## 🏗️ Tech Stack

### **Backend**
- **Laravel 12** - Modern PHP framework with advanced features
- **PHP 8.2+** - Latest PHP with performance optimizations
- **MySQL** - Reliable database with proper indexing
- **Laravel Sanctum** - Secure SPA authentication
- **Queue System** - Background job processing with Redis/Database

### **Frontend**
- **React 19** - Latest React with concurrent features
- **TypeScript** - Type-safe development
- **Vite** - Lightning-fast build tool
- **Enhanced shadcn/ui** - Custom enhanced UI components with comprehensive variants
- **TailwindCSS** - Utility-first CSS framework
- **TanStack Table** - Powerful data tables

### **Infrastructure**
- **Laravel Horizon** - Queue monitoring and management
- **Supervisor** - Process management for production
- **Redis** - High-performance caching and queuing
- **Axios** - HTTP client with interceptors

---

## 📦 Setup & Installation

### Prerequisites
- PHP 8.2+
- Composer
- Node.js 18+
- MySQL

### 1. Clone the Repository
```bash
git clone https://github.com/TechnicalExpo/AccSystem.git
cd AccSystem
```

### 2. Backend Setup (Laravel)
```bash
cp .env.example .env
composer install
php artisan key:generate
php artisan migrate
# (Optional) seeders, storage:link, etc.
```

### 3. Frontend Setup (React + Vite)
```bash
npm install
npm run dev # or 'npm run build' for production
```

### 4. Run Project
```bash
composer run dev
```

### 5. Sanctum Configuration
- Ensure `SESSION_DOMAIN` and `SANCTUM_STATEFUL_DOMAINS` are set correctly in your `.env`.
- Middleware registration for Sanctum is done in `bootstrap/app.php` using `withMiddleware()`.
- Frontend must call `/sanctum/csrf-cookie` before login and set `withCredentials: true` in Axios.

### 6. Start Queue Workers (Essential for Async Processing)
```bash
# Start the inventory recalculation queue worker
php artisan queue:work --queue=inventory --tries=3 --timeout=300
```

### 7. Access the App
- Visit [http://localhost:8000](http://localhost:8000) for the backend.
- Visit [http://localhost:5173](http://localhost:5173) for the frontend (Vite default).

---

## 🔄 Asynchronous Recalculation System

### **Overview**
The system uses **background job processing** to handle complex inventory recalculations without blocking the user interface. When users edit purchase or sale transactions, the system provides immediate feedback while processing recalculations asynchronously.

### **How It Works**
```mermaid
graph TD
    A[User Edits Transaction] --> B[Immediate Response < 1s]
    B --> C[Job Dispatched to Queue]
    C --> D[Background Worker Processes]
    D --> E[Recalculates All Affected Transactions]
    E --> F[Updates Running Balances & Rates]
    F --> G[Updates P&L for Sales]
    G --> H[Logs Completion Status]
```

### **Key Components**

#### **1. ProcessInventoryRecalculationJob**
- **Location**: `app/Jobs/ProcessInventoryRecalculationJob.php`
- **Purpose**: Handles complex inventory recalculations in background
- **Features**:
  - Processes transactions in chronological order
  - Updates running currency and PKR balances
  - Recalculates average rates using account formulas
  - Updates P&L entries for affected sales
  - Handles both direct (d) and multiply (m) currency formulas

#### **2. Recalculation Logging**
- **Table**: `recalculation_logs`
- **Tracks**: Job status, timing, errors, and completion
- **Status Flow**: `pending` → `processing` → `completed`/`failed`

#### **3. Queue Configuration**
```php
// Queue: inventory
// Timeout: 5 minutes
// Retries: 3 attempts
// Delay: 2-5 seconds between jobs
```

### **Usage Examples**

#### **Purchase Transaction Update**
```php
// User edits purchase → Immediate response
// Background: ProcessInventoryRecalculationJob dispatched
ProcessInventoryRecalculationJob::dispatch($accountId, $fromDate, $transactionId)
    ->onQueue('inventory')
    ->delay(now()->addSeconds(2));
```

#### **Sale Transaction Update**
```php
// User edits sale → Immediate response
// Background: Recalculates inventory + updates P&L
ProcessInventoryRecalculationJob::dispatch($accountId, $fromDate, $transactionId)
    ->onQueue('inventory')
    ->delay(now()->addSeconds(2));
```

---

## 📊 Audit Logging & Compliance

### **Complete Transaction Audit Trail**
Every transaction change is automatically logged with comprehensive details:

#### **TransactionAuditLog Model**
- **Location**: `app/Models/TransactionAuditLog.php`
- **Table**: `transaction_audit_logs`

#### **What Gets Logged**
```json
{
  "transaction_type": "purchase|sale",
  "transaction_id": "TID12345",
  "inv_no": "P-USD001",
  "action": "created|updated|deleted",
  "old_values": {"rate": 85.50, "amount": 1000},
  "new_values": {"rate": 86.00, "amount": 1200},
  "changed_fields": ["rate", "amount"],
  "user_id": 5,
  "ip_address": "*************",
  "user_agent": "Mozilla/5.0...",
  "notes": "Purchase updated with significant changes"
}
```

#### **Audit Features**
- **Change Detection**: Automatically identifies modified fields
- **User Tracking**: Records who made the change
- **Location Tracking**: IP address and user agent
- **Timestamp Precision**: Exact time of changes
- **Context Notes**: Descriptive notes about the change

#### **Compliance Benefits**
- **Regulatory Compliance**: Meet audit requirements
- **Fraud Detection**: Track suspicious activities
- **Error Investigation**: Trace source of discrepancies
- **User Accountability**: Complete change attribution

---

## 📈 Real-time Monitoring

### **RecalculationStatus Component**
A React component that provides live monitoring of background recalculation jobs.

#### **Features**
- **Live Status Updates**: Auto-refreshes every 5 seconds
- **Progress Indicators**: Shows pending, processing, completed, failed jobs
- **Error Handling**: Displays error messages and retry options
- **System Health**: Overall system status and success rates

#### **Usage in Frontend**
```tsx
import RecalculationStatus from '@/components/RecalculationStatus';

// Account-specific monitoring
<RecalculationStatus
  accountId={selectedAccountId}
  autoRefresh={true}
  refreshInterval={5000}
/>

// System-wide monitoring (Admin Dashboard)
<RecalculationStatus
  showSystemStatus={true}
  autoRefresh={true}
  refreshInterval={10000}
/>
```

#### **API Endpoints**
- `GET /api/recalculation-status/account?account_id=123`
- `GET /api/recalculation-status/system`
- `POST /api/recalculation-status/retry-failed`
- `DELETE /api/recalculation-status/clear-old`

---

## 🎨 Enhanced shadcn/ui Components

### **Custom Component Library**
We've significantly enhanced the standard shadcn/ui components to provide enterprise-grade functionality with comprehensive variants and advanced features.

#### **🔘 Enhanced Button Component**
- **Semantic Variants**: `success`, `warning`, `info`, `destructive` with proper color schemes
- **Gradient Variants**: `gradient`, `gradient-success`, `gradient-warning`, `gradient-info`
- **Size Variants**: `xs`, `sm`, `default`, `lg`, `xl` + icon-specific sizes
- **Rounded Variants**: `none`, `sm`, `default`, `lg`, `xl`, `full`
- **Loading States**: Built-in spinner with customizable loading text
- **Icon Positioning**: `leftIcon`, `rightIcon` with proper spacing
- **Advanced Features**: Automatic icon-only detection, disabled state handling

```tsx
// Example usage
<Button
  variant="gradient-success"
  size="lg"
  rounded="full"
  loading={isSubmitting}
  loadingText="Saving..."
  leftIcon={<Save />}
>
  Save Changes
</Button>
```

#### **📋 Enhanced ComboBox Component**
- **Size Variants**: `sm`, `md`, `lg` with responsive widths
- **Loading States**: Spinner animation with loading text
- **Clearable Functionality**: X button to clear selection
- **Disabled State**: Proper styling and interaction blocking
- **Rich Options**: Support for icons, descriptions, disabled states
- **Option Grouping**: Organized options with group headers
- **Custom Rendering**: Custom option rendering with renderOption prop
- **Enhanced Search**: Search by label, value, or description

```tsx
// Example usage
<ComboBox
  size="lg"
  clearable
  loading={isLoading}
  options={[
    {
      value: "user1",
      label: "John Doe",
      description: "Software Engineer",
      icon: <User className="h-4 w-4" />
    }
  ]}
  groups={[
    {
      label: "Actions",
      options: [...]
    }
  ]}
  renderOption={(option) => <CustomOptionComponent />}
/>
```

#### **🔔 Enhanced Toast Component (Sonner)**
- **Preset Functions**: `toast.success()`, `toast.error()`, `toast.warning()`, `toast.info()`
- **Position Variants**: All 6 positions (top/bottom + left/center/right)
- **Rich Content**: Custom React components as toast content
- **Action Buttons**: Primary and cancel actions with callbacks
- **Animation Variants**: `slide`, `fade`, `scale` animations
- **Advanced Features**: Promise toasts, loading states, dismissal

```tsx
// Example usage
toast.success({
  title: "Payment Successful",
  description: "Your payment has been processed.",
  action: {
    label: "View Receipt",
    onClick: () => navigate('/receipt')
  },
  cancel: {
    label: "Dismiss",
    onClick: () => toast.dismiss()
  }
})
```

### **🎯 Component Showcase**
Visit `/component-showcase` to see all enhanced components in action with live examples and usage demonstrations.

```bash
# Access the showcase
http://localhost:8000/component-showcase
```

### **📦 Component Features**
- **TypeScript Support**: Comprehensive type definitions for all variants
- **Accessibility**: ARIA attributes and keyboard navigation
- **Dark Mode**: Full dark mode support with proper theming
- **Responsive Design**: Works seamlessly across all screen sizes
- **Performance**: Optimized rendering with React.memo and useMemo
- **Customization**: Extensive customization options via props and CSS variables

---

## 🎨 Frontend (React)

### **Modern React Architecture**
- **Location**: `resources/js/`
- **Framework**: React 19 with TypeScript
- **Build Tool**: Vite for lightning-fast development
- **UI Library**: shadcn/ui components (DataTable, Dialog, Button, etc.)

### **Key Components**
- **RecalculationStatus.tsx**: Real-time job monitoring
- **Dynamic Data Tables**: Fast, filterable tables with TanStack Table
- **Modern Forms**: Real-time validation with error handling
- **Toast Notifications**: Sonner for user feedback

### **State Management**
- React hooks for local state
- Axios for API communication with credentials
- Real-time updates via polling

---

## 🔧 Backend (Laravel)

### **API-First Architecture**
- **Routes**: `routes/api.php` for API, `routes/web.php` for SPA
- **Controllers**: `app/Http/Controllers/` with resource controllers
- **Models**: `app/Models/` with relationships and scopes

### **Key Controllers**
- **PurchaseController**: Handles purchase transactions with async recalculation
- **SaleController**: Manages sales with P&L calculations
- **RecalculationStatusController**: Provides job monitoring APIs
- **TransactionAuditLog**: Audit trail management

### **Background Jobs**
- **ProcessInventoryRecalculationJob**: Main recalculation engine
- **Queue Configuration**: Redis/Database with proper error handling
- **Job Monitoring**: Real-time status tracking

---

## 🔐 Authentication (Sanctum)

### **Secure SPA Authentication**
- **Laravel Sanctum**: Stateful authentication for SPAs
- **CSRF Protection**: Must call `/sanctum/csrf-cookie` before login
- **Credential Handling**: Axios configured with `withCredentials: true`
- **Domain Configuration**: Proper setup in `.env` for cookies

### **Security Features**
- Role-based access control
- IP address logging for audit trail
- Session management
- CSRF token validation

---

## ⚙️ Queue Workers & Background Jobs

### **Development Setup**
```bash
# Start queue worker for development
php artisan queue:work --queue=inventory --tries=3 --timeout=300 --verbose
```

### **Production Setup with Supervisor**
```ini
[program:laravel-inventory-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /path/to/your/app/artisan queue:work --queue=inventory --tries=3 --timeout=300
autostart=true
autorestart=true
user=www-data
numprocs=2
redirect_stderr=true
stdout_logfile=/var/log/supervisor/laravel-worker.log
```

### **Queue Monitoring**
```bash
# Monitor queue status
php artisan queue:monitor inventory

# View failed jobs
php artisan queue:failed

# Retry failed jobs
php artisan queue:retry all

# Clear all jobs (development only)
php artisan queue:clear
```

### **Laravel Horizon (Recommended for Production)**
```bash
# Install Horizon
composer require laravel/horizon

# Publish configuration
php artisan horizon:install

# Start Horizon
php artisan horizon
```

---

## 🏗️ Project Structure
```
AccSystem/
├── app/
│   ├── Http/Controllers/
│   │   ├── Transaction/
│   │   │   ├── PurchaseController.php     # Async purchase processing
│   │   │   └── SaleController.php         # Async sale processing
│   │   └── RecalculationStatusController.php # Job monitoring API
│   ├── Jobs/
│   │   └── ProcessInventoryRecalculationJob.php # Background recalculation
│   └── Models/
│       ├── TransactionAuditLog.php        # Audit trail model
│       ├── Account.php                    # Account management
│       └── JournalEntry.php               # Double-entry transactions
├── database/
│   ├── migrations/
│   │   ├── create_transaction_audit_logs_table.php
│   │   └── add_missing_columns_to_recalculation_logs.php
│   └── schema.sql                         # Complete database schema
├── resources/js/
│   ├── components/
│   │   ├── ui/                           # Enhanced shadcn/ui components
│   │   │   ├── button.tsx                # Enhanced Button with variants
│   │   │   ├── combobox.tsx              # Enhanced ComboBox with features
│   │   │   └── sonner.tsx                # Enhanced Toast with presets
│   │   ├── RecalculationStatus.tsx       # Real-time monitoring
│   │   └── RecalculationStatusExample.tsx # Usage examples
│   ├── Pages/
│   │   ├── ComponentShowcase.tsx         # Enhanced components showcase
│   │   └── ...                           # Other React pages
│   └── lib/                              # Utilities and configs
├── routes/
│   ├── api.php                           # API routes with monitoring endpoints
│   └── web.php                           # SPA routes + component showcase
└── storage/logs/                         # Application logs
```

---

## 🚀 Development Workflow

### **Frontend Development**
```bash
# Start Vite development server with hot reload
npm run dev

# Build for production
npm run build

# Lint and format code
npm run lint
npm run format
```

### **Backend Development**
```bash
# Start Laravel development server
php artisan serve

# Run migrations
php artisan migrate

# Start queue worker (essential for async features)
php artisan queue:work --queue=inventory --tries=3 --timeout=300 --verbose
```

### **Database Management**
```bash
# Create new migration
php artisan make:migration create_new_table

# Run migrations
php artisan migrate

# Rollback migrations
php artisan migrate:rollback

# Fresh migration with seeding
php artisan migrate:fresh --seed
```

### **Monitoring & Debugging**
```bash
# Monitor Laravel logs
tail -f storage/logs/laravel.log

# Monitor queue jobs
php artisan queue:monitor inventory

# Check recalculation status
curl http://localhost:8000/api/recalculation-status/system
```

---

## 🤝 Contributing
### **Code Standards**
- Follow PSR-12 coding standards for PHP
- Use TypeScript strict mode for frontend
- Write meaningful commit messages
- Include tests for new features
- Update documentation for API changes

### **Pull Request Process**
1. Fork the repository
2. Create a feature branch
3. Make your changes with tests
4. Update documentation
5. Submit pull request with detailed description

---

## 📄 License
[MIT License](LICENSE)

---

## 📚 References & Documentation

### **Framework Documentation**
- [Laravel 12 Documentation](https://laravel.com/docs/12.x)
- [React 19 Documentation](https://react.dev/)
- [Laravel Sanctum](https://laravel.com/docs/12.x/sanctum)
- [Laravel Queues](https://laravel.com/docs/12.x/queues)

### **UI & Frontend**
- [shadcn/ui Components](https://ui.shadcn.com/)
- [TailwindCSS](https://tailwindcss.com/)
- [Vite Build Tool](https://vitejs.dev/)
- [TanStack Table](https://tanstack.com/table)

### **Infrastructure**
- [Laravel Horizon](https://laravel.com/docs/12.x/horizon)
- [Supervisor Process Control](http://supervisord.org/)
- [Redis Documentation](https://redis.io/documentation)

---

## 🏆 System Achievements

### **Performance Metrics**
- **30x faster** user response times
- **100% non-blocking** transaction processing
- **Zero downtime** recalculations
- **Complete audit coverage** for compliance

### **Enterprise Features**
- ✅ **Asynchronous Processing**: Background job system
- ✅ **Complete Audit Trail**: Full transaction logging
- ✅ **Real-time Monitoring**: Live status tracking
- ✅ **Error Recovery**: Automatic retry mechanisms
- ✅ **Scalable Architecture**: Queue-based processing
- ✅ **Enhanced UI Components**: Custom shadcn/ui with comprehensive variants
- ✅ **Component Showcase**: Live demonstration of all enhanced components
- ✅ **Type Safety**: Full TypeScript coverage
- ✅ **Security**: Comprehensive authentication & authorization

---

## 👥 Maintainers & Credits
- **Architecture**: Enterprise-grade Laravel + React system
- **Performance**: Asynchronous processing with queue workers
- **UI/UX**: Modern design with shadcn/ui and TailwindCSS
- **Security**: Laravel Sanctum with comprehensive audit logging

---

## 📞 Support & Contact
For support, feature requests, or questions:
- **Issues**: Open a GitHub issue
- **Documentation**: Check the comprehensive guides above
- **Performance**: Monitor using built-in RecalculationStatus component
- **Logs**: Check `storage/logs/laravel.log` for detailed information
