import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ComboBox } from '@/components/ui/combobox';
import { DatePicker } from '@/components/ui/date-picker';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { BreadcrumbItem } from '@/types';
import axios from 'axios';
import { useEffect, useState } from 'react';
import { NumericFormat } from 'react-number-format';
import { toast } from 'sonner';
import { Head } from '@inertiajs/react';

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Journal Entry', href: '/journal-entry' },
];

interface Account {
    id: number;
    name: string;
    description?: string;
}

interface PaymentType {
    id: number;
    name: string;
    label: string;
}

interface ComboBoxOption {
    value: string;
    label: string;
}

export default function SingleJournalEntry() {
    const [date, setDate] = useState<Date | undefined>(new Date());
    const [loading, setLoading] = useState(false);
    const [paymentType, setPaymentType] = useState('');
    const [creditAccount, setCreditAccount] = useState('');
    const [debitAccount, setDebitAccount] = useState('');
    const [description, setDescription] = useState('');
    const [chqNo, setChqNo] = useState('');
    const [amount, setAmount] = useState('');
    const [accounts, setAccounts] = useState<ComboBoxOption[]>([]);
    const [paymentTypes, setPaymentTypes] = useState<ComboBoxOption[]>([]);

    // Fetch accounts and payment types
    useEffect(() => {
        const fetchData = async () => {
            try {
                const [accountsRes, paymentTypesRes] = await Promise.all([
                    axios.get<Account[]>('/api/accounts'),
                    axios.get<PaymentType[]>('/api/payment-types', {
                        params: {
                            filter_names: [
                                'cust-to-cust',
                                'jv-payment',
                                'checked',
                            ],
                        },
                    }),
                ]);

                // Transform accounts data
                // label: `(${account.id}) ${account.name}` if we want to show the id with the name
                setAccounts(
                    accountsRes.data.map((account: Account) => ({
                        value: account.id.toString(),
                        label: account.name,
                    })),
                );

                // Transform payment types data
                setPaymentTypes(
                    paymentTypesRes.data.map((type: PaymentType) => ({
                        value: type.id.toString(),
                        label: type.label,
                    })),
                );
            } catch (err) {
                console.error('Error fetching data:', err);
                toast.error('Failed to load data');
            }
        };

        fetchData();
    }, []);

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!date || !paymentType || !creditAccount || !debitAccount || !amount) {
            toast.error('Please fill in all required fields');
            return;
        }

        // Helper to format date as YYYY-MM-DD in local time
        const formatDateLocal = (d: Date) => {
            const year = d.getFullYear();
            const month = (d.getMonth() + 1).toString().padStart(2, '0');
            const day = d.getDate().toString().padStart(2, '0');
            return `${year}-${month}-${day}`;
        };

        setLoading(true);
        try {
            await axios.post('/api/journal-entries', {
                date: formatDateLocal(date),
                payment_type_id: paymentType,
                credit_account_id: creditAccount,
                debit_account_id: debitAccount,
                description,
                chq_no: chqNo,
                amount: parseFloat(amount.replace(/,/g, '')),
            });

            toast.success('Journal entry created successfully');
            // Reset form
            setDate(new Date());
            setPaymentType('');
            setCreditAccount('');
            setDebitAccount('');
            setDescription('');
            setChqNo('');
            setAmount('');
        } catch (err) {
            console.error('Error creating journal entry:', err);
            toast.error('Failed to create journal entry');
        } finally {
            setLoading(false);
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Single Journal Entry" />
            <div className="container mx-auto py-6">
                <Card>
                    <CardHeader>
                        <CardTitle>Single Journal Entry</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSubmit} className="max-w-2xl space-y-4">
                            <div className="space-y-4">
                                <div className="grid grid-cols-12 items-center gap-4">
                                    <label className="col-span-3 text-right">Date</label>
                                    <div className="col-span-6 lg:col-span-3">
                                        <DatePicker selected={date} onSelect={setDate} />
                                    </div>
                                </div>

                                <div className="grid grid-cols-12 items-center gap-4">
                                    <label className="col-span-3 text-right">Payment Type</label>
                                    <div className="col-span-6">
                                        <ComboBox
                                            value={paymentType}
                                            onChange={setPaymentType}
                                            options={paymentTypes}
                                            placeholder="Select payment type"
                                        />
                                    </div>
                                </div>

                                <div className="grid grid-cols-12 items-center gap-4">
                                    <label className="col-span-3 text-right">Credit Account</label>
                                    <div className="col-span-6">
                                        <ComboBox
                                            value={creditAccount}
                                            onChange={setCreditAccount}
                                            options={accounts}
                                            placeholder="Select credit account"
                                        />
                                    </div>
                                </div>

                                <div className="grid grid-cols-12 items-center gap-4">
                                    <label className="col-span-3 text-right">Debit Account</label>
                                    <div className="col-span-6">
                                        <ComboBox
                                            value={debitAccount}
                                            onChange={setDebitAccount}
                                            options={accounts}
                                            placeholder="Select debit account"
                                        />
                                    </div>
                                </div>

                                <div className="grid grid-cols-12 items-center gap-4">
                                    <label className="col-span-3 text-right">Description</label>
                                    <div className="col-span-6">
                                        <Textarea
                                            value={description}
                                            onChange={(e) => setDescription(e.target.value)}
                                            placeholder="Enter description"
                                        />
                                    </div>
                                </div>

                                <div className="grid grid-cols-12 items-center gap-4">
                                    <label className="col-span-3 text-right">CHQ NO</label>
                                    <div className="col-span-6">
                                        <Input value={chqNo} onChange={(e) => setChqNo(e.target.value)} placeholder="Enter CHQ NO" />
                                    </div>
                                </div>

                                <div className="grid grid-cols-12 items-center gap-4">
                                    <label className="col-span-3 text-right">Amount</label>
                                    <div className="col-span-6">
                                        <NumericFormat
                                            value={amount}
                                            onValueChange={(values) => setAmount(values.value)}
                                            thousandSeparator={true}
                                            allowNegative={false}
                                            customInput={Input}
                                            placeholder="Enter amount"
                                        />
                                    </div>
                                </div>

                                <div className="grid grid-cols-12 gap-4">
                                    <div className="col-span-3"></div>
                                    <div className="col-span-9 flex gap-4">
                                        <Button type="submit" disabled={loading}>
                                            {loading ? 'Creating...' : 'Submit'}
                                        </Button>
                                        <Button
                                            type="button"
                                            variant="outline"
                                            onClick={() => {
                                                setDate(new Date());
                                                setPaymentType('');
                                                setCreditAccount('');
                                                setDebitAccount('');
                                                setDescription('');
                                                setChqNo('');
                                                setAmount('');
                                            }}
                                        >
                                            Reset
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
