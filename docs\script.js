document.addEventListener('DOMContentLoaded', function() {
    // Smooth scrolling for table of contents links
    document.querySelectorAll('#table-of-contents a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();

            const targetId = this.getAttribute('href');
            const targetElement = document.querySelector(targetId);

            if (targetElement) {
                window.scrollTo({
                    top: targetElement.offsetTop - 70, // Adjust offset for fixed header/nav if any
                    behavior: 'smooth'
                });
            }
        });
    });

    // Optional: Highlight active section in TOC as user scrolls
    // This is a more advanced feature and requires more complex JS
    // For this basic implementation, we'll stick to smooth scrolling.
});