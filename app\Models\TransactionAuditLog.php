<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TransactionAuditLog extends Model
{
    protected $fillable = [
        'transaction_type',
        'transaction_id',
        'inv_no',
        'action',
        'old_values',
        'new_values',
        'changed_fields',
        'user_id',
        'ip_address',
        'user_agent',
        'notes'
    ];

    protected $casts = [
        'old_values' => 'array',
        'new_values' => 'array',
        'changed_fields' => 'array',
    ];

    /**
     * Get the user who performed the action
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Log a transaction audit event
     */
    public static function logTransactionChange(
        string $transactionType,
        string $transactionId,
        string $invNo,
        string $action,
        array $oldValues = [],
        array $newValues = [],
        ?int $userId = null,
        ?string $notes = null
    ): self {
        $changedFields = [];
        
        // Identify changed fields
        foreach ($newValues as $field => $newValue) {
            $oldValue = $oldValues[$field] ?? null;
            if ($oldValue != $newValue) {
                $changedFields[] = $field;
            }
        }

        return self::create([
            'transaction_type' => $transactionType,
            'transaction_id' => $transactionId,
            'inv_no' => $invNo,
            'action' => $action,
            'old_values' => $oldValues,
            'new_values' => $newValues,
            'changed_fields' => $changedFields,
            'user_id' => $userId ?? auth()->id(),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'notes' => $notes
        ]);
    }

    /**
     * Get audit logs for a specific transaction
     */
    public static function getTransactionHistory(string $invNo, string $transactionType = null)
    {
        $query = self::with('user')
            ->where('inv_no', $invNo)
            ->orderBy('created_at', 'desc');

        if ($transactionType) {
            $query->where('transaction_type', $transactionType);
        }

        return $query->get();
    }

    /**
     * Get recent audit activity
     */
    public static function getRecentActivity(int $limit = 50)
    {
        return self::with('user')
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get audit summary for a user
     */
    public static function getUserActivitySummary(int $userId, int $days = 30)
    {
        return self::where('user_id', $userId)
            ->where('created_at', '>=', now()->subDays($days))
            ->selectRaw('
                action,
                transaction_type,
                COUNT(*) as count,
                DATE(created_at) as date
            ')
            ->groupBy('action', 'transaction_type', 'date')
            ->orderBy('date', 'desc')
            ->get();
    }
}
