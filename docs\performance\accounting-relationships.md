# Laravel 12 Accounting Relationships and Features Guide

## Table of Contents
1. [Advanced Relationships](#advanced-relationships)
2. [Financial Reports](#financial-reports)
3. [Reconciliation Features](#reconciliation-features)
4. [Budget Management](#budget-management)
5. [Tax Management](#tax-management)

## Advanced Relationships

### 1. Account Hierarchies
```php
Schema::create('account_hierarchies', function (Blueprint $table) {
    $table->id();
    $table->foreignId('parent_account_id')->constrained('accounts')->onDelete('restrict');
    $table->foreignId('child_account_id')->constrained('accounts')->onDelete('restrict');
    $table->integer('level');
    $table->string('path');
    $table->timestamps();

    $table->unique(['parent_account_id', 'child_account_id']);
    $table->index('path');
});

class Account extends Model
{
    public function parent()
    {
        return $this->belongsToMany(Account::class, 'account_hierarchies', 
            'child_account_id', 'parent_account_id')
            ->withPivot('level', 'path');
    }

    public function children()
    {
        return $this->belongsToMany(Account::class, 'account_hierarchies', 
            'parent_account_id', 'child_account_id')
            ->withPivot('level', 'path');
    }

    public function getAllChildrenBalances()
    {
        return $this->children()
            ->with('transactionLines')
            ->get()
            ->map(function ($account) {
                return [
                    'account' => $account->name,
                    'balance' => $account->transactionLines->sum('amount')
                ];
            });
    }
}
```

### 2. Recurring Transactions
```php
Schema::create('recurring_transactions', function (Blueprint $table) {
    $table->id();
    $table->string('title');
    $table->string('frequency'); // daily, weekly, monthly, quarterly, yearly
    $table->integer('day_of_month')->nullable();
    $table->integer('month_of_year')->nullable();
    $table->date('start_date');
    $table->date('end_date')->nullable();
    $table->date('last_generated')->nullable();
    $table->json('transaction_template');
    $table->boolean('is_active')->default(true);
    $table->timestamps();

    $table->index(['frequency', 'is_active', 'start_date', 'end_date']);
});

class RecurringTransaction extends Model
{
    protected $casts = [
        'transaction_template' => 'array',
        'start_date' => 'date',
        'end_date' => 'date',
        'last_generated' => 'date'
    ];

    public function generateTransaction()
    {
        return DB::transaction(function () {
            $transaction = Transaction::create([
                'journal_id' => $this->transaction_template['journal_id'],
                'description' => $this->transaction_template['description'],
                'transaction_date' => now(),
                // ... other fields
            ]);

            foreach ($this->transaction_template['lines'] as $line) {
                $transaction->lines()->create($line);
            }

            $this->update(['last_generated' => now()]);
            return $transaction;
        });
    }
}
```

## Financial Reports

### 1. Report Templates
```php
Schema::create('report_templates', function (Blueprint $table) {
    $table->id();
    $table->string('name');
    $table->string('type'); // balance_sheet, income_statement, cash_flow
    $table->json('structure');
    $table->json('formatting');
    $table->boolean('is_system')->default(false);
    $table->timestamps();
});

class ReportTemplate extends Model
{
    protected $casts = [
        'structure' => 'array',
        'formatting' => 'array'
    ];

    public function generate($startDate, $endDate)
    {
        $data = collect($this->structure)->map(function ($section) use ($startDate, $endDate) {
            return [
                'title' => $section['title'],
                'accounts' => Account::whereIn('id', $section['account_ids'])
                    ->with(['transactionLines' => function ($query) use ($startDate, $endDate) {
                        $query->whereBetween('transaction_date', [$startDate, $endDate]);
                    }])
                    ->get()
                    ->map(function ($account) {
                        return [
                            'name' => $account->name,
                            'balance' => $account->transactionLines->sum('amount')
                        ];
                    })
            ];
        });

        return [
            'template' => $this->name,
            'period' => [
                'start' => $startDate,
                'end' => $endDate
            ],
            'data' => $data
        ];
    }
}
```

### 2. Custom Financial Statements
```php
class FinancialStatement
{
    public function generateBalanceSheet($date)
    {
        return Cache::tags(['financial_statements'])
            ->remember("balance_sheet_{$date}", 3600, function () use ($date) {
                return [
                    'assets' => $this->getAssets($date),
                    'liabilities' => $this->getLiabilities($date),
                    'equity' => $this->getEquity($date)
                ];
            });
    }

    protected function getAssets($date)
    {
        return Account::whereHas('type', function ($query) {
            $query->where('name', 'Asset');
        })->with(['transactionLines' => function ($query) use ($date) {
            $query->where('transaction_date', '<=', $date);
        }])->get()->map(function ($account) {
            return [
                'account' => $account->name,
                'balance' => $account->transactionLines->sum(function ($line) {
                    return $line->debit - $line->credit;
                })
            ];
        });
    }
}
```

## Reconciliation Features

### 1. Bank Reconciliation
```php
Schema::create('bank_statements', function (Blueprint $table) {
    $table->id();
    $table->foreignId('account_id')->constrained()->onDelete('restrict');
    $table->date('statement_date');
    $table->decimal('opening_balance', 15, 4);
    $table->decimal('closing_balance', 15, 4);
    $table->boolean('is_reconciled')->default(false);
    $table->timestamp('reconciled_at')->nullable();
    $table->foreignId('reconciled_by')->nullable()->constrained('users');
    $table->timestamps();

    $table->index(['account_id', 'statement_date']);
});

Schema::create('reconciliation_items', function (Blueprint $table) {
    $table->id();
    $table->foreignId('bank_statement_id')->constrained()->onDelete('cascade');
    $table->foreignId('transaction_line_id')->nullable()->constrained()->onDelete('restrict');
    $table->date('transaction_date');
    $table->string('reference');
    $table->decimal('amount', 15, 4);
    $table->boolean('is_matched')->default(false);
    $table->timestamps();

    $table->index(['bank_statement_id', 'is_matched']);
});
```

## Budget Management

### 1. Budget Structure
```php
Schema::create('budgets', function (Blueprint $table) {
    $table->id();
    $table->string('name');
    $table->foreignId('fiscal_year_id')->constrained()->onDelete('restrict');
    $table->date('start_date');
    $table->date('end_date');
    $table->string('status'); // draft, approved, closed
    $table->timestamps();
});

Schema::create('budget_lines', function (Blueprint $table) {
    $table->id();
    $table->foreignId('budget_id')->constrained()->onDelete('cascade');
    $table->foreignId('account_id')->constrained()->onDelete('restrict');
    $table->decimal('amount', 15, 4);
    $table->json('monthly_distribution')->nullable();
    $table->text('notes')->nullable();
    $table->timestamps();

    $table->unique(['budget_id', 'account_id']);
});

class Budget extends Model
{
    public function getVariance()
    {
        return $this->budgetLines()
            ->with(['account.transactionLines' => function ($query) {
                $query->whereBetween('transaction_date', [$this->start_date, $this->end_date]);
            }])
            ->get()
            ->map(function ($line) {
                $actual = $line->account->transactionLines->sum('amount');
                return [
                    'account' => $line->account->name,
                    'budgeted' => $line->amount,
                    'actual' => $actual,
                    'variance' => $line->amount - $actual,
                    'variance_percentage' => $line->amount ? (($actual - $line->amount) / $line->amount) * 100 : 0
                ];
            });
    }
}
```

## Tax Management

### 1. Tax Configuration
```php
Schema::create('tax_rates', function (Blueprint $table) {
    $table->id();
    $table->string('name');
    $table->decimal('rate', 5, 2);
    $table->boolean('is_compound')->default(false);
    $table->boolean('is_active')->default(true);
    $table->timestamps();
});

Schema::create('tax_components', function (Blueprint $table) {
    $table->id();
    $table->foreignId('transaction_line_id')->constrained()->onDelete('cascade');
    $table->foreignId('tax_rate_id')->constrained()->onDelete('restrict');
    $table->decimal('taxable_amount', 15, 4);
    $table->decimal('tax_amount', 15, 4);
    $table->timestamps();

    $table->index(['transaction_line_id', 'tax_rate_id']);
});

class TaxCalculator
{
    public function calculateTax($amount, $taxRates)
    {
        $total = 0;
        $breakdown = [];

        foreach ($taxRates as $rate) {
            $taxAmount = $rate->is_compound 
                ? ($amount + $total) * ($rate->rate / 100)
                : $amount * ($rate->rate / 100);

            $total += $taxAmount;
            $breakdown[] = [
                'rate' => $rate,
                'amount' => $taxAmount
            ];
        }

        return [
            'total_tax' => $total,
            'breakdown' => $breakdown
        ];
    }
}
``` 