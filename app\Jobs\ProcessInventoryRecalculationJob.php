<?php

namespace App\Jobs;

use App\Models\JournalEntry;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class ProcessInventoryRecalculationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 300; // 5 minutes timeout
    public $tries = 3;
    public $backoff = [10, 30, 60]; // Backoff delays in seconds

    protected $accountId;
    protected $fromDate;
    protected $triggerTransactionId;

    /**
     * Create a new job instance.
     */
    public function __construct($accountId, $fromDate, $triggerTransactionId = null)
    {
        $this->accountId = $accountId;
        $this->fromDate = $fromDate;
        $this->triggerTransactionId = $triggerTransactionId;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $startTime = microtime(true);
        
        Log::info("Starting inventory recalculation job", [
            'account_id' => $this->accountId,
            'from_date' => $this->fromDate,
            'trigger_transaction_id' => $this->triggerTransactionId,
            'job_id' => $this->job ? $this->job->getJobId() : 'unknown'
        ]);

        try {
            // Update recalculation log status to 'processing'
            $this->updateRecalculationStatus('processing');

            DB::transaction(function () {
                $this->performRecalculation();
            });

            // Update recalculation log status to 'completed'
            $this->updateRecalculationStatus('completed');

            $endTime = microtime(true);
            $duration = round($endTime - $startTime, 2);

            Log::info("Inventory recalculation job completed successfully", [
                'account_id' => $this->accountId,
                'duration_seconds' => $duration,
                'job_id' => $this->job ? $this->job->getJobId() : 'unknown'
            ]);

        } catch (\Exception $e) {
            $this->updateRecalculationStatus('failed', $e->getMessage());
            
            Log::error("Inventory recalculation job failed", [
                'account_id' => $this->accountId,
                'error' => $e->getMessage(),
                'job_id' => $this->job ? $this->job->getJobId() : 'unknown',
                'trace' => $e->getTraceAsString()
            ]);

            throw $e; // Re-throw to trigger retry mechanism
        }
    }

    /**
     * Perform the actual recalculation logic
     */
    private function performRecalculation(): void
    {
        // Load the account to get currency information
        $account = \App\Models\Account::with('currency')->find($this->accountId);
        if (!$account || !$account->currency_id) {
            Log::warning("Account ID {$this->accountId} is not a currency account. Skipping recalculation.");
            return;
        }

        // Get all transactions from the specified date onwards for this account
        $transactions = JournalEntry::where('account_id', $this->accountId)
            ->where('date', '>=', $this->fromDate)
            ->orderBy('date', 'asc')
            ->orderBy('id', 'asc')
            ->get();

        if ($transactions->isEmpty()) {
            Log::info("No transactions found for recalculation", [
                'account_id' => $this->accountId,
                'from_date' => $this->fromDate
            ]);
            return;
        }

        // Find the last transaction before our batch to get starting balances
        $firstTransactionInBatch = $transactions->first();
        $latestTxBeforeBatch = JournalEntry::where('account_id', $this->accountId)
            ->where(function ($query) use ($firstTransactionInBatch) {
                $query->where('date', '<', $firstTransactionInBatch->date)
                      ->orWhere(function ($query) use ($firstTransactionInBatch) {
                          $query->where('date', '=', $firstTransactionInBatch->date)
                                ->where('id', '<', $firstTransactionInBatch->id);
                      });
            })
            ->orderBy('date', 'desc')
            ->orderBy('id', 'desc')
            ->first();

        // Initialize running balances from last transaction or zero
        $currentCurrencyBalance = $latestTxBeforeBatch ? (float)$latestTxBeforeBatch->running_currency_balance : 0.0;
        $currentPkrValue = $latestTxBeforeBatch ? (float)$latestTxBeforeBatch->running_pkr_balance : 0.0;

        Log::info("Initial state before recalculation", [
            'account_id' => $this->accountId,
            'currency_balance' => $currentCurrencyBalance,
            'pkr_value' => $currentPkrValue,
            'total_transactions' => $transactions->count()
        ]);

        // Collect sales transactions that need P&L updates
        $salesToUpdate = [];
        $processedCount = 0;

        // Process each transaction
        foreach ($transactions as $transaction) {
            $this->recalculateTransaction($transaction, $account, $currentCurrencyBalance, $currentPkrValue, $salesToUpdate);
            $processedCount++;

            if ($processedCount % 10 == 0) {
                Log::info("Processed {$processedCount}/{$transactions->count()} transactions");
            }
        }

        // Now update all the P&L entries for affected sales
        if (count($salesToUpdate) > 0) {
            Log::info("Updating P&L entries for " . count($salesToUpdate) . " sales affected by rate changes");
            foreach ($salesToUpdate as $saleUpdate) {
                try {
                    $this->updateSalePnlEntry(
                        $saleUpdate['tid'],
                        $saleUpdate['inv_no'],
                        $saleUpdate['new_cogs_pkr']
                    );
                } catch (\Exception $e) {
                    Log::error("Failed to update P&L for sale TID: {$saleUpdate['tid']}, InvNo: {$saleUpdate['inv_no']}. Error: " . $e->getMessage());
                }
            }
        }

        Log::info("Recalculation completed", [
            'account_id' => $this->accountId,
            'total_processed' => $processedCount,
            'pnl_updates' => count($salesToUpdate)
        ]);
    }

    /**
     * Recalculate a single transaction
     */
    private function recalculateTransaction($transaction, $account, &$currentCurrencyBalance, &$currentPkrValue, &$salesToUpdate): void
    {
        // Get transaction amounts
        $transactionCurrencyAmount = (float)($transaction->currency_amount ?? 0.0);
        $transactionPkrAmount = (float)$transaction->amount;

        // Calculate average rate based on account formula
        $avgRateBeforeThisTx = 0.0;
        if ($account->formula === 'd') { // Direct formula: Currency/PKR
            if ($currentPkrValue != 0) {
                $avgRateBeforeThisTx = $currentCurrencyBalance / $currentPkrValue;
            }
        } else { // Inverse formula: PKR/Currency
            if ($currentCurrencyBalance != 0) {
                $avgRateBeforeThisTx = $currentPkrValue / $currentCurrencyBalance;
            }
        }

        $oldAvgRate = (float)($transaction->avg_rate ?? 0.0);
        $newAvgRate = round($avgRateBeforeThisTx, 2);

        $transaction->avg_rate = $newAvgRate;

        // For Purchase entry, make sure the PKR amount is properly calculated
        if ($transaction->payment_type_id == 10 && !$transaction->is_credit && $transactionCurrencyAmount > 0) {
            // This is a purchase debit entry, recalculate PKR amount based on exact exchange rate
            $currencyExchangeRate = (float)$transaction->exchange_rate; // This is the purchase rate
            $formula = $account->formula;

            // Get correct PKR amount based on exact formula and exchange rate
            $correctPkrAmount = $formula === 'd'
                ? round($transactionCurrencyAmount / $currencyExchangeRate, 2)
                : round($transactionCurrencyAmount * $currencyExchangeRate, 2);

            if (abs($correctPkrAmount - $transactionPkrAmount) > 0.01) {
                $transaction->amount = $correctPkrAmount;
                $transactionPkrAmount = $correctPkrAmount; // Update for running balance calculation
            }
        }

        // For Sale entry, update based on the new average rate
        if ($transaction->is_credit &&
            $transaction->payment_type_id == 11 && // Sale
            $transactionCurrencyAmount > 0) {

            // Calculate the new COGS based on the new average rate
            $formula = $account->formula;
            $newCogsPkrValue = $formula === 'd'
                ? round($transactionCurrencyAmount / $newAvgRate, 2)
                : round($transactionCurrencyAmount * $newAvgRate, 2);

            // Store for updating after committing the main transaction
            $salesToUpdate[] = [
                'tid' => $transaction->TID,
                'inv_no' => $transaction->inv_no,
                'new_cogs_pkr' => abs($newCogsPkrValue), // Ensure positive for calculation
                'currency_amount' => $transactionCurrencyAmount,
                'old_avg_rate' => $oldAvgRate,
                'new_avg_rate' => $newAvgRate
            ];

            // Update the PKR amount of the currency credit leg to reflect the new COGS
            $transaction->amount = -abs($newCogsPkrValue); // Ensure negative for credit
            $transactionPkrAmount = -abs($newCogsPkrValue); // Update for running balance calculation
        }

        // Update running balances based on transaction type
        if ($transaction->is_credit) { // Credit transaction
            $currentCurrencyBalance -= $transactionCurrencyAmount;
            $currentPkrValue += $transactionPkrAmount; // PKR amount is negative for credits, so this correctly subtracts value.
        } else { // Debit transaction
            $currentCurrencyBalance += $transactionCurrencyAmount;
            $currentPkrValue += $transactionPkrAmount;
        }

        // Save updated balances with appropriate rounding
        $transaction->running_currency_balance = round($currentCurrencyBalance, 2);
        $transaction->running_pkr_balance = round($currentPkrValue, 2);

        $transaction->save();
    }

    /**
     * Updates the P&L entry associated with a sale.
     *
     * @param string $tid Transaction ID
     * @param string $invNo Invoice number
     * @param float $newCogsPkrValue New cost of goods sold in PKR
     * @return void
     */
    private function updateSalePnlEntry(string $tid, string $invNo, float $newCogsPkrValue): void
    {
        try {
            // Find customer debit leg (selling price in PKR)
            $customerDebitJe = JournalEntry::where('TID', $tid)
                ->where('inv_no', $invNo)
                ->where('payment_type_id', 11) // Sale transaction
                ->where('is_credit', 0)        // Debit entry
                ->whereNull('currency_amount') // PKR transaction (no currency amount)
                ->first();

            if (!$customerDebitJe) {
                Log::warning("Could not find customer debit leg for sale TID: {$tid}, InvNo: {$invNo}");
                return;
            }

            // Get selling price in PKR
            $pkrAtSellingRate = (float)$customerDebitJe->amount;

            // Find currency credit leg to identify P&L entry and update its PKR amount
            $currencyCreditJe = JournalEntry::where('TID', $tid)
                ->where('inv_no', $invNo)
                ->where('payment_type_id', 11)
                ->where('is_credit', 1)
                ->whereNotNull('currency_amount')
                ->first();

            if(!$currencyCreditJe){
                Log::warning("Could not find currency credit leg for sale TID: {$tid}, InvNo: {$invNo}");
                return;
            }

            // Find P&L entry (not the customer debit leg or currency credit leg)
            $pnlJe = JournalEntry::where('TID', $tid)
                ->where('inv_no', $invNo)
                ->where('payment_type_id', 11)
                ->whereNot('id', $customerDebitJe->id)
                ->whereNot('id', $currencyCreditJe->id)
                ->first();

            if ($pnlJe) {
                // Calculate actual profit/loss
                $actualProfitLoss = $pkrAtSellingRate - $newCogsPkrValue;
                $oldPnlAmount = (float)$pnlJe->amount;

                // Update P&L entry with new amount and correct credit/debit flag
                $pnlJe->amount = $actualProfitLoss >= 0 ? -round($actualProfitLoss, 2) : round(abs($actualProfitLoss), 2);
                $pnlJe->is_credit = $actualProfitLoss >= 0 ? 1 : 0; // Profit is Credit to P&L
                $pnlJe->save();

                // Update description to reflect the new margin
                $description = $pnlJe->description;
                if (preg_match('/\| Margin: [\d\.\-]+/', $description, $matches)) {
                    $newDescription = str_replace(
                        $matches[0],
                        "| Margin: " . round($actualProfitLoss, 2),
                        $description
                    );
                    $pnlJe->description = $newDescription;
                    $pnlJe->save();
                }

                Log::debug("Updated P&L amount from {$oldPnlAmount} to {$pnlJe->amount} for TID: {$tid}");
            } else {
                Log::warning("Could not find P&L leg for sale TID: {$tid}, InvNo: {$invNo}");
            }

        } catch (\Exception $e) {
            Log::error("Error updating P&L for sale TID: {$tid}, InvNo: {$invNo}. Error: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Update recalculation status in the logs table
     */
    private function updateRecalculationStatus($status, $errorMessage = null): void
    {
        try {
            DB::table('recalculation_logs')
                ->where('account_id', $this->accountId)
                ->where('from_date', $this->fromDate)
                ->where('trigger_type', 'purchase_update')
                ->where('status', '!=', 'completed') // Don't update already completed ones
                ->update([
                    'status' => $status,
                    'error_message' => $errorMessage,
                    'updated_at' => now()
                ]);
        } catch (\Exception $e) {
            Log::warning("Failed to update recalculation status", [
                'error' => $e->getMessage(),
                'account_id' => $this->accountId
            ]);
        }
    }

    /**
     * Handle job failure
     */
    public function failed(\Throwable $exception): void
    {
        $this->updateRecalculationStatus('failed', $exception->getMessage());
        
        Log::error("Inventory recalculation job permanently failed", [
            'account_id' => $this->accountId,
            'from_date' => $this->fromDate,
            'error' => $exception->getMessage(),
            'job_id' => $this->job ? $this->job->getJobId() : 'unknown'
        ]);
    }

    /**
     * Get tags for Horizon
     */
    public function tags(): array
    {
        return [
            'inventory-recalculation',
            'account:' . $this->accountId,
            'trigger:' . ($this->triggerTransactionId ?? 'unknown')
        ];
    }
}
