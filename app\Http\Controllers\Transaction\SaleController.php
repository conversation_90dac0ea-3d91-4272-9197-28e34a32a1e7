<?php

namespace App\Http\Controllers\Transaction;

use App\Http\Controllers\Controller;
use App\Models\Account;
use App\Models\Currency; // Not directly used in this version but good to have
use App\Models\JournalEntry;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str; // For TID generation if needed
use Illuminate\Support\Facades\Log;
use App\Jobs\ProcessInventoryRecalculationJob;
use App\Models\TransactionAuditLog;
use Carbon\Carbon;

class SaleController extends Controller
{
    // Helper: Generate a unique Transaction ID (TID) for linking journal entries
    private function generateNewTid(int $paymentTypeId): string
    {
        // Using the same approach as JournalEntryController's generateTransactionNumber
        $lastEntry = JournalEntry::lockForUpdate()
            ->latest('TID') // Assuming TID is structured to be sortable like this
            ->first();

        $lastSequence = 0;
        if ($lastEntry) {
            $lastTID = $lastEntry->TID;
            // Extract numeric sequence from last TID, assuming format YYMMSEQ (e.g., 23120001)
            // This needs to be robust. If TID format is different, this logic must change.
            // Current logic: TID = date (ym) + sequence (4 digits)
            if (strlen($lastTID) >= 6) { // ymXXXX = 6 chars
                // Check if the first 2 chars are numeric (year part of date)
                if(is_numeric(substr($lastTID, 0, 2))) {
                    $lastSequence = (int)substr($lastTID, 4); // Get sequence part if TID is like YYMMNNNN
                } else {
                    // Fallback or different logic if TID format is not YYMM based
                    // For now, assume it's just a sequence if not YYMM based
                    $lastSequence = (int)$lastTID; 
                }
            } else {
                 $lastSequence = (int)$lastTID; // If TID is shorter, assume it's just a sequence.
            }
        }

        $newSequence = $lastSequence + 1;
        $datePrefix = now()->format('ym');
        $tid = $datePrefix . str_pad($newSequence, 4, '0', STR_PAD_LEFT);

        // Ensure the TID is unique for the given payment type
        while (JournalEntry::where('TID', $tid)->where('payment_type_id', $paymentTypeId)->exists()) {
            $newSequence++;
            $tid = $datePrefix . str_pad($newSequence, 4, '0', STR_PAD_LEFT);
        }

        return $tid;
    }

    // Helper: Calculate and set running balances for a journal entry
    private function calculateAndSetRunningBalances(JournalEntry $journalEntry): void
    {
        $latestPrevJe = JournalEntry::where('account_id', $journalEntry->account_id)
            ->where(function ($query) use ($journalEntry) {
                $query->where('date', '<', $journalEntry->date)
                      ->orWhere(function ($query) use ($journalEntry) {
                          $query->where('date', '=', $journalEntry->date)
                                ->where('id', '<', $journalEntry->id);
                      });
            })
            ->orderBy('date', 'desc')
            ->orderBy('id', 'desc')
            ->first();

        $prevRunningCurrency = $latestPrevJe ? $latestPrevJe->running_currency_balance : 0; // updated column name
        $prevRunningPkr = $latestPrevJe ? $latestPrevJe->running_pkr_balance : 0;          // updated column name

        $transactionCurrencyAmount = $journalEntry->currency_amount ?? 0; // updated column name
        $transactionPkrAmount = $journalEntry->amount; // This is already signed

        if ($journalEntry->is_credit) { // Outflow from this account
            $journalEntry->running_currency_balance = $prevRunningCurrency - $transactionCurrencyAmount;
        } else { // Inflow to this account
            $journalEntry->running_currency_balance = $prevRunningCurrency + $transactionCurrencyAmount;
        }
        $journalEntry->running_pkr_balance = $prevRunningPkr + $transactionPkrAmount;
    }

    // Helper: Get financial summary for a currency account (Copied from PurchaseController)
    private function getAccountFinancialSummary(Account $account): array
    {
        $rawEntries = JournalEntry::where('account_id', $account->id)
            ->orderBy('date', 'asc')
            ->orderBy('id', 'asc')
            ->get();

        $totalCurrencyBalance = 0;
        $totalPkrBalance = 0;

        foreach ($rawEntries as $entry) {
            $currencyAmountForEntry = $entry->currency_amount ?? 0;
            
            if ($entry->is_credit) { // Outflow from this account
                $totalCurrencyBalance -= $currencyAmountForEntry;
            } else { // Inflow to this account
                $totalCurrencyBalance += $currencyAmountForEntry;
            }
            $totalPkrBalance += $entry->amount; // amount is already signed PKR
        }
        
        $averageRate = 0;
        if ($account->formula === 'd') { // Divide: Currency / PKR
            // For 'd', avg_rate is CURR/PKR. PKR value of 1 unit of CURR = 1 / (CURR/PKR rate)
            // Total CURR / Total PKR value of that CURR
            if ($totalPkrBalance != 0) {
                $averageRate = $totalCurrencyBalance / $totalPkrBalance; 
            }
        } else { // Multiply (default): PKR / Currency
            // For 'm', avg_rate is PKR/CURR.
            // Total PKR value of CURR / Total CURR
            if ($totalCurrencyBalance != 0) {
                $averageRate = $totalPkrBalance / $totalCurrencyBalance;
            }
        }

        return [
            'total_currency_balance' => $totalCurrencyBalance,
            'total_pkr_balance' => $totalPkrBalance,
            'current_average_rate' => round($averageRate, 2), // Changed to 2 decimal places
            'formula_type' => $account->formula,
        ];
    }

    // Helper: Generate Sale Order Number (S-NO)
    private function generateSaleOrderNumber(Account $currencyAccount): string
    {
        $currencyCode = $currencyAccount->currency->code ?? 'GEN'; // Fallback generic code
        $prefix = "S-" . strtoupper($currencyCode);

        // Find the last S-NO for this currency and payment_type_id for Sale (e.g., 11)
        $latestEntry = JournalEntry::where('inv_no', 'LIKE', $prefix . '%')
            ->where('payment_type_id', 11) // Assuming 11 is Sale
            ->orderByRaw('CAST(SUBSTRING(inv_no, LENGTH(?) + 1) AS UNSIGNED) DESC', [$prefix])
            ->first();
            
        $sequence = 1;
        if ($latestEntry) {
            $lastNumPart = str_replace($prefix, '', $latestEntry->inv_no);
            if (is_numeric($lastNumPart)) {
                $sequence = (int)$lastNumPart + 1;
            }
        }
        return $prefix . $sequence;
    }

    // New method for GET /api/sale-entries/get-details
    public function getSaleEntryDetails(Request $request)
    {
        $request->validate(['currency_account_id' => 'required|exists:accounts,id']);
        // currency_account_id is now the ID of the currency account being sold (from frontend's selectCr)
        $currencyAccount = Account::with(['currency', 'accountType'])->find($request->currency_account_id);

        if (!$currencyAccount || !$currencyAccount->currency_id) {
            return response()->json(['error' => 'Selected account is not a valid currency account or currency relationship is missing.'], 422);
        }

        $sNo = $this->generateSaleOrderNumber($currencyAccount);
        $summary = $this->getAccountFinancialSummary($currencyAccount);

        // Attempt to find the associated P&L account
        // Convention: "[Currency Code] P&L Account" or a specific link.
        // For now, let's try to find by name convention.
        // This requires knowing the AccountType ID for "Income" or "Profit & Loss"
        // Assuming 'Income' account type has a specific ID we can query.
        // This part is speculative and might need adjustment based on actual AccountType setup.
        $pnlAccount = null;
        $pnlAccountId = null;
        $pnlAccountName = null;

        if ($currencyAccount->currency) {
            $expectedPnlAccountName = strtoupper($currencyAccount->currency->code) . ' P&L Account';
            // We need to know the account type ID for "Income" or "Revenue"
            // Let's assume for now we search by name and that P&L accounts are of a certain type.
            // This is a placeholder - a more robust way to link currency to P&L account is needed.
            $pnlAccount = Account::where('name', $expectedPnlAccountName)
                                // ->where('account_type_id', ID_OF_INCOME_ACCOUNT_TYPE) // Add this condition
                                ->first();
            if ($pnlAccount) {
                $pnlAccountId = $pnlAccount->id;
                $pnlAccountName = $pnlAccount->name;
            }
        }

        return response()->json([
            'transaction_number' => $sNo, // S-NO
            'currency_id' => $currencyAccount->currency->id,
            'currency_code' => $currencyAccount->currency->code,
            'currency_balance' => $summary['total_currency_balance'],
            'pkr_balance' => $summary['total_pkr_balance'], // This is total PKR value of the currency stock
            'average_rate' => $summary['current_average_rate'], // Average cost rate
            'formula_type' => $currencyAccount->formula,
            'pnl_account_id' => $pnlAccountId, // Suggested P&L Account ID
            'pnl_account_name' => $pnlAccountName, // Suggested P&L Account Name
            // Consider sending a list of all available P&L (Income) accounts if a default isn't found/reliable
        ]);
    }

    // Store a new Sale transaction
    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'date' => 'required|date_format:Y-m-d',
            'selectCr' => 'required|exists:accounts,id', // Currency Account ID (CREDIT)
            'selectDr' => 'required|exists:accounts,id', // Customer Account ID (DEBIT)
            'currency_amount' => 'required|numeric|gt:0',
            'rate' => 'required|numeric|gt:0', // Selling Rate
            'pkr_amount' => 'required|numeric', // PKR amount at selling rate - will be validated
            'avg_rate' => 'required|numeric|gt:0', // Average rate of the currency being sold, now expected as 2dp or validated against 2dp
            'pnl_account_id' => 'required|exists:accounts,id', // P&L Account for profit/loss
            'profit_loss_amount' => 'required|numeric', // The calculated profit or loss amount - will be validated
            'description' => 'nullable|string|max:255',
            'refno' => 'nullable|string|max:100',
            'orderid' => 'required|string|max:100|unique:journal_entries,inv_no,NULL,id,payment_type_id,11', // S-NO, unique for sales
            'is_credit_jama' => 'nullable|in:0,1', // For optional JAMA entry (Future)
        ]);

        $customerAccount = Account::find($validatedData['selectDr']); // Customer being debited
        $currencyAccount = Account::with('currency')->find($validatedData['selectCr']); // Currency being credited (sold)

        if (!$currencyAccount || !$currencyAccount->currency_id) {
            return response()->json(['error' => 'Currency Account (selectCr) must be a valid currency account.'], 422);
        }
        if ($customerAccount && $customerAccount->currency_id) {
            return response()->json(['error' => 'Customer Account (selectDr) must be a PKR account (no currency_id).'], 422);
        }
        $pnlAccount = Account::find($validatedData['pnl_account_id']);
        if (!$pnlAccount) { // TODO: Further validation: check if it's an income/P&L type account
             return response()->json(['error' => 'Invalid P&L account selected.'], 422);
        }

        // Validate PKR amount at selling rate
        $currencyFormula = strtolower($currencyAccount->formula);
        $pkrAtSellingRate = $currencyFormula === 'd'
            ? round($validatedData['currency_amount'] / round($validatedData['rate'], 2), 2)
            : round($validatedData['currency_amount'] * round($validatedData['rate'], 2), 2);

        if (abs($pkrAtSellingRate - round($validatedData['pkr_amount'], 2)) > 0.01) {
            return response()->json(['error' => 'PKR amount at selling rate mismatch. Expected: ' . $pkrAtSellingRate . ', Received: ' . $validatedData['pkr_amount'] . '. Formula: ' . $currencyFormula], 422);
        }

        // Calculate PKR amount at average rate (cost of currency sold)
        $pkrAtAverageRate = $currencyFormula === 'd'
            ? round($validatedData['currency_amount'] / round($validatedData['avg_rate'], 2), 2)
            : round($validatedData['currency_amount'] * round($validatedData['avg_rate'], 2), 2);
            
        // Validate Profit/Loss amount
        $calculatedProfitLoss = $pkrAtSellingRate - $pkrAtAverageRate;
        if (abs($calculatedProfitLoss - round($validatedData['profit_loss_amount'], 2)) > 0.015) { // Increased tolerance slightly for rounding diffs
            return response()->json(['error' => 'Profit/Loss amount mismatch. Expected: ' . round($calculatedProfitLoss,2) . ', Received: ' . $validatedData['profit_loss_amount'] .'. PKR@Sell: '.$pkrAtSellingRate.', PKR@Avg: '.$pkrAtAverageRate], 422);
        }

        DB::beginTransaction();
        try {
            $tid = $this->generateNewTid(11); // Assuming 11 for Sale
            $userId = Auth::id();
            $paymentTypeId = 11; // Sale

            $currencyCode = $currencyAccount->currency->code ?? 'CUR';
            
            $baseNarration = "Sale to {$customerAccount->name} {$currencyCode}" . number_format($validatedData['currency_amount'],2) . "@" . number_format($validatedData['rate'],2);
            $fullNarration = $baseNarration;
            if (!empty($validatedData['refno'])) $fullNarration .= " | Ref: {$validatedData['refno']}";
            if (!empty($validatedData['description'])) $fullNarration .= " | {$validatedData['description']}";

            // 1. Customer Debit Entry
            $customerDebitJe = new JournalEntry([
                'TID' => $tid,
                'date' => $validatedData['date'],
                'payment_type_id' => $paymentTypeId,
                'account_id' => $customerAccount->id, // Correct: Customer Account ID from selectDr
                'is_credit' => 0, // Debit customer
                'description' => $fullNarration,
                'inv_no' => $validatedData['orderid'], // S-NO
                'currency_amount' => null, 
                'amount' => $pkrAtSellingRate, // Positive, total sale amount
                'exchange_rate' => round($validatedData['rate'], 2), // Selling rate stored at 2dp
                'formula_type' => null, 
                'avg_rate' => null, 
                'created_by' => $userId,
            ]);
            $customerDebitJe->save();
            // $this->calculateAndSetRunningBalances($customerDebitJe); // If customer account needs running PKR balance

            // 2. Currency Credit Entry (Inventory Reduction)
            $currencyCreditNarration = "Sale {$currencyCode}" . number_format($validatedData['currency_amount'],2) . "@" . number_format($validatedData['rate'],2) . " (Avg: " . number_format($validatedData['avg_rate'],2) . ")";
            if (!empty($validatedData['refno'])) $currencyCreditNarration .= " | Ref: {$validatedData['refno']}";
            if (!empty($validatedData['description'])) $currencyCreditNarration .= " | {$validatedData['description']}";

            $currencyCreditJe = new JournalEntry([
                'TID' => $tid,
                'date' => $validatedData['date'],
                'payment_type_id' => $paymentTypeId,
                'account_id' => $currencyAccount->id, // Correct: Currency Account ID from selectCr
                'is_credit' => 1, // Credit currency account
                'description' => $currencyCreditNarration,
                'inv_no' => $validatedData['orderid'], // S-NO
                'currency_amount' => $validatedData['currency_amount'], 
                'amount' => -$pkrAtAverageRate, // Negative, cost of currency sold
                'exchange_rate' => round($validatedData['avg_rate'], 2), // Average rate used for this leg stored at 2dp
                'formula_type' => $currencyAccount->formula,
                'avg_rate' => round($validatedData['avg_rate'], 2), // Storing the 2dp avg_rate
                'created_by' => $userId,
            ]);
            $currencyCreditJe->save();
            $this->calculateAndSetRunningBalances($currencyCreditJe); 
            $currencyCreditJe->save();

            // 3. Profit or Loss (P&L) Entry
            if (abs($calculatedProfitLoss) > 0.001) { 
                $pnlNarration = "P/L for {$currencyAccount->name} {$currencyCode}".number_format($validatedData['currency_amount'],2)."@".number_format($validatedData['rate'],2)." | Margin: " . round($calculatedProfitLoss, 2);
                if (!empty($validatedData['description'])) $pnlNarration .= " | {$validatedData['description']}";

                $pnlJe = new JournalEntry([
                    'TID' => $tid,
                    'date' => $validatedData['date'],
                    'payment_type_id' => $paymentTypeId,
                    'account_id' => $validatedData['pnl_account_id'],
                    'is_credit' => $calculatedProfitLoss > 0 ? 1 : 0, // Profit is Credit to P&L, Loss is Debit
                    'amount' => $calculatedProfitLoss > 0 ? -$calculatedProfitLoss : abs($calculatedProfitLoss), // Credit amounts are negative
                    'description' => $pnlNarration,
                    'inv_no' => $validatedData['orderid'], // S-NO
                    'currency_amount' => null,
                    'exchange_rate' => null, 
                    'created_by' => $userId,
                ]);
                $pnlJe->save();
                // $this->calculateAndSetRunningBalances($pnlJe); // If P&L account needs running PKR balance
            }

            // Optional: Exchange Entry (JAMA) - Future as per user
            // if (isset($validatedData['is_credit_jama']) && $validatedData['is_credit_jama'] === "1") {
            //     // ExchangeEntry::create([...]);
            // }

            DB::commit();

            // Log the creation in audit trail
            TransactionAuditLog::logTransactionChange(
                'sale',
                $tid,
                $validatedData['orderid'],
                'created',
                [],
                [
                    'date' => $validatedData['date'],
                    'currency_account_id' => $validatedData['selectCr'],
                    'customer_account_id' => $validatedData['selectDr'],
                    'currency_amount' => $validatedData['currency_amount'],
                    'rate' => $validatedData['rate'],
                    'pkr_amount' => $pkrAtSellingRate,
                    'avg_rate' => $validatedData['avg_rate'],
                    'pnl_account_id' => $validatedData['pnl_account_id'],
                    'profit_loss_amount' => $calculatedProfitLoss,
                    'description' => $validatedData['description'],
                    'refno' => $validatedData['refno']
                ],
                Auth::id(),
                "New sale transaction created"
            );

            return response()->json(['message' => 'Sale transaction recorded successfully.', 'TID' => $tid, 'S_NO' => $validatedData['orderid']], 201);

        } catch (\Exception $e) {
            DB::rollBack();
            // Log::error('Sale Transaction Error: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            return response()->json(['error' => 'Failed to record sale transaction.', 'details' => $e->getMessage()], 500);
        }
    }

    
    // List Sales Transactions
    public function index(Request $request)
    {
        $query = JournalEntry::query()
            ->select(
                'je_customer_debit.id as sale_entry_id',
                'je_customer_debit.inv_no as order_id',
                'je_customer_debit.date as date',
                'je_customer_debit.description as sale_description',
                'currency_account.name as currency_account_name', // This is account being credited
                'currencies.code as currency_code',
                'customer_account.name as customer_account_name', // This is account being debited
                'je_currency_credit.currency_amount as currency_amount_sold',
                'je_customer_debit.exchange_rate as selling_rate',
                DB::raw('ABS(je_customer_debit.amount) as pkr_amount_sold')
            )
            ->from('journal_entries as je_customer_debit') // Customer Debit leg
            ->join('accounts as customer_account', 'je_customer_debit.account_id', '=', 'customer_account.id')
            ->join('journal_entries as je_currency_credit', function ($join) { // Currency Credit leg
                $join->on('je_customer_debit.TID', '=', 'je_currency_credit.TID')
                     ->on('je_customer_debit.inv_no', '=', 'je_currency_credit.inv_no')
                     ->where('je_currency_credit.is_credit', '=', 1)
                     ->where('je_currency_credit.payment_type_id', '=', 11) // Sale
                     ->whereNotNull('je_currency_credit.currency_amount');
            })
            ->join('accounts as currency_account', 'je_currency_credit.account_id', '=', 'currency_account.id')
            ->leftJoin('currencies', 'currency_account.currency_id', '=', 'currencies.id')
            ->where('je_customer_debit.payment_type_id', 11) // Sale
            ->where('je_customer_debit.is_credit', 0); // Customer leg is debit (selectDr)

        // Filters need to consider the new meaning of selectDr/selectCr if passed from frontend for filtering
        // If request has 'customer_account_id', it's je_customer_debit.account_id
        // If request has 'currency_account_id', it's je_currency_credit.account_id

        if ($request->filled('date_from')) {
            $query->where('je_customer_debit.date', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->where('je_customer_debit.date', '<=', $request->date_to);
        }
        if ($request->filled('customer_account_id')) {
            $query->where('je_customer_debit.account_id', $request->customer_account_id);
        }
        if ($request->filled('currency_account_id')) {
            $query->where('je_currency_credit.account_id', $request->currency_account_id);
        }

        $query->groupBy(
            'je_customer_debit.id',
            'je_customer_debit.inv_no',
            'je_customer_debit.date',
            'je_customer_debit.description',
            'currency_account.name',
            'currencies.code',
            'customer_account.name',
            'je_currency_credit.currency_amount',
            'je_customer_debit.exchange_rate',
            'je_customer_debit.amount'
        );
        
        $sortBy = $request->input('sort_by', 'date');
        $sortDirection = $request->input('sort_direction', 'desc');
        $validSortColumns = [
            'date' => 'je_customer_debit.date',
            'order_id' => 'je_customer_debit.inv_no',
            'currency_amount_sold' => 'je_currency_credit.currency_amount',
            'selling_rate' => 'je_customer_debit.exchange_rate',
            'pkr_amount_sold' => DB::raw('ABS(je_customer_debit.amount)'),
        ];
        if (array_key_exists($sortBy, $validSortColumns)) {
            $query->orderBy($validSortColumns[$sortBy], $sortDirection);
        } else {
            $query->orderBy('je_customer_debit.date', 'desc');
        }
        
        $perPage = $request->input('per_page', 15);
        $sales = $query->paginate($perPage);

        $sales->getCollection()->transform(function ($sale) {
            $refNo = '';
            if ($sale->sale_description) {
                $parts = explode(' | ', $sale->sale_description);
                foreach ($parts as $part) {
                    if (Str::startsWith($part, 'Ref: ')) {
                        $refNo = Str::after($part, 'Ref: ');
                        break;
                    }
                }
            }
            return [
                'id' => $sale->order_id,
                'date' => \Carbon\Carbon::parse($sale->date)->format('Y-m-d'),
                'ref_no' => $refNo,
                'order_id' => $sale->order_id,
                'customer_account_name' => $sale->customer_account_name,
                'currency_account_name' => $sale->currency_account_name,
                'currency_code' => $sale->currency_code,
                'currency_amount' => (float)$sale->currency_amount_sold,
                'rate' => (float)$sale->selling_rate,
                'pkr_amount' => (float)$sale->pkr_amount_sold,
            ];
        });
        return response()->json($sales);
    }

    // Show Sale Entry for editing. $id is S-NO (inv_no)
    public function show($id)
    {
        $journalEntries = JournalEntry::with(['account', 'account.currency', 'createdBy'])
            ->where('inv_no', $id)
            ->where('payment_type_id', 11) // Sale
            ->orderBy('id', 'asc') 
            ->get();

        if ($journalEntries->count() < 2) { 
            return response()->json(['error' => 'Sale transaction not found or incomplete. Expected at least 2 entries.'], 404);
        }

        $customerDebitJe = null;
        $currencyCreditJe = null;
        $pnlJe = null;

        // Identify the journal entry legs based on new understanding
        // Customer Debit: is_credit=0, account is PKR (no currency_id)
        // Currency Credit: is_credit=1, account is Currency (currency_id is not null)
        foreach($journalEntries as $entry) {
            if ($entry->is_credit == 0 && $entry->account && $entry->account->currency_id === null) {
                 $customerDebitJe = $entry; // This is the Customer Account (selectDr)
            } elseif ($entry->is_credit == 1 && $entry->account && $entry->account->currency_id !== null) {
                $currencyCreditJe = $entry; // This is the Currency Account (selectCr)
            }
        }
        // PNL entry is the remaining one, if it exists and is not one of the above
        if ($customerDebitJe && $currencyCreditJe) {
             $pnlJe = $journalEntries->firstWhere(function ($entry) use ($customerDebitJe, $currencyCreditJe) {
                return $entry->id != $customerDebitJe->id && $entry->id != $currencyCreditJe->id;
            });
        }

        if (!$customerDebitJe || !$currencyCreditJe) {
            return response()->json(['error' => 'Core sale transaction entries (Customer Debit or Currency Credit) not found.'], 404);
        }
        
        $originalDesc = '';
        $refNoFromDesc = '';
        // Use currency code from the actual currency credit entry for robust pattern matching
        $currencyCodeForPattern = $currencyCreditJe->account->currency ? $currencyCreditJe->account->currency->code : '[A-Z]{3}';
        $baseNarrationPattern = "/Sale to .*? {$currencyCodeForPattern}[\d\.,]+@[\d\.,]+/i";

        if ($customerDebitJe->description) {
            $parts = explode(' | ', $customerDebitJe->description);
            $firstPartProcessed = false;
            foreach ($parts as $part) {
                if (!$firstPartProcessed && preg_match($baseNarrationPattern, $part)) {
                    $firstPartProcessed = true;
                    continue; 
                }
                if (Str::startsWith($part, 'Ref: ')) {
                    $refNoFromDesc = Str::after($part, 'Ref: ');
                } else {
                    if (!empty($originalDesc)) $originalDesc .= " | ";
                    $originalDesc .= $part;
                }
            }
        }

        $profitLossAmount = 0;
        if ($pnlJe) {
            // amount is stored negative for credit (profit), positive for debit (loss)
            $profitLossAmount = $pnlJe->is_credit ? -$pnlJe->amount : $pnlJe->amount;
        }

        return response()->json([
            'date' => $customerDebitJe->date->format('Y-m-d'),
            'selectCr' => $currencyCreditJe->account_id, // Currency Account ID (Credit)
            'selectDr' => $customerDebitJe->account_id,  // Customer Account ID (Debit)
            'currency_amount' => (float)$currencyCreditJe->currency_amount,
            'rate' => round((float)$customerDebitJe->exchange_rate, 2), // Selling rate 2dp
            'pkr_amount' => (float)abs($customerDebitJe->amount), 
            'avg_rate' => round((float)$currencyCreditJe->exchange_rate, 2), // Historical avg_rate from tx 2dp
            'pnl_account_id' => $pnlJe ? $pnlJe->account_id : null,
            'profit_loss_amount' => round($profitLossAmount, 2), 
            'description' => $originalDesc,
            'refno' => $refNoFromDesc,
            'orderid' => $customerDebitJe->inv_no, 
            'is_credit_jama' => null, // Placeholder for future JAMA logic
        ]);
    }

    // Update Sale Transaction. $id is S-NO (inv_no)
    public function update(Request $request, $id)
    {
        $validatedData = $request->validate([
            'date' => 'required|date_format:Y-m-d',
            'selectCr' => 'required|exists:accounts,id', // Currency Account ID (CREDIT)
            'selectDr' => 'required|exists:accounts,id', // Customer Account ID (DEBIT)
            'currency_amount' => 'required|numeric|gt:0',
            'rate' => 'required|numeric|gt:0', 
            'pkr_amount' => 'required|numeric',
            'avg_rate' => 'required|numeric|gt:0',
            'pnl_account_id' => 'required|exists:accounts,id',
            'profit_loss_amount' => 'required|numeric',
            'description' => 'nullable|string|max:255',
            'refno' => 'nullable|string|max:100',
            // 'is_credit_jama' => 'nullable|in:0,1', 
        ]);

        $newCurrencyAccount = Account::with('currency')->find($validatedData['selectCr']); // Currency being credited (sold)
        $newCustomerAccount = Account::find($validatedData['selectDr']); // Customer being debited
        $newPnlAccount = Account::find($validatedData['pnl_account_id']);

        if (!$newCurrencyAccount || !$newCurrencyAccount->currency_id) {
            return response()->json(['error' => 'Currency Account (selectCr) must be a valid currency account.'], 422);
        }
        if ($newCustomerAccount && $newCustomerAccount->currency_id) {
            return response()->json(['error' => 'Customer Account (selectDr) must be a PKR account.'], 422);
        }
        if (!$newPnlAccount) { // Add further P&L type validation if needed
             return response()->json(['error' => 'Invalid P&L account.'], 422);
        }

        $currencyFormula = strtolower($newCurrencyAccount->formula);
        $pkrAtSellingRate = $currencyFormula === 'd'
            ? round($validatedData['currency_amount'] / round($validatedData['rate'], 2), 2)
            : round($validatedData['currency_amount'] * round($validatedData['rate'], 2), 2);

        if (abs($pkrAtSellingRate - round($validatedData['pkr_amount'],2)) > 0.01) {
            return response()->json(['error' => 'PKR amount (selling) mismatch on update.'], 422);
        }
        
        $pkrAtAverageRate = $currencyFormula === 'd'
            ? round($validatedData['currency_amount'] / round($validatedData['avg_rate'], 2), 2)
            : round($validatedData['currency_amount'] * round($validatedData['avg_rate'], 2), 2);

        $calculatedProfitLoss = $pkrAtSellingRate - $pkrAtAverageRate;
        if (abs($calculatedProfitLoss - round($validatedData['profit_loss_amount'],2)) > 0.015) { // Slight tolerance
            return response()->json(['error' => 'Profit/Loss amount mismatch on update. Expected: '.round($calculatedProfitLoss,2).', Received: '.$validatedData['profit_loss_amount']], 422);
        }

        DB::beginTransaction();
        try {
            $journalEntries = JournalEntry::where('inv_no', $id) 
                                        ->where('payment_type_id', 11) // Sale
                                        ->with('account') // Eager load account for identification
                                        ->get();

            if ($journalEntries->count() < 2) { 
                DB::rollBack();
                return response()->json(['error' => 'Sale transaction not found or incomplete for update (less than 2 entries).'], 404);
            }

            $customerDebitJe = null;
            $currencyCreditJe = null;
            $pnlJe = null;
            $oldPnlAccountId = null;

            foreach($journalEntries as $entry) {
                if ($entry->is_credit == 0 && $entry->account && $entry->account->currency_id === null) {
                    $customerDebitJe = $entry;
                } elseif ($entry->is_credit == 1 && $entry->account && $entry->account->currency_id !== null) {
                    $currencyCreditJe = $entry;
                }
            }
            if ($customerDebitJe && $currencyCreditJe) {
                $pnlJe = $journalEntries->firstWhere(function ($entry) use ($customerDebitJe, $currencyCreditJe, &$oldPnlAccountId) {
                    if ($entry->id != $customerDebitJe->id && $entry->id != $currencyCreditJe->id) {
                        $oldPnlAccountId = $entry->account_id;
                        return true;
                    }
                    return false;
                });
            }

            if (!$customerDebitJe || !$currencyCreditJe) {
                DB::rollBack();
                return response()->json(['error' => 'Could not identify core transaction legs (Customer/Currency) for update.'], 404);
            }
            
            // Save original values for recalculation decision
            $originalDate = $currencyCreditJe->date->format('Y-m-d');
            $originalCurrencyAccountId = $currencyCreditJe->account_id;
            $originalCurrencyAmount = $currencyCreditJe->currency_amount;
            $originalAvgRate = $currencyCreditJe->avg_rate;
            
            $tid = $customerDebitJe->TID; 
            $userId = Auth::id(); 
            
            $currencyCode = $newCurrencyAccount->currency->code ?? 'CUR';
            $baseNarration = "Sale to {$newCustomerAccount->name} {$currencyCode}" . number_format($validatedData['currency_amount'],2) . "@" . number_format($validatedData['rate'],2);
            $fullNarration = $baseNarration;
            if (!empty($validatedData['refno'])) $fullNarration .= " | Ref: {$validatedData['refno']}";
            if (!empty($validatedData['description'])) $fullNarration .= " | {$validatedData['description']}";

            // Update Customer Debit JE
            $customerDebitJe->fill([
                'date' => $validatedData['date'],
                'account_id' => $newCustomerAccount->id, // Correct: Customer Account ID from selectDr
                'description' => $fullNarration,
                'amount' => $pkrAtSellingRate,
                'exchange_rate' => round($validatedData['rate'], 2), // Selling rate 2dp
                'updated_by' => $userId,
            ]);
            $customerDebitJe->save();

            // Update Currency Credit JE
            $currencyCreditNarration = "Sale {$currencyCode}" . number_format($validatedData['currency_amount'],2) . "@" . number_format($validatedData['rate'],2) . " (Avg: " . number_format($validatedData['avg_rate'],2) . ")";
            if (!empty($validatedData['refno'])) $currencyCreditNarration .= " | Ref: {$validatedData['refno']}";
            if (!empty($validatedData['description'])) $currencyCreditNarration .= " | {$validatedData['description']}";
            
            $currencyCreditJe->fill([
                'date' => $validatedData['date'],
                'account_id' => $newCurrencyAccount->id, // Correct: Currency Account ID from selectCr
                'description' => $currencyCreditNarration,
                'currency_amount' => $validatedData['currency_amount'],
                'amount' => -$pkrAtAverageRate,
                'exchange_rate' => round($validatedData['avg_rate'], 2), // Avg rate 2dp
                'formula_type' => $newCurrencyAccount->formula,
                'avg_rate' => round($validatedData['avg_rate'], 2), // Avg rate 2dp
                'updated_by' => $userId,
            ]);
            // Don't recalculate running balances here; it will be done in the full recalculation
            $currencyCreditJe->save();

            // Handle P&L JE: Update, Create, or Delete
            $pnlNarration = "P/L for {$newCurrencyAccount->name} {$currencyCode}".number_format($validatedData['currency_amount'],2)."@".number_format($validatedData['rate'],2)." | Margin: " . round($calculatedProfitLoss, 2);
            if (!empty($validatedData['description'])) $pnlNarration .= " | {$validatedData['description']}";

            if (abs($calculatedProfitLoss) > 0.001) { 
                if ($pnlJe) { 
                    $pnlJe->fill([
                        'date' => $validatedData['date'],
                        'account_id' => $newPnlAccount->id,
                        'is_credit' => $calculatedProfitLoss > 0 ? 1 : 0,
                        'amount' => $calculatedProfitLoss > 0 ? -$calculatedProfitLoss : abs($calculatedProfitLoss),
                        'description' => $pnlNarration,
                        'updated_by' => $userId,
                    ]);
                    $pnlJe->save();
                } else { 
                    $pnlJe = new JournalEntry([
                        'TID' => $tid,
                        'date' => $validatedData['date'],
                        'payment_type_id' => 11, // Sale
                        'account_id' => $newPnlAccount->id,
                        'is_credit' => $calculatedProfitLoss > 0 ? 1 : 0,
                        'amount' => $calculatedProfitLoss > 0 ? -$calculatedProfitLoss : abs($calculatedProfitLoss),
                        'description' => $pnlNarration,
                        'inv_no' => $id, // S-NO
                        'created_by' => $userId, // Or Auth::id() if original creator is not relevant on new PNL
                        'updated_by' => $userId,
                    ]);
                    $pnlJe->save();
                }
            } else { 
                if ($pnlJe) { 
                    $pnlJe->delete();
                }
            }
            
            // TODO: Robust recalculation of ALL SUBSEQUENT journal entries for all affected accounts (old/new).
            // This is critical if accounts, dates, or significant amounts changed.

            // Commit the basic update before recalculation
            DB::commit();
            
            // Determine if significant changes occurred that require recalculation
            $newDate = $validatedData['date'];
            $newCurrencyAccountId = (int)$validatedData['selectCr'];
            $newCurrencyAmount = (float)$validatedData['currency_amount'];
            $newAvgRate = (float)$validatedData['avg_rate'];

            $significantChange = $originalDate !== $newDate ||
                                $originalCurrencyAccountId !== $newCurrencyAccountId ||
                                abs((float)$originalCurrencyAmount - $newCurrencyAmount) > 0.001 ||
                                abs((float)$originalAvgRate - $newAvgRate) > 0.001;

            // Log the update in audit trail
            TransactionAuditLog::logTransactionChange(
                'sale',
                $tid,
                $id,
                'updated',
                [
                    'date' => $originalDate,
                    'currency_account_id' => $originalCurrencyAccountId,
                    'currency_amount' => $originalCurrencyAmount,
                    'avg_rate' => $originalAvgRate
                ],
                [
                    'date' => $validatedData['date'],
                    'currency_account_id' => $validatedData['selectCr'],
                    'customer_account_id' => $validatedData['selectDr'],
                    'currency_amount' => $validatedData['currency_amount'],
                    'rate' => $validatedData['rate'],
                    'pkr_amount' => $pkrAtSellingRate,
                    'avg_rate' => $validatedData['avg_rate'],
                    'pnl_account_id' => $validatedData['pnl_account_id'],
                    'profit_loss_amount' => $calculatedProfitLoss,
                    'description' => $validatedData['description'],
                    'refno' => $validatedData['refno']
                ],
                Auth::id(),
                $significantChange ? "Sale updated with significant changes requiring recalculation" : "Sale updated with minor changes"
            );

            // If there was a significant change, dispatch background recalculation
            if ($significantChange) {
                // Use the earliest date to ensure all affected transactions are updated
                $earliestAffectedDate = min($originalDate, $newDate);

                // Dispatch job for new currency account
                ProcessInventoryRecalculationJob::dispatch($newCurrencyAccountId, $earliestAffectedDate, $id)
                    ->onQueue('inventory')
                    ->delay(now()->addSeconds(2));

                // If currency account changed, dispatch job for old account too
                if ($originalCurrencyAccountId !== $newCurrencyAccountId) {
                    ProcessInventoryRecalculationJob::dispatch($originalCurrencyAccountId, $earliestAffectedDate, $id)
                        ->onQueue('inventory')
                        ->delay(now()->addSeconds(5));
                }

                Log::info("Dispatched background recalculation jobs for Sale update S-NO: {$id}");
            }

            return response()->json(['message' => 'Sale transaction updated. Balances are being recalculated.']);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['error' => 'Failed to update sale transaction.', 'details' => $e->getMessage(). ' Line: ' . $e->getLine() . ' File: ' . $e->getFile() ], 500);
        }
    }

    // Delete Sale Transaction. $id is S-NO (inv_no)
    public function destroy($id)
    {
        DB::beginTransaction();
        try {
            $journalEntries = JournalEntry::where('inv_no', $id) 
                                        ->where('payment_type_id', 11) // Sale
                                        ->get();

            if ($journalEntries->isEmpty()) {
                DB::rollBack();
                return response()->json(['error' => 'Sale transaction not found.'], 404);
            }

            // Collect information needed for recalculation
            $affectedAccounts = [];
            $earliestDate = null;
            
            foreach ($journalEntries as $entry) {
                // Only track currency accounts for recalculation
                if ($entry->currency_amount !== null) {
                    $accountId = $entry->account_id;
                    $date = $entry->date->format('Y-m-d');
                    
                    $affectedAccounts[$accountId] = $date;
                    
                    // Track the earliest date for recalculation
                    if ($earliestDate === null || $date < $earliestDate) {
                        $earliestDate = $date;
                    }
                }
                
                // Delete the entry
                $entry->delete();
            }
            
            DB::commit();
            
            // TODO: Implement robust recalculation for subsequent journal entries' running balances.
            // This would involve identifying all affected accounts and dates.
            
            // Log the deletion in audit trail
            TransactionAuditLog::logTransactionChange(
                'sale',
                $journalEntries->first()->TID,
                $id,
                'deleted',
                [], // No old values needed for deletion
                [], // No new values for deletion
                Auth::id(),
                "Sale transaction deleted"
            );

            // Dispatch background recalculation jobs for all affected accounts
            foreach ($affectedAccounts as $accountId => $date) {
                ProcessInventoryRecalculationJob::dispatch($accountId, $date, $id)
                    ->onQueue('inventory')
                    ->delay(now()->addSeconds(2));
                Log::info("Dispatched background recalculation job for Account ID: {$accountId} from Date: {$date} after deleting S-NO: {$id}");
            }

            return response()->json(['message' => 'Sale transaction deleted. Balances are being recalculated.']);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['error' => 'Failed to delete sale transaction.', 'details' => $e->getMessage()], 500);
        }
    }
    
    /**
     * DEPRECATED: This method has been replaced with asynchronous ProcessInventoryRecalculationJob
     * Keeping for reference but should not be used in production
     *
     * @deprecated Use ProcessInventoryRecalculationJob instead
     */
    private function recalculateAllSubsequentTransactions_DEPRECATED(int $accountId, string $startDate): void
    {
        try {
            // Load the account
            $account = Account::with('currency')->findOrFail($accountId);
            if (!$account->currency_id) {
                Log::warning("Account ID {$accountId} is not a currency account. Skipping recalculation.");
                return;
            }

            // Get all transactions ordered by date and ID
            $transactions = JournalEntry::where('account_id', $accountId)
                ->where('date', '>=', $startDate)
                ->orderBy('date', 'asc')
                ->orderBy('id', 'asc')
                ->get();

            if ($transactions->isEmpty()) {
                Log::info("No transactions found for Account ID: {$accountId} on or after {$startDate}. No recalculation needed.");
                return;
            }

            // Find the last transaction before our batch to get starting balances
            $firstTransactionInBatch = $transactions->first();
            $latestTxBeforeBatch = JournalEntry::where('account_id', $accountId)
                ->where(function ($query) use ($firstTransactionInBatch) {
                    $query->where('date', '<', $firstTransactionInBatch->date)
                          ->orWhere(function ($query) use ($firstTransactionInBatch) {
                              $query->where('date', '=', $firstTransactionInBatch->date)
                                    ->where('id', '<', $firstTransactionInBatch->id);
                          });
                })
                ->orderBy('date', 'desc')
                ->orderBy('id', 'desc')
                ->first();

            // Initialize running balances from last transaction or zero
            $currentCurrencyBalance = $latestTxBeforeBatch ? (float)$latestTxBeforeBatch->running_currency_balance : 0.0;
            $currentPkrValue = $latestTxBeforeBatch ? (float)$latestTxBeforeBatch->running_pkr_balance : 0.0;

            Log::debug("Initial state before recalculation - Currency Balance: {$currentCurrencyBalance}, PKR Value: {$currentPkrValue}");

            // Collect sales transactions that need P&L updates
            $salesToUpdate = [];

            // Process each transaction
            DB::beginTransaction();
            foreach ($transactions as $transaction) {
                // Get transaction amounts
                $transactionCurrencyAmount = (float)($transaction->currency_amount ?? 0.0);
                $transactionPkrAmount = (float)$transaction->amount;

                Log::debug("Processing transaction ID: {$transaction->id}, Type: " . 
                        ($transaction->is_credit ? 'Credit' : 'Debit') . 
                        ", Currency Amount: " . ($transactionCurrencyAmount ?: 'NULL') . 
                        ", PKR Amount: {$transactionPkrAmount}");

                // Calculate average rate based on account formula
                $avgRateBeforeThisTx = 0.0;
                if ($account->formula === 'd') { // Direct formula: Currency/PKR
                    if ($currentPkrValue != 0) {
                        $avgRateBeforeThisTx = $currentCurrencyBalance / $currentPkrValue;
                    }
                } else { // Inverse formula: PKR/Currency
                    if ($currentCurrencyBalance != 0) {
                        $avgRateBeforeThisTx = $currentPkrValue / $currentCurrencyBalance;
                    }
                }
                
                $oldAvgRate = (float)($transaction->avg_rate ?? 0.0);
                $newAvgRate = round($avgRateBeforeThisTx, 2);
                
                Log::debug("Calculated AvgRate: {$newAvgRate}, Old value: {$oldAvgRate}");
                
                $transaction->avg_rate = $newAvgRate;

                // For Purchase entry, make sure the PKR amount is properly calculated
                if ($transaction->payment_type_id == 10 && !$transaction->is_credit && $transactionCurrencyAmount > 0) {
                    // This is a purchase debit entry, recalculate PKR amount based on exact exchange rate
                    $currencyExchangeRate = (float)$transaction->exchange_rate; // This is the purchase rate
                    $formula = $account->formula;
                    
                    // Get correct PKR amount based on exact formula and exchange rate
                    $correctPkrAmount = $formula === 'd' 
                        ? round($transactionCurrencyAmount / $currencyExchangeRate, 2)
                        : round($transactionCurrencyAmount * $currencyExchangeRate, 2);
                    
                    if (abs($correctPkrAmount - $transactionPkrAmount) > 0.01) {
                        Log::debug("Updating purchase PKR amount - Old: {$transactionPkrAmount}, New: {$correctPkrAmount}");
                        $transaction->amount = $correctPkrAmount;
                        $transactionPkrAmount = $correctPkrAmount; // Update for running balance calculation
                    }
                }

                // For Sale entry, update based on the new average rate
                if ($transaction->is_credit && 
                    $transaction->payment_type_id == 11 && // Sale
                    $transactionCurrencyAmount > 0) {
                    
                    // Calculate the new COGS based on the new average rate
                    $formula = $account->formula;
                    $newCogsPkrValue = $formula === 'd' 
                        ? round($transactionCurrencyAmount / $newAvgRate, 2)
                        : round($transactionCurrencyAmount * $newAvgRate, 2);
                    
                    Log::debug("Updating sale COGS - Old: {$transactionPkrAmount}, New: -" . abs($newCogsPkrValue));
                    
                    // Store for updating after committing the main transaction
                    $salesToUpdate[] = [
                        'tid' => $transaction->TID,
                        'inv_no' => $transaction->inv_no,
                        'new_cogs_pkr' => abs($newCogsPkrValue), // Ensure positive for calculation
                        'currency_amount' => $transactionCurrencyAmount,
                        'old_avg_rate' => $oldAvgRate,
                        'new_avg_rate' => $newAvgRate
                    ];
                    
                    // Update the PKR amount of the currency credit leg to reflect the new COGS
                    $oldAmount = $transaction->amount;
                    $transaction->amount = -abs($newCogsPkrValue); // Ensure negative for credit
                    $transactionPkrAmount = -abs($newCogsPkrValue); // Update for running balance calculation
                    Log::debug("Updated sale amount from {$oldAmount} to {$transaction->amount}");
                }

                // Update running balances based on transaction type
                if ($transaction->is_credit) { // Credit transaction
                    $currentCurrencyBalance -= $transactionCurrencyAmount;
                    $currentPkrValue += $transactionPkrAmount; // PKR amount is negative for credits, so this correctly subtracts value.
                } else { // Debit transaction
                    $currentCurrencyBalance += $transactionCurrencyAmount;
                    $currentPkrValue += $transactionPkrAmount;
                }

                // Save updated balances with appropriate rounding
                $transaction->running_currency_balance = round($currentCurrencyBalance, 2);
                $transaction->running_pkr_balance = round($currentPkrValue, 2);

                $transaction->save();
                Log::debug("Updated Tx ID: {$transaction->id}. AvgRate: {$transaction->avg_rate}, RunCurrBal: {$transaction->running_currency_balance}, RunPkrBal: {$transaction->running_pkr_balance}");
            }
            DB::commit();

            // Now update all the P&L entries for affected sales
            if (count($salesToUpdate) > 0) {
                Log::info("Updating P&L entries for " . count($salesToUpdate) . " sales affected by rate changes");
                foreach ($salesToUpdate as $saleUpdate) {
                    try {
                        $this->updateSalePnlEntry(
                            $saleUpdate['tid'], 
                            $saleUpdate['inv_no'], 
                            $saleUpdate['new_cogs_pkr']
                        );
                        Log::info("Updated P&L for sale TID: {$saleUpdate['tid']}, InvNo: {$saleUpdate['inv_no']}, New COGS: {$saleUpdate['new_cogs_pkr']}");
                    } catch (\Exception $e) {
                        Log::error("Failed to update P&L for sale TID: {$saleUpdate['tid']}, InvNo: {$saleUpdate['inv_no']}. Error: " . $e->getMessage());
                    }
                }
            }
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Error during manual recalculation for Account ID: {$accountId}, Date: {$startDate}. Error: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Updates the P&L entry associated with a sale.
     * 
     * @param string $tid Transaction ID
     * @param string $invNo Invoice number
     * @param float $newCogsPkrValue New cost of goods sold in PKR
     * @return void
     */
    private function updateSalePnlEntry(string $tid, string $invNo, float $newCogsPkrValue): void
    {
        DB::beginTransaction();
        try {
            // Find customer debit leg (selling price in PKR)
            $customerDebitJe = JournalEntry::where('TID', $tid)
                ->where('inv_no', $invNo)
                ->where('payment_type_id', 11) // Sale transaction
                ->where('is_credit', 0)        // Debit entry
                ->whereNull('currency_amount') // PKR transaction (no currency amount)
                ->first();

            if (!$customerDebitJe) {
                Log::warning("Could not find customer debit leg for sale TID: {$tid}, InvNo: {$invNo}");
                DB::rollBack();
                return;
            }

            // Get selling price in PKR
            $pkrAtSellingRate = (float)$customerDebitJe->amount;

            // Find currency credit leg to identify P&L entry and update its PKR amount
            $currencyCreditJe = JournalEntry::where('TID', $tid)
                ->where('inv_no', $invNo)
                ->where('payment_type_id', 11)
                ->where('is_credit', 1)
                ->whereNotNull('currency_amount')
                ->first();
                
            if(!$currencyCreditJe){
                Log::warning("Could not find currency credit leg for sale TID: {$tid}, InvNo: {$invNo}");
                DB::rollBack();
                return;
            }

            // Find P&L entry (not the customer debit leg or currency credit leg)
            $pnlJe = JournalEntry::where('TID', $tid)
                ->where('inv_no', $invNo)
                ->where('payment_type_id', 11)
                ->whereNot('id', $customerDebitJe->id)
                ->whereNot('id', $currencyCreditJe->id)
                ->first();

            if ($pnlJe) {
                // Calculate actual profit/loss
                $actualProfitLoss = $pkrAtSellingRate - $newCogsPkrValue;
                $oldPnlAmount = (float)$pnlJe->amount;

                // Update P&L entry with new amount and correct credit/debit flag
                $pnlJe->amount = $actualProfitLoss >= 0 ? -round($actualProfitLoss, 2) : round(abs($actualProfitLoss), 2);
                $pnlJe->is_credit = $actualProfitLoss >= 0 ? 1 : 0; // Profit is Credit to P&L
                $pnlJe->save();
                
                // Update description to reflect the new margin
                $description = $pnlJe->description;
                if (preg_match('/\| Margin: [\d\.\-]+/', $description, $matches)) {
                    $newDescription = str_replace(
                        $matches[0], 
                        "| Margin: " . round($actualProfitLoss, 2),
                        $description
                    );
                    $pnlJe->description = $newDescription;
                    $pnlJe->save();
                }
                
                Log::debug("Updated P&L amount from {$oldPnlAmount} to {$pnlJe->amount}");
            } else {
                Log::warning("Could not find P&L leg for sale TID: {$tid}, InvNo: {$invNo}");
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Error updating P&L for sale TID: {$tid}, InvNo: {$invNo}. Error: " . $e->getMessage());
            throw $e;
        }
    }

}
