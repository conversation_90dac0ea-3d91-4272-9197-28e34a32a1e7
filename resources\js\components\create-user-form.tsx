import { FormEventHandler } from 'react';
import InputError from '@/components/input-error';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ComboBox, ComboBoxOption } from '@/components/ui/combobox';
import { type FormDataConvertible } from '@inertiajs/core';

const roles = ['Admin', 'Editor', 'Viewer'];

export interface CreateUserFormData {
    name: string;
    email: string;
    username: string;
    password: string;
    password_confirmation: string;
    role: string;
    [key: string]: FormDataConvertible;
}

export interface CreateUserFormProps {
    data: CreateUserFormData;
    setData: (key: keyof CreateUserFormData | Partial<CreateUserFormData>, value?: FormDataConvertible) => void;
    errors: Partial<Record<keyof CreateUserFormData, string>>;
    processing: boolean;
    onSubmit: FormEventHandler<HTMLFormElement>;
}

export default function CreateUserForm({ 
    data, 
    setData, 
    errors, 
    processing, 
    onSubmit 
}: CreateUserFormProps) {
    const roleOptions: ComboBoxOption[] = roles.map(r => ({ value: r, label: r }));

    return (
        <form className="space-y-6" onSubmit={onSubmit}>
            {/* Row 1: Name and Username */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="grid gap-2">
                    <Label htmlFor="name">Name</Label>
                    <Input id="name" value={data.name} onChange={e => setData('name', e.target.value)} required />
                    <InputError message={errors.name} />
                </div>
                <div className="grid gap-2">
                    <Label htmlFor="username">Username</Label>
                    <Input id="username" value={data.username} onChange={e => setData('username', e.target.value)} required />
                    <InputError message={errors.username} />
                </div>
            </div>
            {/* Row 2: Email and Role */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="grid gap-2">
                    <Label htmlFor="email">Email</Label>
                    <Input id="email" type="email" value={data.email} onChange={e => setData('email', e.target.value)} required />
                    <InputError message={errors.email} />
                </div>
                <div className="grid gap-2">
                    <Label htmlFor="role">Role</Label>
                    <ComboBox
                        options={roleOptions}
                        value={data.role}
                        onChange={(val: string) => setData('role', val)}
                        placeholder="Select role..."
                        className="min-w-[200px]"
                    />
                    <InputError message={errors.role} />
                </div>
            </div>
            {/* Row 3: Password */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="grid gap-2">
                    <Label htmlFor="password">Password</Label>
                    <Input id="password" type="password" value={data.password} onChange={e => setData('password', e.target.value)} required />
                    <InputError message={errors.password} />
                </div>
                <div className="grid gap-2">
                    <Label htmlFor="password_confirmation">Confirm Password</Label>
                    <Input id="password_confirmation" type="password" value={data.password_confirmation} onChange={e => setData('password_confirmation', e.target.value)} required />
                    <InputError message={errors.password_confirmation} />
                </div>
            </div>
            <Button type="submit" disabled={processing} className="w-full">Create User</Button>
        </form>
    );
}
