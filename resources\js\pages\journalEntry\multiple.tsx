import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ComboBox } from '@/components/ui/combobox';
import { DatePicker } from '@/components/ui/date-picker';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AppLayout from '@/layouts/app-layout';
import { BreadcrumbItem } from '@/types';
import axios from 'axios';
import { Info, Loader2 } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { NumericFormat } from 'react-number-format';
import { toast } from 'sonner';
import { v4 as uuidv4 } from 'uuid';
import { Head } from '@inertiajs/react';

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Journal Multiple Entry', href: '/journal-multiple-entry' },
];

interface Account {
    id: number;
    name: string;
    description?: string;
    account_type?: { // Add account_type to the interface
        id: number;
        name: string;
    };
}

interface PaymentType {
    id: number;
    name: string;
    label: string;
}

interface ComboBoxOption {
    value: string;
    label: string;
}

interface JournalEntry {
    id: string;
    creditAccount: string;
    debitAccount: string;
    description: string;
    chqNo: string;
    amount: string;
}

export default function MultipleJournalEntry() {
    const [date, setDate] = useState<Date | undefined>(new Date());
    const [loading, setLoading] = useState(false);
    const [paymentType, setPaymentType] = useState('');
    const [entries, setEntries] = useState<JournalEntry[]>([
        {
            id: uuidv4(),
            creditAccount: '',
            debitAccount: '',
            description: '',
            chqNo: '',
            amount: '',
        },
    ]);
    const [allAccounts, setAllAccounts] = useState<Account[]>([]); // Store all accounts
    const [filteredCreditAccounts, setFilteredCreditAccounts] = useState<ComboBoxOption[]>([]); // Filtered accounts for Credit
    const [filteredDebitAccounts, setFilteredDebitAccounts] = useState<ComboBoxOption[]>([]); // Filtered accounts for Debit
    const [paymentTypes, setPaymentTypes] = useState<ComboBoxOption[]>([]);
    const [showForm, setShowForm] = useState(false); // State to control form visibility

    // Fetch accounts and payment types
    useEffect(() => {
        const fetchData = async () => {
            try {
                const [accountsRes, paymentTypesRes] = await Promise.all([
                    axios.get<Account[]>('/api/accounts'),
                    axios.get<PaymentType[]>('/api/payment-types', {
                        params: {
                            filter_names: [
                                'cust-to-cust',
                                'jv-payment',
                                'bank-payment',
                                'bank-receipt',
                                'cash-payment',
                                'cash-receipt',
                            ],
                        },
                    }),
                ]);

                // Transform accounts data
                // Store all accounts and set initial filtered lists
                setAllAccounts(accountsRes.data);
                const allAccountsOptions = accountsRes.data.map((account: Account) => ({
                    value: account.id.toString(),
                    label: account.name,
                }));
                setFilteredCreditAccounts(allAccountsOptions);
                setFilteredDebitAccounts(allAccountsOptions);

                // Transform payment types data
                setPaymentTypes(
                    paymentTypesRes.data.map((type: PaymentType) => ({
                        value: type.id.toString(),
                        label: type.label,
                    })),
                );
            } catch (err) {
                console.error('Error fetching data:', err);
                toast.error('Failed to load data');
            }
        };

        fetchData();
    }, []);

    // Effect to filter accounts based on selected payment type
    useEffect(() => {
        const allAccountsOptions = allAccounts.map((account) => ({
            value: account.id.toString(),
            label: account.name,
        }));

        let creditAccounts = allAccountsOptions;
        let debitAccounts = allAccountsOptions;

        const selectedPaymentType = paymentTypes.find(type => type.value === paymentType);
        const paymentTypeName = selectedPaymentType?.label.toLowerCase(); // Use label for comparison as per backend

        if (paymentTypeName) {
            switch (paymentTypeName) {
                case 'bank payment':
                    creditAccounts = allAccounts
                        .filter(account => account.account_type?.name === 'bank')
                        .map(account => ({
                            value: account.id.toString(),
                            label: account.name,
                        }));
                    // Debit accounts remain unfiltered
                    break;
                case 'bank receipt':
                    // Credit accounts remain unfiltered
                    debitAccounts = allAccounts
                        .filter(account => account.account_type?.name === 'bank')
                        .map(account => ({
                            value: account.id.toString(),
                            label: account.name,
                        }));
                    break;
                case 'cash payment':
                    creditAccounts = allAccounts
                        .filter(account => account.account_type?.name === 'cash')
                        .map(account => ({
                            value: account.id.toString(),
                            label: account.name,
                        }));
                    // Debit accounts remain unfiltered
                    break;
                case 'cash receipt':
                    // Credit accounts remain unfiltered
                    debitAccounts = allAccounts
                        .filter(account => account.account_type?.name === 'cash')
                        .map(account => ({
                            value: account.id.toString(),
                            label: account.name,
                        }));
                    break;
                // For 'cust-to-cust' and 'jv-payment', no filtering is needed,
                // so creditAccounts and debitAccounts remain allAccountsOptions
            }
        }

        setFilteredCreditAccounts(creditAccounts);
        setFilteredDebitAccounts(debitAccounts);

    }, [paymentType, allAccounts, paymentTypes]); // Re-run when paymentType, allAccounts, or paymentTypes change

    // Effect to show form when date and payment type are selected
    useEffect(() => {
        if (date && paymentType) {
            setShowForm(true);
        } else {
            setShowForm(false);
        }
    }, [date, paymentType]);

    const updateEntry = (id: string, field: keyof JournalEntry, value: string) => {
        setEntries((prevEntries) => {
            let updatedEntries = prevEntries.map((entry) => (entry.id === id ? { ...entry, [field]: value } : entry));

            // Check if the updated entry is now completely empty
            const updatedEntry = updatedEntries.find((entry) => entry.id === id);
            const isEntryEmpty =
                updatedEntry &&
                updatedEntry.creditAccount === '' &&
                updatedEntry.debitAccount === '' &&
                updatedEntry.description === '' &&
                updatedEntry.chqNo === '' &&
                updatedEntry.amount === '';

            // If the entry is empty and there's more than one row, remove it
            if (isEntryEmpty && updatedEntries.length > 1) {
                updatedEntries = updatedEntries.filter((entry) => entry.id !== id);
            }

            // Ensure there is always at least one empty row at the end
            const lastEntry = updatedEntries[updatedEntries.length - 1];
            const isLastRowEmpty =
                lastEntry &&
                lastEntry.creditAccount === '' &&
                lastEntry.debitAccount === '' &&
                lastEntry.description === '' &&
                lastEntry.chqNo === '' &&
                lastEntry.amount === '';

            if (!isLastRowEmpty) {
                updatedEntries = [
                    ...updatedEntries,
                    {
                        id: uuidv4(),
                        creditAccount: '',
                        debitAccount: '',
                        description: '',
                        chqNo: '',
                        amount: '',
                    },
                ];
            }

            return updatedEntries;
        });
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        // Helper to format date as YYYY-MM-DD in local time
        const formatDateLocal = (d: Date) => {
            const year = d.getFullYear();
            const month = (d.getMonth() + 1).toString().padStart(2, '0');
            const day = d.getDate().toString().padStart(2, '0');
            return `${year}-${month}-${day}`;
        };

        // Filter out the last entry row if all its fields are empty
        const entriesToSubmit = entries.filter((entry, index) => {
            // Check if it's the last entry
            if (index === entries.length - 1) {
                // Check if all fields in the last entry are empty
                return !(
                    entry.creditAccount === '' &&
                    entry.debitAccount === '' &&
                    entry.description === '' &&
                    entry.chqNo === '' &&
                    entry.amount === ''
                );
            }
            // Keep all other entries
            return true;
        });

        // Frontend validation: ensure all required fields within each entry are filled and provide row-specific errors.
        const validationErrors: string[] = [];
        entriesToSubmit.forEach((entry, index) => {
            const rowNumber = index + 1;
            if (!entry.creditAccount) {
                validationErrors.push(`Credit account is missing in row ${rowNumber}.`);
            }
            if (!entry.debitAccount) {
                validationErrors.push(`Debit account is missing in row ${rowNumber}.`);
            }
            if (!entry.amount) {
                validationErrors.push(`Amount is missing in row ${rowNumber}.`);
            }
            // Optional: Add validation for description if it's considered required
            // if (!entry.description) {
            //   validationErrors.push(`Description is missing in row ${rowNumber}.`);
            // }
        });

        if (validationErrors.length > 0) {
            validationErrors.forEach((error) => toast.error(error));
            return;
        }

        setLoading(true);
        try {
            // Ensure date is defined before formatting, although the form is hidden until date is selected
            if (!date) {
                toast.error('Date is not selected.'); // This should not happen with the current logic, but good for safety
                setLoading(false);
                return;
            }

            const formattedEntries = entriesToSubmit.map((entry) => ({
                date: formatDateLocal(date),
                payment_type_id: paymentType,
                credit_account_id: entry.creditAccount,
                debit_account_id: entry.debitAccount,
                description: entry.description,
                chq_no: entry.chqNo,
                amount: parseFloat(entry.amount.replace(/,/g, '')),
            }));

            await axios.post('/api/journal-entries/multiple', {
                date: formatDateLocal(date),
                payment_type_id: paymentType,
                entries: formattedEntries,
            });

            toast.success('Journal entries created successfully');
            // Reset form
            setDate(new Date());
            setPaymentType('');
            setEntries([
                {
                    id: uuidv4(),
                    creditAccount: '',
                    debitAccount: '',
                    description: '',
                    chqNo: '',
                    amount: '',
                },
            ]);
        } catch (err) {
            console.error('Error creating journal entries:', err);
            toast.error('Failed to create journal entries');
        } finally {
            setLoading(false);
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Mulitple Journal Entry" />
            <div className="container mx-auto py-6">
                <Card>
                    <CardHeader>
                        <CardTitle>Multiple Journal Entry</CardTitle>
                    </CardHeader>
                    <CardContent>
                        {/* Combine Date and Payment Type in one responsive grid row */}
                        <div className="grid grid-cols-1 items-center gap-4 md:grid-cols-12">
                            <div className="flex items-center gap-2 sm:col-span-6 md:col-span-4 lg:col-span-3">
                                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Date:</label>
                                <div>
                                    <DatePicker selected={date} onSelect={setDate} />
                                </div>
                            </div>
                            <div className="flex items-center gap-2 sm:col-span-6 md:col-span-3 lg:col-span-3">
                                <label className="text-sm font-medium whitespace-nowrap text-gray-700 dark:text-gray-300">Payment Type:</label>
                                <ComboBox value={paymentType} onChange={setPaymentType} options={paymentTypes} placeholder="Select payment type" />
                            </div>
                        </div>

                        {showForm ? (
                            <form onSubmit={handleSubmit} className="mt-4 space-y-4">
                                <div className="space-y-4">
                                    <Table>
                                        <TableHeader>
                                            <TableRow>
                                                <TableHead>Credit Account</TableHead>
                                                <TableHead>Debit Account</TableHead>
                                                <TableHead>Description</TableHead>
                                                <TableHead>CHQ NO</TableHead>
                                                <TableHead>Amount</TableHead>
                                            </TableRow>
                                        </TableHeader>
                                        <TableBody>
                                            {entries.map((entry) => (
                                                <TableRow key={entry.id}>
                                                    <TableCell>
                                                        <ComboBox
                                                            value={entry.creditAccount}
                                                            onChange={(value) => updateEntry(entry.id, 'creditAccount', value)}
                                                            options={filteredCreditAccounts} // Use filtered credit accounts
                                                            placeholder="Select credit account"
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <ComboBox
                                                            value={entry.debitAccount}
                                                            onChange={(value) => updateEntry(entry.id, 'debitAccount', value)}
                                                            options={filteredDebitAccounts} // Use filtered debit accounts
                                                            placeholder="Select debit account"
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <Input
                                                            value={entry.description}
                                                            onChange={(e) => updateEntry(entry.id, 'description', e.target.value)}
                                                            placeholder="Enter description"
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <Input
                                                            value={entry.chqNo}
                                                            onChange={(e) => updateEntry(entry.id, 'chqNo', e.target.value)}
                                                            placeholder="Enter CHQ NO"
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <NumericFormat
                                                            value={entry.amount}
                                                            onValueChange={(values) => updateEntry(entry.id, 'amount', values.value)}
                                                            thousandSeparator={true}
                                                            // decimalScale={2}
                                                            // fixedDecimalScale={true}
                                                            allowNegative={false}
                                                            customInput={Input}
                                                            placeholder="Enter amount"
                                                        />
                                                    </TableCell>
                                                </TableRow>
                                            ))}
                                        </TableBody>
                                    </Table>

                                    <div className="mt-4 flex justify-end space-x-4 text-lg font-semibold">
                                        <div>
                                            Total Amount:{' '}
                                            <NumericFormat
                                                value={entries.reduce((sum, entry) => sum + parseFloat(entry.amount.replace(/,/g, '') || '0'), 0)}
                                                displayType={'text'}
                                                thousandSeparator={true}
                                                decimalScale={0}
                                                fixedDecimalScale={false}
                                            />
                                        </div>
                                    </div>

                                    <div className="mt-4 flex justify-end">
                                        <Button type="submit" disabled={loading}>
                                            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                                            {loading ? 'Submitting Entries...' : 'Submit Entries'}
                                        </Button>
                                    </div>
                                </div>
                            </form>
                        ) : (
                            <div className="mt-4 flex items-center justify-center space-x-3 p-6 text-blue-800 dark:text-blue-200">
                                <Info className="h-5 w-5" />
                                <span className="text-base font-medium">Please select a Date and Payment Type to start adding journal entries.</span>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
