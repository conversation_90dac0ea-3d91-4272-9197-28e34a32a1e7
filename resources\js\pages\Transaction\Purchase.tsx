import React, { useState, useEffect } from 'react';
import { Inertia } from '@inertiajs/inertia'; // For navigation if needed
import { useForm, Head } from '@inertiajs/react'; // Using Inertia's form helper and Head
import axios from 'axios'; // For direct API calls
import { NumericFormat, type NumberFormatValues } from 'react-number-format'; // Import OnValueChange for type

// Shadcn/UI Components (ensure these are correctly imported from your project structure)
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ComboBox } from '@/components/ui/combobox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'; // Basic select
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'; // For DatePicker
import { Calendar } from '@/components/ui/calendar';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

// Icons (example using lucide-react)
import { CalendarIcon, Check, ChevronsUpDown, Loader2, Save, ShoppingCart } from 'lucide-react';
import type { Errors } from '@inertiajs/core'; 
import AppLayout from '@/layouts/app-layout';
import { BreadcrumbItem } from '@/types';
import { toast } from 'sonner';

// Helper for formatting numbers (e.g., from react-number-format or a custom one)
// For simplicity, using basic toLocaleString, replace with a proper library if needed for NumericFormat features.
const formatNumber = (value: number | string, options?: Intl.NumberFormatOptions) => {
    const num = typeof value === 'string' ? parseFloat(value.replace(/,/g, '')) : value;
    if (isNaN(num)) return '';
    return num.toLocaleString(undefined, options);
};

// Helper to parse number strings (remove commas)
const parseFormattedNumber = (value: string | number): number => {
    if (typeof value === 'number') return value;
    if (!value) return 0;
    return parseFloat(value.replace(/,/g, '')) || 0;
};

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Purchase Entry', href: '/Transaction/Purchase' }
];

interface Account {
    id: number;
    name: string;
    currency_code?: string; // For currency accounts
    formula?: 'm' | 'd';    // For currency accounts
}

interface TransactionDetailsResponse {
    transaction_number: string;
    currency_balance: number;
    pkr_balance: number;
    average_rate: number;
    formula_type: 'm' | 'd';
}

interface PurchaseFormData {
    date: string;
    selectDr: string; // Store as string from ComboBox
    selectCr: string; // Store as string from ComboBox
    currency_amount: string; // Stored as string from NumericFormat's unformatted value
    rate: string;            // Stored as string from NumericFormat's unformatted value
    pkr_amount: string;      // Display value, also string
    description: string;
    refno: string;
    orderid: string;
    is_debit: '0' | '1';
    [key: string]: any; // Index signature for compatibility
}

// This interface represents the data structure for submission (numeric amounts)
interface PurchaseFormSubmitData {
    date: string;
    selectDr: number | null;
    selectCr: number | null;
    currency_amount: number;
    rate: number;
    pkr_amount: number;
    description: string;
    refno: string;
    orderid: string;
    is_debit: '0' | '1';
    [key: string]: any; // Index signature for compatibility with Inertia RequestPayload
}

const initialFormData: PurchaseFormData = {
    date: new Date().toISOString().split('T')[0], // YYYY-MM-DD
    selectDr: '', // Currency Account ID
    selectCr: '', // Customer Account ID
    currency_amount: '',
    rate: '',
    pkr_amount: '', // Read-only, calculated
    description: '',
    refno: '',
    orderid: '', // P-NO, fetched
    is_debit: '0', // BANAM: "0" for No (default), "1" for Yes
};

export default function PurchasePage() {
    // Use the form without transform
    const { data, setData, post, processing, errors, reset, clearErrors } = useForm<PurchaseFormData>(initialFormData);

    const [currencyAccounts, setCurrencyAccounts] = useState<Account[]>([]);
    const [customerAccounts, setCustomerAccounts] = useState<Account[]>([]);
    const [selectedCurrencyDetails, setSelectedCurrencyDetails] = useState<TransactionDetailsResponse | null>(null);
    const [loadingDetails, setLoadingDetails] = useState(false);
    const [datePickerOpen, setDatePickerOpen] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false); // New state for submission

    // Fetch initial accounts data
    useEffect(() => {
        axios.get('/api/accounts', { params: { account_type_id: 5 } }) /* ID for Currency Account Type */
            .then(res => setCurrencyAccounts(res.data))
            .catch(err => console.error("Failed to fetch currency accounts", err));

        axios.get('/api/accounts', { params: { exclude_account_type_ids: '4,5,7' } })
            .then(res => setCustomerAccounts(res.data))
            .catch(err => console.error("Failed to fetch customer accounts", err));
        // Replace 'your_currency_account_type_id' and 'your_customer_pkr_account_type_id' 
        // with actual IDs from your account_types table (e.g., by name lookup if possible or hardcode if stable)
    }, []);

    const handleCurrencyAccountChange = async (accountId: string) => {
        setData('selectDr', accountId);
        if (accountId) {
            setLoadingDetails(true);
            try {
                const response = await axios.get<TransactionDetailsResponse>('/api/purchase-entries/get-details', {
                    params: { currency_account_id: accountId }
                });
                setSelectedCurrencyDetails(response.data);
                setData('orderid', response.data.transaction_number);
            } catch (error) {
                console.error("Failed to fetch transaction details", error);
                setSelectedCurrencyDetails(null);
                setData('orderid', '');
                toast.error("Failed to fetch P-NO and account details.");
            } finally {
                setLoadingDetails(false);
            }
        } else {
            setSelectedCurrencyDetails(null);
            setData('orderid', '');
        }
    };

    // Calculate PKR Amount
    useEffect(() => {
        if (data.currency_amount && data.rate && selectedCurrencyDetails?.formula_type) {
            const currAmount = parseFormattedNumber(data.currency_amount);
            const exchangeRate = parseFormattedNumber(data.rate);

            if (!isNaN(currAmount) && !isNaN(exchangeRate) && exchangeRate > 0) {
                const calculatedPkrFloat = selectedCurrencyDetails.formula_type === 'd'
                    ? currAmount / exchangeRate
                    : currAmount * exchangeRate;
                
                const pkrForState = parseFloat(calculatedPkrFloat.toFixed(2));
                setData('pkr_amount', formatNumber(pkrForState, { minimumFractionDigits: 2, maximumFractionDigits: 2 }));
            } else {
                setData('pkr_amount', '');
            }
        } else if (!data.currency_amount || !data.rate) {
            setData('pkr_amount', '');
        }
    }, [data.currency_amount, data.rate, selectedCurrencyDetails, setData]);

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        clearErrors();
        
        // Instead of relying on transform, manually prepare the submission data
        const submissionData: PurchaseFormSubmitData = {
            date: data.date,
            selectDr: data.selectDr ? Number(data.selectDr) : null,
            selectCr: data.selectCr ? Number(data.selectCr) : null,
            currency_amount: parseFormattedNumber(data.currency_amount),
            rate: parseFormattedNumber(data.rate),
            pkr_amount: parseFormattedNumber(data.pkr_amount),
            description: data.description,
            refno: data.refno,
            orderid: data.orderid,
            is_debit: data.is_debit,
        };
        
        // console.log("Submitting data:", submissionData); // This should show in console
        
        // Use axios for direct API calls instead of Inertia to avoid the Inertia response requirement
        setIsSubmitting(true); // Set loading state
        axios.post('/api/purchase-entries', submissionData)
            .then(response => {
                // console.log("Success response:", response.data);
                toast.success('Purchase order placed successfully!');
                
                // Reset form to initial state
                reset();
                setSelectedCurrencyDetails(null);
                
                // Set a new default date for next entry
                setData('date', new Date().toISOString().split('T')[0]);
            })
            .catch(error => {
                console.error("Submission error:", error.response?.data);
                const errorData = error.response?.data?.errors || {};
                const firstKey = Object.keys(errorData)[0];
                const errorMessage = firstKey 
                    ? errorData[firstKey] 
                    : (error.response?.data?.message || 'An unknown error occurred');
                
                toast.error('Purchase order failed!', {
                    description: Array.isArray(errorMessage) ? errorMessage[0] : errorMessage,
                });
            })
            .finally(() => {
                setIsSubmitting(false); // Clear loading state
            });
    };

    const handleNumericValueChange = (field: keyof PurchaseFormData, values: NumberFormatValues) => {
        setData(field, values.value); // values.value is the unformatted numeric string
    };

    const handleIsDebitChange = (value: string) => {
        if (value === '0' || value === '1') {
            setData('is_debit', value);
        }
    };

    const handleCustomerAccountChange = (value: string) => {
        setData('selectCr', value);
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>

            <div className="container mx-auto p-4">
                <Head title="Create Purchase Entry" />
                <Card className="max-w-3xl mx-auto">
                <CardHeader>
                    <CardTitle>Create Purchase Entry</CardTitle>
                </CardHeader>
                <form onSubmit={handleSubmit}>
                    <CardContent className="space-y-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <Label htmlFor="date">Date</Label>
                                <Popover open={datePickerOpen} onOpenChange={setDatePickerOpen}>
                                    <PopoverTrigger asChild>
                                        <Button
                                            variant={"outline"}
                                            className={`w-full justify-start text-left font-normal ${
                                                !data.date && "text-muted-foreground" 
                                            }`}
                                        >
                                            <CalendarIcon className="mr-2 h-4 w-4" />
                                            {data.date ? new Date(data.date).toLocaleDateString() : <span>Pick a date</span>}
                                        </Button>
                                    </PopoverTrigger>
                                    <PopoverContent className="w-auto p-0">
                                        <Calendar
                                            mode="single"
                                            selected={data.date ? new Date(data.date) : undefined}
                                            onSelect={(selectedDate) => {
                                                if (selectedDate) {
                                                    // Fix timezone issue by manually formatting the date
                                                    // This ensures we get the exact date selected without timezone adjustments
                                                    const year = selectedDate.getFullYear();
                                                    const month = String(selectedDate.getMonth() + 1).padStart(2, '0');
                                                    const day = String(selectedDate.getDate()).padStart(2, '0');
                                                    setData('date', `${year}-${month}-${day}`);
                                                } else {
                                                    setData('date', '');
                                                }
                                                setDatePickerOpen(false);
                                            }}
                                            initialFocus
                                        />
                                    </PopoverContent>
                                </Popover>
                                {errors.date && <p className="text-sm text-red-600 mt-1">{errors.date}</p>}
                            </div>

                            <div>
                                <Label htmlFor="currencyAccount">Currency Account (DR)</Label>
                                <ComboBox 
                                    value={data.selectDr}
                                    onChange={handleCurrencyAccountChange}
                                    options={currencyAccounts.map(acc => ({
                                        label: `${acc.name} ${acc.currency_code ? `(${acc.currency_code})` : ''}`,
                                        value: String(acc.id)
                                    }))}
                                    placeholder="Select currency..."
                                />
                                {errors.selectDr && <p className="text-sm text-red-600 mt-1">{errors.selectDr}</p>}
                            </div>
                        </div>

                        { (loadingDetails || data.orderid || selectedCurrencyDetails) &&
                            <div className="space-y-4">
                                {selectedCurrencyDetails && (
                                    <Alert className="mt-4 bg-sky-50 dark:bg-sky-900 border-sky-200 dark:border-sky-700 text-sky-800 dark:text-sky-100">
                                        <ShoppingCart className="h-5 w-5 inline-block mr-2" />
                                        <AlertTitle className="font-semibold">Currency Details</AlertTitle>
                                        <AlertDescription className="grid grid-cols-2 md:grid-cols-2 gap-x-4 gap-y-1 text-sm mt-2">
                                            <span>Balance: {formatNumber(selectedCurrencyDetails.currency_balance, {maximumFractionDigits:2})}</span>
                                            <span>PKR Value: {formatNumber(selectedCurrencyDetails.pkr_balance, {maximumFractionDigits:2})}</span>
                                            <span>Avg. Cost: {formatNumber(selectedCurrencyDetails.average_rate, {minimumFractionDigits:2, maximumFractionDigits: 2})}</span>
                                            <span>Formula: {selectedCurrencyDetails.formula_type?.toUpperCase()}</span>
                                        </AlertDescription>
                                    </Alert>
                                )}
                                {data.orderid && (
                                    <div>
                                        <Input id="transactionNumber" value={data.orderid} readOnly className="text-center text-2xl font-bold bg-gray-100 dark:bg-gray-900" />
                                    </div>
                                )}
                                {loadingDetails && !data.orderid && <p className="text-center">Loading P-NO and details...</p>}
                            </div>
                        }
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <Label htmlFor="referenceNumber">Manual Ref#</Label>
                                <Input id="referenceNumber" value={data.refno} onChange={e => setData('refno', e.target.value)} />
                                {errors.refno && <p className="text-sm text-red-600 mt-1">{errors.refno}</p>}
                            </div>
                            <div>
                                <Label htmlFor="customerAccount">Customer PKR Account (CR)</Label>
                                <ComboBox 
                                    value={data.selectCr}
                                    onChange={handleCustomerAccountChange}
                                    options={customerAccounts.map(acc => ({
                                        label: acc.name,
                                        value: String(acc.id) 
                                    }))}
                                    placeholder="Select customer..."
                                />
                                {errors.selectCr && <p className="text-sm text-red-600 mt-1">{errors.selectCr}</p>}
                            </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 items-end">
                            <div>
                                <Label htmlFor="currencyAmount">Amount (Currency)</Label>
                                <NumericFormat 
                                    id="currencyAmount"
                                    value={data.currency_amount} 
                                    onValueChange={(values: NumberFormatValues) => handleNumericValueChange('currency_amount', values)}
                                    customInput={Input}
                                    thousandSeparator={true}
                                    allowNegative={false}
                                    decimalScale={0}
                                    placeholder="e.g., 1000"
                                    className="w-full"
                                />
                                {errors.currency_amount && <p className="text-sm text-red-600 mt-1">{errors.currency_amount}</p>}
                            </div>
                            <div>
                                <Label htmlFor="exchangeRate">Rate</Label>
                                <NumericFormat 
                                    id="exchangeRate" 
                                    value={data.rate} 
                                    onValueChange={(values: NumberFormatValues) => handleNumericValueChange('rate', values)}
                                    customInput={Input}
                                    thousandSeparator={true}
                                    allowNegative={false}
                                    decimalScale={2}
                                    fixedDecimalScale
                                    placeholder="e.g., 275.50"
                                    className="w-full"
                                    disabled={!data.selectDr || loadingDetails || isSubmitting}
                                />
                                {errors.rate && <p className="text-sm text-red-600 mt-1">{errors.rate}</p>}
                            </div>
                            <div>
                                <Label htmlFor="pkrAmount">Amount (PKR)</Label>
                                <NumericFormat 
                                    id="pkrAmount"
                                    value={data.pkr_amount} 
                                    readOnly 
                                    customInput={Input} 
                                    thousandSeparator={true}
                                    decimalScale={2}
                                    fixedDecimalScale
                                    className="font-bold bg-gray-100 dark:bg-gray-700 w-full" // User removed p-2 border rounded, adjusted bg for dark
                                />
                                {errors.pkr_amount && <p className="text-sm text-red-600 mt-1">{errors.pkr_amount}</p>}
                            </div>
                        </div>
                        
                        <div>
                            <Label htmlFor="description">Description</Label>
                            <Input id="description" value={data.description} onChange={e => setData('description', e.target.value)} />
                            {errors.description && <p className="text-sm text-red-600 mt-1">{errors.description}</p>}
                        </div>

                        <div>
                            <Label htmlFor="is_debit">Is Debit (BANAM)?</Label>
                            <Select onValueChange={handleIsDebitChange} value={data.is_debit}>
                                <SelectTrigger id="is_debit">
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="0">No</SelectItem>
                                    <SelectItem value="1">Yes</SelectItem>
                                </SelectContent>
                            </Select>
                            {errors.is_debit && <p className="text-sm text-red-600 mt-1">{errors.is_debit}</p>}
                        </div>

                    </CardContent>
                    <CardFooter>
                        <Button type="submit" className="w-full md:w-auto" disabled={isSubmitting || loadingDetails}>
                            {isSubmitting ? (
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            ) : (
                                <ShoppingCart className="mr-2 h-4 w-4" />
                            )}
                            Place Order
                        </Button>
                    </CardFooter>
                </form>
                </Card>
            </div>
        </AppLayout>
    );
}
