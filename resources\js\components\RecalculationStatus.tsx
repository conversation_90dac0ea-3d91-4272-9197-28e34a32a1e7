import React, { useState, useEffect } from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import {
    Info,
    RotateCw,
    CheckCircle,
    AlertCircle,
    Clock,
    RefreshCw,
    ChevronDown,
    ChevronUp,
    Loader2
} from 'lucide-react';
import axios from 'axios';

interface RecalculationLog {
    id: number;
    from_date: string;
    trigger_type: string;
    description: string;
    status: 'pending' | 'processing' | 'completed' | 'failed';
    error_message?: string;
    created_at: string;
    updated_at: string;
}

interface RecalculationStatusData {
    account_id: number;
    pending_jobs: number;
    processing_jobs: number;
    is_busy: boolean;
    recent_logs: RecalculationLog[];
}

interface RecalculationStatusProps {
    accountId?: number;
    showSystemStatus?: boolean;
    autoRefresh?: boolean;
    refreshInterval?: number;
}

const RecalculationStatus: React.FC<RecalculationStatusProps> = ({
    accountId,
    showSystemStatus = false,
    autoRefresh = true,
    refreshInterval = 5000
}) => {
    const [statusData, setStatusData] = useState<RecalculationStatusData | null>(null);
    const [systemStats, setSystemStats] = useState<any>(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [expanded, setExpanded] = useState(false);

    const fetchStatus = async () => {
        if (!accountId && !showSystemStatus) return;

        setLoading(true);
        setError(null);

        try {
            if (showSystemStatus) {
                const response = await axios.get('/api/recalculation-status/system');
                setSystemStats(response.data);
            } else if (accountId) {
                const response = await axios.get(`/api/recalculation-status/account`, {
                    params: { account_id: accountId }
                });
                setStatusData(response.data);
            }
        } catch (err: any) {
            setError(err.response?.data?.message || 'Failed to fetch recalculation status');
        } finally {
            setLoading(false);
        }
    };

    const retryFailed = async () => {
        if (!accountId) return;

        try {
            await axios.post('/api/recalculation-status/retry-failed', {
                account_id: accountId
            });
            fetchStatus(); // Refresh after retry
        } catch (err: any) {
            setError(err.response?.data?.message || 'Failed to retry failed jobs');
        }
    };

    useEffect(() => {
        fetchStatus();

        if (autoRefresh) {
            const interval = setInterval(fetchStatus, refreshInterval);
            return () => clearInterval(interval);
        }
    }, [accountId, showSystemStatus, autoRefresh, refreshInterval]);

    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'pending':
                return <Clock className="h-4 w-4 text-yellow-500" />;
            case 'processing':
                return <RotateCw className="h-4 w-4 text-blue-500 animate-spin" />;
            case 'completed':
                return <CheckCircle className="h-4 w-4 text-green-500" />;
            case 'failed':
                return <AlertCircle className="h-4 w-4 text-red-500" />;
            default:
                return <Info className="h-4 w-4" />;
        }
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'pending': return 'bg-yellow-100 text-yellow-800';
            case 'processing': return 'bg-blue-100 text-blue-800';
            case 'completed': return 'bg-green-100 text-green-800';
            case 'failed': return 'bg-red-100 text-red-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    if (showSystemStatus && systemStats) {
        return (
            <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">System Recalculation Status</CardTitle>
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={fetchStatus}
                        disabled={loading}
                    >
                        <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                    </Button>
                </CardHeader>
                <CardContent>
                    <div className="flex gap-4 flex-wrap">
                        <div className="flex items-center gap-2">
                            <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                                {systemStats.statistics.pending_jobs}
                            </Badge>
                            <span className="text-sm">Pending</span>
                        </div>
                        <div className="flex items-center gap-2">
                            <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                                {systemStats.statistics.processing_jobs}
                            </Badge>
                            <span className="text-sm">Processing</span>
                        </div>
                        <div className="flex items-center gap-2">
                            <Badge variant="secondary" className="bg-red-100 text-red-800">
                                {systemStats.statistics.failed_jobs}
                            </Badge>
                            <span className="text-sm">Failed (24h)</span>
                        </div>
                        <span className="text-sm">Success Rate: {systemStats.statistics.success_rate}%</span>
                    </div>

                    {systemStats.system_busy && (
                        <Alert className="mt-2">
                            <Info className="h-4 w-4" />
                            <AlertDescription>
                                System is processing recalculations
                            </AlertDescription>
                        </Alert>
                    )}
                </CardContent>
            </Card>
        );
    }

    if (!statusData && !loading) {
        return null;
    }

    const isBusy = statusData?.is_busy || false;
    const hasRecentLogs = statusData?.recent_logs && statusData.recent_logs.length > 0;

    return (
        <div className="mb-4">
            {isBusy && (
                <Alert>
                    <RotateCw className="h-4 w-4 animate-spin" />
                    <AlertDescription>
                        <div className="flex items-center justify-between">
                            <span>
                                Balance recalculation in progress
                                ({statusData?.pending_jobs} pending, {statusData?.processing_jobs} processing)
                            </span>
                            <div className="flex items-center gap-2">
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={fetchStatus}
                                    disabled={loading}
                                >
                                    <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                                </Button>
                                {hasRecentLogs && (
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => setExpanded(!expanded)}
                                    >
                                        {expanded ? 'Hide' : 'Show'} Details
                                    </Button>
                                )}
                            </div>
                        </div>
                    </AlertDescription>
                </Alert>
            )}

            {error && (
                <Alert variant="destructive" className="mt-2">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                        <div className="flex items-center justify-between">
                            <div>
                                <div className="font-medium">Error loading recalculation status</div>
                                <div className="text-sm">{error}</div>
                            </div>
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => setError(null)}
                            >
                                ×
                            </Button>
                        </div>
                    </AlertDescription>
                </Alert>
            )}

            {hasRecentLogs && (
                <Collapsible open={expanded} onOpenChange={setExpanded}>
                    <CollapsibleTrigger asChild>
                        <Button variant="ghost" className="w-full justify-between p-0 h-auto">
                            <span className="font-medium">Recent Recalculation Activity</span>
                            {expanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                        </Button>
                    </CollapsibleTrigger>
                    <CollapsibleContent className="space-y-2 mt-2">
                        {statusData.recent_logs.map((log: RecalculationLog) => (
                            <div key={log.id} className="flex items-start gap-3 p-3 border rounded-lg">
                                <div className="mt-0.5">
                                    {getStatusIcon(log.status)}
                                </div>
                                <div className="flex-1 min-w-0">
                                    <div className="flex items-center gap-2 mb-1">
                                        <span className="font-medium text-sm">{log.description}</span>
                                        <Badge className={getStatusColor(log.status)}>
                                            {log.status.toUpperCase()}
                                        </Badge>
                                    </div>
                                    <div className="text-xs text-gray-600 mb-1">
                                        From: {log.from_date} | Type: {log.trigger_type}
                                    </div>
                                    <div className="text-xs text-gray-500">
                                        {new Date(log.created_at).toLocaleString()}
                                    </div>
                                    {log.error_message && (
                                        <div className="text-xs text-red-600 mt-1">
                                            Error: {log.error_message}
                                        </div>
                                    )}
                                </div>
                            </div>
                        ))}

                        {statusData.recent_logs.some(log => log.status === 'failed') && (
                            <div className="text-center mt-4">
                                <Button
                                    variant="destructive"
                                    size="sm"
                                    onClick={retryFailed}
                                >
                                    Retry Failed Jobs
                                </Button>
                            </div>
                        )}
                    </CollapsibleContent>
                </Collapsible>
            )}

            {loading && !statusData && (
                <div className="text-center p-4">
                    <Loader2 className="h-4 w-4 animate-spin inline-block" />
                    <span className="ml-2">Loading recalculation status...</span>
                </div>
            )}
        </div>
    );
};

export default RecalculationStatus;
