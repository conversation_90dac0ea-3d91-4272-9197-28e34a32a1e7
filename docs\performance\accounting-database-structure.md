# Laravel 12 Accounting Database Structure Guide

## Table of Contents
1. [Core Financial Tables](#core-financial-tables)
2. [Relationship Best Practices](#relationship-best-practices)
3. [Multi-Tenancy Support](#multi-tenancy-support)
4. [Polymorphic Relationships](#polymorphic-relationships)
5. [UUID Implementation](#uuid-implementation)
6. [Indexing Strategy](#indexing-strategy)

## Core Financial Tables

### 1. Chart of Accounts Structure
```php
// database/migrations/create_chart_of_accounts_table.php
Schema::create('account_types', function (Blueprint $table) {
    $table->id();
    $table->string('name');  // Asset, Liability, Equity, Revenue, Expense
    $table->string('code_prefix', 2);
    $table->timestamps();
});

Schema::create('account_categories', function (Blueprint $table) {
    $table->id();
    $table->foreignId('account_type_id')->constrained()->onDelete('restrict');
    $table->string('name');  // e.g., Current Assets, Fixed Assets
    $table->string('code_prefix', 4);
    $table->timestamps();
});

Schema::create('accounts', function (Blueprint $table) {
    $table->id();
    $table->foreignId('account_type_id')->constrained()->onDelete('restrict');
    $table->foreignId('account_category_id')->constrained()->onDelete('restrict');
    $table->string('code', 10)->unique();
    $table->string('name');
    $table->text('description')->nullable();
    $table->boolean('is_active')->default(true);
    $table->boolean('allows_manual_entries')->default(true);
    $table->decimal('opening_balance', 15, 4)->default(0);
    $table->string('currency', 3)->default('USD');
    $table->timestamps();
    $table->softDeletes();

    $table->index(['account_type_id', 'account_category_id', 'is_active']);
    $table->index(['code', 'is_active']);
});
```

### 2. Transaction Structure
```php
Schema::create('fiscal_years', function (Blueprint $table) {
    $table->id();
    $table->string('name');
    $table->date('start_date');
    $table->date('end_date');
    $table->boolean('is_closed')->default(false);
    $table->timestamps();
    
    $table->index(['start_date', 'end_date']);
});

Schema::create('journals', function (Blueprint $table) {
    $table->id();
    $table->string('code')->unique();
    $table->string('name');
    $table->boolean('is_active')->default(true);
    $table->timestamps();
});

Schema::create('transactions', function (Blueprint $table) {
    $table->uuid('id')->primary();
    $table->foreignId('journal_id')->constrained()->onDelete('restrict');
    $table->foreignId('fiscal_year_id')->constrained()->onDelete('restrict');
    $table->string('reference_no')->unique();
    $table->date('transaction_date');
    $table->string('description');
    $table->string('currency', 3)->default('USD');
    $table->decimal('exchange_rate', 10, 6)->default(1);
    $table->boolean('is_posted')->default(false);
    $table->timestamp('posted_at')->nullable();
    $table->foreignId('created_by')->constrained('users')->onDelete('restrict');
    $table->foreignId('posted_by')->nullable()->constrained('users')->onDelete('restrict');
    $table->timestamps();
    $table->softDeletes();

    $table->index(['transaction_date', 'is_posted']);
    $table->index(['journal_id', 'fiscal_year_id']);
});

Schema::create('transaction_lines', function (Blueprint $table) {
    $table->id();
    $table->foreignUuid('transaction_id')->constrained()->onDelete('cascade');
    $table->foreignId('account_id')->constrained()->onDelete('restrict');
    $table->decimal('debit', 15, 4)->default(0);
    $table->decimal('credit', 15, 4)->default(0);
    $table->string('description')->nullable();
    $table->json('metadata')->nullable();
    $table->timestamps();

    $table->index(['transaction_id', 'account_id']);
});
```

## Relationship Best Practices

### 1. Model Relationships
```php
class Account extends Model
{
    use HasFactory, SoftDeletes;

    public function type()
    {
        return $this->belongsTo(AccountType::class, 'account_type_id');
    }

    public function category()
    {
        return $this->belongsTo(AccountCategory::class, 'account_category_id');
    }

    public function transactionLines()
    {
        return $this->hasMany(TransactionLine::class);
    }

    public function balance()
    {
        return $this->transactionLines()
            ->selectRaw('
                account_id,
                SUM(debit - credit) as balance,
                MAX(created_at) as last_transaction
            ')
            ->groupBy('account_id');
    }
}

class Transaction extends Model
{
    use HasFactory, SoftDeletes;

    protected $keyType = 'string';
    public $incrementing = false;

    protected static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
            $model->id = (string) Str::uuid();
        });
    }

    public function journal()
    {
        return $this->belongsTo(Journal::class);
    }

    public function fiscalYear()
    {
        return $this->belongsTo(FiscalYear::class);
    }

    public function lines()
    {
        return $this->hasMany(TransactionLine::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }
}
```

## Multi-Tenancy Support

### 1. Organization Structure
```php
Schema::create('organizations', function (Blueprint $table) {
    $table->id();
    $table->string('name');
    $table->string('code')->unique();
    $table->string('currency', 3)->default('USD');
    $table->json('settings')->nullable();
    $table->timestamps();
});

Schema::create('organization_users', function (Blueprint $table) {
    $table->id();
    $table->foreignId('organization_id')->constrained()->onDelete('cascade');
    $table->foreignId('user_id')->constrained()->onDelete('cascade');
    $table->string('role');  // owner, admin, accountant, viewer
    $table->timestamps();

    $table->unique(['organization_id', 'user_id']);
});

// Add organization_id to relevant tables
Schema::table('accounts', function (Blueprint $table) {
    $table->foreignId('organization_id')->after('id')->constrained()->onDelete('cascade');
    $table->unique(['organization_id', 'code']);
});
```

### 2. Multi-Database Implementation
```php
// config/database.php
'connections' => [
    'tenant' => [
        'driver' => 'mysql',
        'url' => env('DATABASE_URL'),
        'host' => env('DB_HOST', '127.0.0.1'),
        'port' => env('DB_PORT', '3306'),
        'database' => null, // Will be set dynamically
        'username' => env('DB_USERNAME', 'forge'),
        'password' => env('DB_PASSWORD', ''),
    ],
],

// app/Models/Organization.php
class Organization extends Model
{
    public function configure()
    {
        config([
            'database.connections.tenant.database' => 'acc_' . $this->id
        ]);

        DB::purge('tenant');
        DB::reconnect('tenant');
    }
}
```

## Polymorphic Relationships

### 1. Document Attachments
```php
Schema::create('documents', function (Blueprint $table) {
    $table->id();
    $table->morphs('documentable');
    $table->string('type'); // invoice, receipt, contract
    $table->string('file_path');
    $table->string('original_name');
    $table->string('mime_type');
    $table->unsignedBigInteger('size');
    $table->timestamps();
    
    $table->index(['documentable_type', 'documentable_id', 'type']);
});

// Usage in models
class Transaction extends Model
{
    public function documents()
    {
        return $this->morphMany(Document::class, 'documentable');
    }
}
```

### 2. Audit Trail
```php
Schema::create('audits', function (Blueprint $table) {
    $table->id();
    $table->morphs('auditable');
    $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
    $table->string('event'); // created, updated, deleted
    $table->json('old_values')->nullable();
    $table->json('new_values')->nullable();
    $table->string('ip_address', 45)->nullable();
    $table->string('user_agent')->nullable();
    $table->timestamps();

    $table->index(['auditable_type', 'auditable_id', 'created_at']);
});
```

## UUID Implementation

### 1. UUID Trait
```php
trait HasUuid
{
    protected static function bootHasUuid()
    {
        static::creating(function ($model) {
            if (!$model->getKey()) {
                $model->{$model->getKeyName()} = (string) Str::uuid();
            }
        });
    }

    public function getIncrementing()
    {
        return false;
    }

    public function getKeyType()
    {
        return 'string';
    }
}

// Usage in models
class Transaction extends Model
{
    use HasUuid;
}
```

## Indexing Strategy

### 1. Performance-Optimized Indexes
```php
// Add composite indexes for common queries
Schema::table('transaction_lines', function (Blueprint $table) {
    // For balance calculations
    $table->index([
        'account_id', 
        'transaction_date', 
        DB::raw('(debit - credit)')
    ]);

    // For transaction search
    $table->index([
        'account_id',
        'created_at',
        'transaction_id'
    ]);
});

// Add full-text search capabilities
Schema::table('transactions', function (Blueprint $table) {
    $table->fullText(['description', 'reference_no']);
});
```

### 2. Index Management
```php
class AccountingIndexManager
{
    public function optimizeIndexes()
    {
        DB::statement('ANALYZE TABLE transactions, transaction_lines, accounts');
        
        // Rebuild specific indexes
        DB::statement('ALTER TABLE transaction_lines 
            DROP INDEX account_balance_idx,
            ADD INDEX account_balance_idx (account_id, transaction_date, debit, credit)
        ');
    }
}
``` 