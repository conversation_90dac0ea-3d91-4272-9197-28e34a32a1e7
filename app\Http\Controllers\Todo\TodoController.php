<?php

namespace App\Http\Controllers\Todo;

use App\Http\Requests\Todo\StoreTodoRequest;
use App\Http\Requests\Todo\UpdateTodoRequest;
use App\Models\Todo;
use Illuminate\Support\Facades\Auth;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use App\Http\Controllers\Controller;

class TodoController extends Controller
{
    use AuthorizesRequests;

    public function index()
    {
        return Todo::where('user_id', Auth::id())
            ->orderBy('created_at', 'desc')
            ->limit(15)
            ->get();
    }

    public function store(StoreTodoRequest $request)
    {
        $validated = $request->validated();

        $validated['user_id'] = Auth::id();
        $validated['completed'] = false;

        return Todo::create($validated);
    }

    public function update(UpdateTodoRequest $request, Todo $todo)
    {
        $this->authorize('update', $todo);

        $validated = $request->validated();

        $todo->update($validated);

        return $todo;
    }

    public function destroy(Todo $todo)
    {
        $this->authorize('delete', $todo);

        $todo->delete();

        return response()->noContent();
    }
}
