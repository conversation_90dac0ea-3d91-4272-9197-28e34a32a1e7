import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, DialogContent, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/react';
import axios from 'axios';
import { BarChart2, Book, BookOpen, Check, Edit2, FileText, Plus, ShoppingBag, ShoppingCart, Trash2 } from 'lucide-react';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
];

export default function Dashboard() {
    interface Todo {
        id: number;
        title: string;
        completed: boolean;
        created_at: string;
    }

    const [todos, setTodos] = useState<Todo[]>([]);
    const [newTodo, setNewTodo] = useState('');
    const [editingTodo, setEditingTodo] = useState<number | null>(null);
    const [editText, setEditText] = useState('');
    const [isDialogOpen, setIsDialogOpen] = useState(false);

    useEffect(() => {
        fetchTodos();
    }, []);

    const fetchTodos = async () => {
        try {
            const response = await axios.get('/api/todos?limit=15');
            setTodos(response.data);
        } catch {
            toast.error('Failed to fetch todos');
        }
    };

    const handleAddTodo = async () => {
        if (!newTodo.trim()) return;

        // Optimistic UI update
        const tempId = Date.now(); // Use timestamp as temporary ID
        const newTodoItem: Todo = {
            id: tempId,
            title: newTodo.trim(),
            completed: false,
            created_at: new Date().toISOString(), // Use current time optimistically
        };

        setTodos((prev) => {
            const updatedTodos = [newTodoItem, ...prev];
            return updatedTodos.length > 15 ? updatedTodos.slice(0, 15) : updatedTodos;
        });
        setNewTodo('');
        setIsDialogOpen(false);
        // Optimistic toast (optional, could show only on server success)
        // toast.info('Adding todo...');

        try {
            const response = await axios.post('/api/todos', {
                title: newTodoItem.title,
                completed: newTodoItem.completed,
            });

            // Replace temporary item with actual item from server
            setTodos((prev) => prev.map((todo) => (todo.id === tempId ? response.data : todo)));
            toast.success('Todo added successfully');
        } catch {
            toast.error('Failed to add todo');
            // Rollback UI update on error
            setTodos((prev) => prev.filter((todo) => todo.id !== tempId));
        }
    };

    const handleUpdateTodo = async (id: number) => {
        if (!editText.trim()) return;

        const originalTodos = [...todos]; // Store original state for rollback
        const originalTodo = originalTodos.find((t) => t.id === id);
        if (!originalTodo) return;

        // Optimistic UI update
        setTodos((prev) => prev.map((todo) => (todo.id === id ? { ...todo, title: editText.trim() } : todo)));
        setEditingTodo(null);
        // toast.info('Updating todo...'); // Optional optimistic toast

        try {
            await axios.put(`/api/todos/${id}`, {
                title: editText.trim(),
                // Send the original completed status unless it was changed separately
                completed: originalTodo.completed,
            });
            toast.success('Todo updated successfully');
            // No need to setTodos again on success, UI is already updated
        } catch {
            toast.error('Failed to update todo');
            // Rollback UI update on error
            setTodos(originalTodos);
            // Optionally re-open edit mode if needed
            // setEditingTodo(id);
            // setEditText(originalTodo.title);
        }
    };

    const handleToggleTodo = async (id: number) => {
        const originalTodos = [...todos]; // Store original state for rollback
        const originalTodo = originalTodos.find((t) => t.id === id);
        if (!originalTodo) return;

        // Optimistic UI update
        setTodos((prev) => prev.map((todo) => (todo.id === id ? { ...todo, completed: !todo.completed } : todo)));
        // toast.info('Updating status...'); // Optional optimistic toast

        try {
            await axios.put(`/api/todos/${id}`, {
                completed: !originalTodo.completed, // Send the toggled state
            });
            toast.success('Todo status updated');
            // No need to setTodos again on success, UI is already updated
        } catch {
            toast.error('Failed to update todo status');
            // Rollback UI update on error
            setTodos(originalTodos);
        }
    };

    const getTimeBadgeClass = (createdAt: string | undefined) => {
        if (!createdAt) return 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300';
        const diff = Date.now() - new Date(createdAt).getTime();
        const minutes = Math.floor(diff / 60000);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);
        const weeks = Math.floor(days / 7);
        const months = Math.floor(days / 30);

        if (minutes < 60) return 'bg-purple-100 text-purple-700 dark:bg-purple-900 dark:text-purple-300';
        if (hours < 24) return 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300';
        if (days < 7) return 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300';
        if (weeks < 5) return 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900 dark:text-yellow-300';
        if (months < 12) return 'bg-pink-100 text-pink-700 dark:bg-pink-900 dark:text-pink-300';
        return 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300';
    };

    const getTimeBadgeText = (createdAt: string | undefined) => {
        if (!createdAt) return '';
        const diff = Date.now() - new Date(createdAt).getTime();
        const minutes = Math.floor(diff / 60000);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);
        const weeks = Math.floor(days / 7);
        const months = Math.floor(days / 30);
        const years = Math.floor(months / 12);

        if (minutes < 60) return minutes === 1 ? '1 m' : `${minutes} m`;
        if (hours < 24) return hours === 1 ? '1 hour' : `${hours} hours`;
        if (days < 7) return days === 1 ? '1 day' : `${days} days`;
        if (weeks < 5) return weeks === 1 ? '1 week' : `${weeks} weeks`;
        if (months < 12) return months === 1 ? '1 month' : `${months} months`;
        return years === 1 ? '1 year' : `${years} years`;
    };

    const handleDeleteTodo = async (id: number) => {
        const originalTodos = [...todos]; // Store original state for rollback
        const todoToDelete = originalTodos.find(t => t.id === id);
        if (!todoToDelete) return;

        // Optimistic UI update
        setTodos((prev) => prev.filter((todo) => todo.id !== id));
        // toast.info('Deleting todo...'); // Optional optimistic toast

        try {
            await axios.delete(`/api/todos/${id}`);
            toast.success('Todo deleted successfully');
            // No need to setTodos again on success, UI is already updated
        } catch {
            toast.error('Failed to delete todo');
            // Rollback UI update on error
            setTodos(originalTodos);
        }
    };
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Dashboard" />
            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                {/* Dashboard Cards Section */}
                <div className="mb-6 grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
                    {/* DAILY BOOK */}
                    <div
                        className="group animate-dashboard-card relative flex flex-col items-start overflow-hidden rounded-lg bg-blue-600 p-4 text-white shadow transition-transform duration-300 hover:z-20 hover:scale-[1.03] hover:shadow-lg"
                        style={{ animationDelay: '0.10s' }}
                    >
                        <BookOpen className="animate-dashboard-icon absolute top-3 right-3 h-16 w-16 opacity-20" />
                        <div className="animate-dashboard-label z-10 mb-2 text-sm font-semibold">DAILY BOOK</div>
                        <div className="animate-dashboard-value z-10 mb-1 text-2xl font-bold">0</div>
                        <div className="animate-dashboard-desc z-10 text-xs opacity-80">Shows All Daily Transactions</div>
                    </div>
                    {/* LEDGERS */}
                    <div
                        className="group animate-dashboard-card relative flex flex-col items-start overflow-hidden rounded-lg bg-green-600 p-4 text-white shadow transition-transform duration-300 hover:z-20 hover:scale-[1.03] hover:shadow-lg"
                        style={{ animationDelay: '0.22s' }}
                    >
                        <Book className="animate-dashboard-icon absolute top-3 right-3 h-16 w-16 opacity-20" />
                        <div className="animate-dashboard-label z-10 mb-2 text-sm font-semibold">LEDGERS</div>
                        <div className="animate-dashboard-value z-10 mb-1 text-2xl font-bold">0</div>
                        <div className="animate-dashboard-desc z-10 text-xs opacity-80">All ledgers detail</div>
                    </div>
                    {/* BALANCE SHEET */}
                    <div
                        className="group animate-dashboard-card relative flex flex-col items-start overflow-hidden rounded-lg bg-red-600 p-4 text-white shadow transition-transform duration-300 hover:z-20 hover:scale-[1.03] hover:shadow-lg"
                        style={{ animationDelay: '0.34s' }}
                    >
                        <FileText className="animate-dashboard-icon absolute top-3 right-3 h-16 w-16 opacity-20" />
                        <div className="animate-dashboard-label z-10 mb-2 text-sm font-semibold">BALANCE SHEET</div>
                        <div className="animate-dashboard-value z-10 mb-1 text-2xl font-bold">0</div>
                        <div className="animate-dashboard-desc z-10 text-xs opacity-80">Shows Balance sheet</div>
                    </div>
                    {/* ALL PURCHASE ORDERS */}
                    <div
                        className="group animate-dashboard-card relative flex flex-col items-start overflow-hidden rounded-lg bg-yellow-500 p-4 text-white shadow transition-transform duration-300 hover:z-20 hover:scale-[1.03] hover:shadow-lg"
                        style={{ animationDelay: '0.46s' }}
                    >
                        <ShoppingCart className="animate-dashboard-icon absolute top-3 right-3 h-16 w-16 opacity-20" />
                        <div className="animate-dashboard-label z-10 mb-2 text-sm font-semibold">ALL PURCHASE ORDERS</div>
                        <div className="animate-dashboard-value z-10 mb-1 text-2xl font-bold">0</div>
                        <div className="animate-dashboard-desc z-10 text-xs opacity-80">Shows buying orders list</div>
                    </div>
                    {/* ALL SALE ORDERS */}
                    <div
                        className="group animate-dashboard-card relative flex flex-col items-start overflow-hidden rounded-lg bg-purple-500 p-4 text-white shadow transition-transform duration-300 hover:z-20 hover:scale-[1.03] hover:shadow-lg"
                        style={{ animationDelay: '0.58s' }}
                    >
                        <ShoppingBag className="animate-dashboard-icon absolute top-3 right-3 h-16 w-16 opacity-20" />
                        <div className="animate-dashboard-label z-10 mb-2 text-sm font-semibold">ALL SALE ORDERS</div>
                        <div className="animate-dashboard-value z-10 mb-1 text-2xl font-bold">0</div>
                        <div className="animate-dashboard-desc z-10 text-xs opacity-80">Shows sale orders list</div>
                    </div>
                    {/* INCOME STATEMENT */}
                    <div
                        className="group animate-dashboard-card relative flex flex-col items-start overflow-hidden rounded-lg bg-gray-700 p-4 text-white shadow transition-transform duration-300 hover:z-20 hover:scale-[1.03] hover:shadow-lg"
                        style={{ animationDelay: '0.70s' }}
                    >
                        <BarChart2 className="animate-dashboard-icon absolute top-3 right-3 h-16 w-16 opacity-20" />
                        <div className="animate-dashboard-label z-10 mb-2 text-sm font-semibold">INCOME STATEMENT</div>
                        <div className="animate-dashboard-value z-10 mb-1 text-2xl font-bold">0</div>
                        <div className="animate-dashboard-desc z-10 text-xs opacity-80">Shows Daily P&L Info</div>
                    </div>
                </div>

                {/* To Do List and Quick Email in grid: To Do List spans 4/6, Email spans 2/6 */}
                <div className="animate-dashboard-section mb-6 grid grid-cols-1 gap-6 lg:grid-cols-6">
                    {/* To Do List Section */}
                    <div className="border-sidebar-border/70 dark:border-sidebar-border animate-dashboard-todo col-span-1 rounded-xl border bg-white p-6 shadow lg:col-span-4 dark:bg-[#18181b]">
                        <div className="mb-4 flex items-center justify-between">
                            <h2 className="text-lg font-semibold">To Do List</h2>
                            <div>
                                <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                                    <DialogTrigger asChild>
                                        <Button className="hover:bg-background dark:hover:backgroun hover:border-accent gap-2 transition-colors hover:border-2 hover:text-black dark:hover:text-white">
                                            <Plus size={16} /> Add Todo
                                        </Button>
                                    </DialogTrigger>
                                    <DialogContent>
                                        <DialogTitle>Add New Todo</DialogTitle>
                                        <div className="mt-4 flex gap-2">
                                            <input
                                                type="text"
                                                value={newTodo}
                                                onChange={(e) => setNewTodo(e.target.value)}
                                                placeholder="Todo description"
                                                className="flex-1 rounded border p-2"
                                                onKeyDown={(e) => {
                                                    if (e.key === 'Enter') {
                                                        e.preventDefault();
                                                        handleAddTodo();
                                                    }
                                                }}
                                            />
                                            <Button onClick={handleAddTodo}>Add</Button>

                                        </div>
                                    </DialogContent>
                                </Dialog>
                            </div>
                        </div>
                        <ul className="max-h-[250px] overflow-y-auto divide-y divide-gray-200 dark:divide-gray-700">
                            {todos.map((todo) => (
                                <li
                                    key={todo.id}
                                    className="group flex items-center justify-between px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800"
                                >
                                    {editingTodo === todo.id ? (
                                        <div className="relative flex flex-1 items-center gap-2">
                                            <input
                                                type="text"
                                                value={editText}
                                                onChange={(e) => setEditText(e.target.value)}
                                                onKeyDown={(e) => {
                                                    if (e.key === 'Enter') {
                                                        handleUpdateTodo(todo.id);
                                                    } else if (e.key === 'Escape') {
                                                        setEditingTodo(null);
                                                    }
                                                }}
                                                className="flex-1 rounded border p-2"
                                                autoFocus
                                            />
                                            <button
                                                onClick={() => handleUpdateTodo(todo.id)}
                                                className="p-1 text-green-600 transition hover:text-green-800"
                                                aria-label="Confirm edit"
                                            >
                                                <Check size={20} />
                                            </button>
                                            <button
                                                onClick={() => setEditingTodo(null)}
                                                className="p-1 text-red-600 transition hover:text-red-800"
                                                aria-label="Cancel edit"
                                            >
                                                <Trash2 size={20} />
                                            </button>
                                            {/* Hide badges and icons when editing */}
                                            <style>{`
                                                li > div.flex.items-center.gap-2 > span,
                                                li > div.flex.items-center.gap-2 > div {
                                                    display: none !important;
                                                }
                                            `}</style>
                                        </div>
                                    ) : (
                                        <>
                                            <span className={`flex-1 ${todo.completed ? 'text-gray-400 line-through' : ''}`}>{todo.title}</span>

                                            <div className="flex items-center gap-2">
                                                <span
                                                    className={`rounded px-2 py-0.5 text-xs group-hover:hidden ${getTimeBadgeClass(todo.created_at)}`}
                                                >
                                                    {getTimeBadgeText(todo.created_at)}
                                                </span>

                                                <div className="hidden items-center gap-1 group-hover:flex">
                                                    <button
                                                        onClick={() => {
                                                            setEditingTodo(todo.id);
                                                            setEditText(todo.title);
                                                        }}
                                                        className="p-1 text-gray-500 transition hover:text-yellow-500"
                                                    >
                                                        <Edit2 size={14} />
                                                    </button>
                                                    <button
                                                        onClick={() => handleToggleTodo(todo.id)}
                                                        className="p-1 text-gray-500 transition hover:text-green-500"
                                                    >
                                                        <Check size={14} />
                                                    </button>
                                                    <button
                                                        onClick={() => handleDeleteTodo(todo.id)}
                                                        className="p-1 text-gray-500 transition hover:text-red-500"
                                                    >
                                                        <Trash2 size={14} />
                                                    </button>
                                                </div>
                                            </div>
                                        </>
                                    )}
                                </li>
                            ))}
                        </ul>
                    </div>

                    {/* Quick Email Section */}
                    <div className="border-sidebar-border/70 dark:border-sidebar-border animate-dashboard-email col-span-1 rounded-xl border bg-white p-6 shadow lg:col-span-2 dark:bg-[#18181b]">
                        <h2 className="mb-4 text-lg font-semibold">Quick Email</h2>
                        <div className="mb-4">
                            <input type="email" placeholder="Recipient" className="mb-2 w-full rounded border p-2" />
                            <input type="text" placeholder="Subject" className="mb-2 w-full rounded border p-2" />
                            <textarea placeholder="Message" className="mb-2 w-full rounded border p-2" rows={3}></textarea>
                        </div>
                        <button className="rounded bg-blue-600 px-4 py-2 font-semibold text-white transition hover:bg-blue-700">Send</button>
                    </div>
                </div>
                {/* Animations CSS */}
                <style>
                    {`
                        @keyframes dashboard-card {
                            0% { opacity: 0; transform: translateY(32px) scale(0.96); }
                            60% { opacity: 1; transform: translateY(-4px) scale(1.03); }
                            100% { opacity: 1; transform: translateY(0) scale(1); }
                        }
                        .animate-dashboard-card {
                            opacity: 0;
                            animation: dashboard-card 0.9s cubic-bezier(0.4,0,0.2,1) both;
                        }
                        @keyframes dashboard-icon {
                            0% { opacity: 0; transform: scale(0.7) rotate(-12deg); }
                            60% { opacity: 0.1; transform: scale(1.08) rotate(6deg); }
                            100% { opacity: 0.2; transform: scale(1) rotate(0deg); }
                        }
                        .animate-dashboard-icon {
                            animation: dashboard-icon 0.9s cubic-bezier(0.4,0,0.2,1) both;
                        }
                        @keyframes dashboard-label {
                            0% { opacity: 0; transform: translateY(16px); }
                            100% { opacity: 1; transform: translateY(0); }
                        }
                        .animate-dashboard-label {
                            opacity: 0;
                            animation: dashboard-label 0.8s cubic-bezier(0.4,0,0.2,1) 0.09s both;
                        }
                        @keyframes dashboard-value {
                            0% { opacity: 0; transform: scale(0.8); }
                            60% { opacity: 1; transform: scale(1.08); }
                            100% { opacity: 1; transform: scale(1); }
                        }
                        .animate-dashboard-value {
                            opacity: 0;
                            animation: dashboard-value 0.8s cubic-bezier(0.4,0,0.2,1) 0.18s both;
                        }
                        @keyframes dashboard-desc {
                            0% { opacity: 0; transform: translateY(10px); }
                            100% { opacity: 1; transform: translateY(0); }
                        }
                        .animate-dashboard-desc {
                            opacity: 0;
                            animation: dashboard-desc 0.8s cubic-bezier(0.4,0,0.2,1) 0.25s both;
                        }
                        /* Section fade-in */
                        @keyframes dashboard-section {
                            0% { opacity: 0; transform: translateY(32px); }
                            100% { opacity: 1; transform: translateY(0); }
                        }
                        .animate-dashboard-section {
                            opacity: 0;
                            animation: dashboard-section 1s cubic-bezier(0.4,0,0.2,1) 0.75s both;
                        }
                        /* To Do List and Email fade-in */
                        @keyframes dashboard-todo {
                            0% { opacity: 0; transform: translateX(-24px); }
                            100% { opacity: 1; transform: translateX(0); }
                        }
                        .animate-dashboard-todo {
                            opacity: 0;
                            animation: dashboard-todo 0.8s cubic-bezier(0.4,0,0.2,1) 1.05s both;
                        }
                        @keyframes dashboard-email {
                            0% { opacity: 0; transform: translateX(24px); }
                            100% { opacity: 1; transform: translateX(0); }
                        }
                        .animate-dashboard-email {
                            opacity: 0;
                            animation: dashboard-email 0.8s cubic-bezier(0.4,0,0.2,1) 1.15s both;
                        }
                    `}
                </style>
            </div>
        </AppLayout>
    );
}
