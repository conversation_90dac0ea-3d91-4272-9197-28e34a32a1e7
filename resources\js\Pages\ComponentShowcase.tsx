import React, { useState } from 'react'
import { Head } from '@inertiajs/react'
import AppLayout from '@/layouts/app-layout'
import { Button } from '@/components/ui/button'
import { ComboBox } from '@/components/ui/combobox'
import { toast, Toaster } from '@/components/ui/sonner'
import {
  Save,
  Download,
  Upload,
  Settings,
  User,
  Star,
  Heart,
  ShoppingCart,
  Bell,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Clock,
  Search,
  Filter,
  Edit,
  Trash2,
  Plus,
  Minus,
  Check,
  X,
  AlertTriangle,
  Info,
  CheckCircle,
  XCircle,
  Copy,
  Code2,
  Eye,
  EyeOff
} from 'lucide-react'

// Code Block Component with Copy Functionality
const CodeBlock = ({
  code,
  language = 'tsx',
  title,
  showCopy = true
}: {
  code: string
  language?: string
  title?: string
  showCopy?: boolean
}) => {
  const [copied, setCopied] = useState(false)
  const [isExpanded, setIsExpanded] = useState(false)

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(code)
      setCopied(true)
      toast.success('Code copied to clipboard!')
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      toast.error('Failed to copy code')
    }
  }

  const isLongCode = code.split('\n').length > 10

  return (
    <div className="relative">
      {title && (
        <div className="flex items-center justify-between mb-2">
          <h5 className="text-sm font-medium text-slate-700 dark:text-slate-300">{title}</h5>
          <div className="flex items-center gap-2">
            {isLongCode && (
              <button
                onClick={() => setIsExpanded(!isExpanded)}
                className="flex items-center gap-1 text-xs text-slate-500 hover:text-slate-700 dark:hover:text-slate-300"
              >
                {isExpanded ? <EyeOff className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
                {isExpanded ? 'Collapse' : 'Expand'}
              </button>
            )}
            {showCopy && (
              <button
                onClick={copyToClipboard}
                className="flex items-center gap-1 text-xs text-slate-500 hover:text-slate-700 dark:hover:text-slate-300 transition-colors"
              >
                <Copy className="h-3 w-3" />
                {copied ? 'Copied!' : 'Copy'}
              </button>
            )}
          </div>
        </div>
      )}
      <div className="relative">
        <pre className={`text-xs text-slate-600 dark:text-slate-400 bg-slate-50 dark:bg-slate-900 rounded-lg p-4 overflow-x-auto border ${
          isLongCode && !isExpanded ? 'max-h-48 overflow-y-hidden' : ''
        }`}>
          <code>{code}</code>
        </pre>
        {!showCopy && title && (
          <button
            onClick={copyToClipboard}
            className="absolute top-2 right-2 p-1.5 rounded-md bg-slate-200 dark:bg-slate-700 hover:bg-slate-300 dark:hover:bg-slate-600 transition-colors"
            title="Copy code"
          >
            <Copy className="h-3 w-3" />
          </button>
        )}
      </div>
    </div>
  )
}

// Component Preview Container
const ComponentPreview = ({
  title,
  description,
  children,
  code,
  className = ""
}: {
  title: string
  description?: string
  children: React.ReactNode
  code?: string
  className?: string
}) => {
  return (
    <div className="border border-slate-200 dark:border-slate-700 rounded-lg overflow-hidden">
      <div className="p-4 border-b border-slate-200 dark:border-slate-700">
        <h4 className="font-medium text-slate-900 dark:text-slate-100">{title}</h4>
        {description && (
          <p className="text-sm text-slate-600 dark:text-slate-400 mt-1">{description}</p>
        )}
      </div>
      <div className={`p-6 bg-white dark:bg-slate-800 ${className}`}>
        {children}
      </div>
      {code && (
        <div className="border-t border-slate-200 dark:border-slate-700 p-4 bg-slate-50 dark:bg-slate-900">
          <CodeBlock code={code} showCopy={false} />
        </div>
      )}
    </div>
  )
}

export default function ComponentShowcase() {
  // Separate state for each ComboBox to prevent interference
  const [selectedValueSmall, setSelectedValueSmall] = useState('')
  const [selectedValueMedium, setSelectedValueMedium] = useState('')
  const [selectedValueLarge, setSelectedValueLarge] = useState('')
  const [selectedValueClearable, setSelectedValueClearable] = useState('')
  const [selectedValueRich, setSelectedValueRich] = useState('')
  const [selectedValueLocation, setSelectedValueLocation] = useState('')
  const [selectedValueContact, setSelectedValueContact] = useState('')
  const [selectedValueTeam, setSelectedValueTeam] = useState('')
  const [selectedGroupValue, setSelectedGroupValue] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  // Sample options for ComboBox
  const simpleOptions = [
    { value: 'option1', label: 'Option 1' },
    { value: 'option2', label: 'Option 2' },
    { value: 'option3', label: 'Option 3' },
    { value: 'option4', label: 'Option 4', disabled: true },
  ]

  const richOptions = [
    {
      value: 'user1',
      label: 'John Doe',
      description: 'Software Engineer • San Francisco, CA',
      icon: <User className="h-4 w-4" />
    },
    {
      value: 'user2',
      label: 'Jane Smith',
      description: 'Product Manager • New York, NY',
      icon: <User className="h-4 w-4" />
    },
    {
      value: 'user3',
      label: 'Bob Johnson',
      description: 'Designer • Austin, TX',
      icon: <User className="h-4 w-4" />
    },
    {
      value: 'user4',
      label: 'Alice Wilson',
      description: 'DevOps Engineer • Seattle, WA',
      icon: <User className="h-4 w-4" />
    },
  ]

  const locationOptions = [
    {
      value: 'ny',
      label: 'New York',
      description: 'New York, United States • EST',
      icon: <MapPin className="h-4 w-4" />
    },
    {
      value: 'london',
      label: 'London',
      description: 'London, United Kingdom • GMT',
      icon: <MapPin className="h-4 w-4" />
    },
    {
      value: 'tokyo',
      label: 'Tokyo',
      description: 'Tokyo, Japan • JST',
      icon: <MapPin className="h-4 w-4" />
    },
    {
      value: 'sydney',
      label: 'Sydney',
      description: 'Sydney, Australia • AEDT',
      icon: <MapPin className="h-4 w-4" />
    },
  ]

  const contactOptions = [
    {
      value: 'email1',
      label: '<EMAIL>',
      description: 'Primary work email',
      icon: <Mail className="h-4 w-4" />
    },
    {
      value: 'phone1',
      label: '+****************',
      description: 'Mobile phone',
      icon: <Phone className="h-4 w-4" />
    },
    {
      value: 'phone2',
      label: '+****************',
      description: 'Office phone',
      icon: <Phone className="h-4 w-4" />
    },
  ]

  const groupedOptions = [
    {
      label: 'Actions',
      options: [
        { value: 'save', label: 'Save', icon: <Save className="h-4 w-4" /> },
        { value: 'download', label: 'Download', icon: <Download className="h-4 w-4" /> },
        { value: 'upload', label: 'Upload', icon: <Upload className="h-4 w-4" /> },
      ]
    },
    {
      label: 'Settings',
      options: [
        { value: 'profile', label: 'Profile', icon: <User className="h-4 w-4" /> },
        { value: 'preferences', label: 'Preferences', icon: <Settings className="h-4 w-4" /> },
        { value: 'notifications', label: 'Notifications', icon: <Bell className="h-4 w-4" /> },
      ]
    }
  ]

  const handleLoadingDemo = async () => {
    setIsLoading(true)
    setTimeout(() => {
      setIsLoading(false)
      toast.success('Operation completed successfully!')
    }, 2000)
  }



  const showRichToastExamples = () => {
    toast.success({
      title: 'Payment Successful',
      description: 'Your payment of $99.99 has been processed successfully.',
      action: {
        label: 'View Receipt',
        onClick: () => toast.info('📄 Opening receipt in new window...')
      },
      cancel: {
        label: 'Dismiss',
        onClick: () => toast.dismiss()
      }
    })
  }

  const showLocationToast = () => {
    toast.info({
      title: 'Location Services',
      description: 'Your current location: New York, NY, USA',
      richContent: (
        <div className="flex items-center gap-3 p-2">
          <MapPin className="h-8 w-8 text-blue-500" />
          <div>
            <div className="font-semibold">📍 Location Detected</div>
            <div className="text-sm text-muted-foreground">Latitude: 40.7128° N</div>
            <div className="text-sm text-muted-foreground">Longitude: 74.0060° W</div>
            <div className="text-xs text-muted-foreground mt-1">Accuracy: ±10 meters</div>
          </div>
        </div>
      ),
      action: {
        label: 'Open Maps',
        onClick: () => toast.success('🗺️ Opening Google Maps...')
      }
    })
  }

  const showNotificationToast = () => {
    toast.custom(
      <div className="flex items-center gap-3 p-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg shadow-lg">
        <Bell className="h-6 w-6" />
        <div className="flex-1">
          <div className="font-semibold">📱 New Notification</div>
          <div className="text-sm opacity-90">You have 3 unread messages</div>
          <div className="text-xs opacity-75 mt-1">2 minutes ago</div>
        </div>
        <Button
          size="sm"
          variant="ghost"
          className="text-white hover:bg-white/20"
          onClick={() => toast.success('📬 Opening messages...')}
        >
          View
        </Button>
      </div>
    )
  }

  const showEmailToast = () => {
    toast.info({
      title: 'New Email Received',
      description: 'From: <EMAIL>',
      richContent: (
        <div className="space-y-3">
          <div className="flex items-center gap-3">
            <Mail className="h-5 w-5 text-blue-500" />
            <div>
              <div className="font-medium">📧 Project Update</div>
              <div className="text-sm text-muted-foreground"><EMAIL></div>
            </div>
          </div>
          <div className="text-sm bg-slate-50 dark:bg-slate-800 p-2 rounded">
            "The quarterly report is ready for review. Please check the attached documents..."
          </div>
          <div className="flex gap-2">
            <Button size="sm" onClick={() => toast.success('📖 Opening email...')}>
              Read
            </Button>
            <Button size="sm" variant="outline" onClick={() => toast.info('📁 Email archived')}>
              Archive
            </Button>
          </div>
        </div>
      )
    })
  }

  const showCalendarToast = () => {
    toast.warning({
      title: 'Meeting Reminder',
      description: 'Team standup in 15 minutes',
      richContent: (
        <div className="space-y-3">
          <div className="flex items-center gap-3">
            <Calendar className="h-5 w-5 text-orange-500" />
            <div>
              <div className="font-medium">📅 Daily Standup</div>
              <div className="text-sm text-muted-foreground">Conference Room A</div>
            </div>
          </div>
          <div className="flex items-center gap-2 text-sm">
            <Clock className="h-4 w-4" />
            <span>10:00 AM - 10:30 AM</span>
          </div>
          <div className="flex gap-2">
            <Button size="sm" onClick={() => toast.success('📞 Joining meeting...')}>
              Join Now
            </Button>
            <Button size="sm" variant="outline" onClick={() => toast.info('⏰ Reminder snoozed for 5 minutes')}>
              Snooze
            </Button>
          </div>
        </div>
      )
    })
  }

  const showProgressToast = () => {
    const toastId = toast.loading('📤 Uploading file... 0%')

    let progress = 0
    const interval = setInterval(() => {
      progress += 20

      if (progress <= 80) {
        toast.loading(`📤 Uploading file... ${progress}%`, { id: toastId })
      } else {
        clearInterval(interval)
        toast.success('✅ File uploaded successfully!', { id: toastId })
      }
    }, 500)
  }

  const showPromiseToast = () => {
    const fetchData = new Promise((resolve, reject) => {
      setTimeout(() => {
        if (Math.random() > 0.3) {
          resolve('Data loaded successfully!')
        } else {
          reject(new Error('Failed to load data'))
        }
      }, 3000)
    })

    toast.promise(fetchData, {
      loading: '🔄 Loading user data...',
      success: (data) => `✅ ${data}`,
      error: '❌ Failed to load data. Please try again.',
    })
  }

  return (
    <AppLayout breadcrumbs={[{ title: 'Component Showcase', href: '/component-showcase' }]}>
      <Head title="Component Showcase" />

      {/* Add Toaster for toast notifications */}
      <Toaster position="bottom-right" />

      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 p-8">
        <div className="max-w-7xl mx-auto space-y-12">
          {/* Header */}
          <div className="text-center space-y-4">
            <h1 className="text-4xl font-bold text-slate-900 dark:text-slate-100">
              Enhanced shadcn Components Showcase
            </h1>
            <p className="text-lg text-slate-600 dark:text-slate-400 max-w-3xl mx-auto">
              Explore our enhanced shadcn components with comprehensive variants, loading states, 
              and advanced features for modern web applications.
            </p>
          </div>

          {/* Button Component Showcase */}
          <section className="bg-white dark:bg-slate-800 rounded-xl p-8 shadow-lg">
            <div className="mb-8">
              <h2 className="text-3xl font-bold mb-3 text-slate-900 dark:text-slate-100">
                Button Component
              </h2>
              <p className="text-lg text-slate-600 dark:text-slate-400">
                A versatile button component with multiple variants, sizes, and states.
              </p>
            </div>

            <div className="space-y-8">
              {/* Default Button */}
              <ComponentPreview
                title="Default"
                description="The default button style."
                code={`<Button>Button</Button>`}
              >
                <Button>Button</Button>
              </ComponentPreview>

              {/* Size Variants */}
              <ComponentPreview
                title="Sizes"
                description="Different button sizes from extra small to extra large."
                code={`<Button size="xs">Extra Small</Button>
<Button size="sm">Small</Button>
<Button size="default">Default</Button>
<Button size="lg">Large</Button>
<Button size="xl">Extra Large</Button>`}
              >
                <div className="flex flex-wrap items-center gap-4">
                  <Button size="xs">Extra Small</Button>
                  <Button size="sm">Small</Button>
                  <Button size="default">Default</Button>
                  <Button size="lg">Large</Button>
                  <Button size="xl">Extra Large</Button>
                </div>
              </ComponentPreview>

              {/* Semantic Variants */}
              <ComponentPreview
                title="Variants"
                description="Different button variants for various use cases."
                code={`<Button variant="default">Default</Button>
<Button variant="success">Success</Button>
<Button variant="warning">Warning</Button>
<Button variant="info">Info</Button>
<Button variant="destructive">Destructive</Button>
<Button variant="outline">Outline</Button>
<Button variant="secondary">Secondary</Button>
<Button variant="ghost">Ghost</Button>
<Button variant="link">Link</Button>`}
              >
                <div className="flex flex-wrap items-center gap-4">
                  <Button variant="default">Default</Button>
                  <Button variant="success">Success</Button>
                  <Button variant="warning">Warning</Button>
                  <Button variant="info">Info</Button>
                  <Button variant="destructive">Destructive</Button>
                  <Button variant="outline">Outline</Button>
                  <Button variant="secondary">Secondary</Button>
                  <Button variant="ghost">Ghost</Button>
                  <Button variant="link">Link</Button>
                </div>
              </ComponentPreview>

              {/* With Icons */}
              <ComponentPreview
                title="With Icons"
                description="Buttons with left and right positioned icons."
                code={`import { Save, Download, Settings } from 'lucide-react'

<Button leftIcon={<Save className="h-4 w-4" />}>
  Save Document
</Button>
<Button rightIcon={<Download className="h-4 w-4" />} variant="outline">
  Download
</Button>
<Button size="icon" variant="ghost">
  <Settings className="h-4 w-4" />
</Button>`}
              >
                <div className="flex flex-wrap items-center gap-4">
                  <Button leftIcon={<Save className="h-4 w-4" />}>Save Document</Button>
                  <Button rightIcon={<Download className="h-4 w-4" />} variant="outline">Download</Button>
                  <Button size="icon" variant="ghost">
                    <Settings className="h-4 w-4" />
                  </Button>
                </div>
              </ComponentPreview>

              {/* Loading States */}
              <ComponentPreview
                title="Loading State"
                description="Buttons with loading spinners and custom loading text."
                code={`<Button loading>Loading</Button>
<Button loading loadingText="Saving...">Save</Button>
<Button
  loading={isSubmitting}
  leftIcon={<Save className="h-4 w-4" />}
  loadingText="Processing..."
>
  Submit Form
</Button>`}
              >
                <div className="flex flex-wrap items-center gap-4">
                  <Button loading>Loading</Button>
                  <Button loading loadingText="Saving...">Save</Button>
                  <Button
                    loading={isLoading}
                    leftIcon={<Save className="h-4 w-4" />}
                    loadingText="Processing..."
                    onClick={handleLoadingDemo}
                  >
                    {isLoading ? 'Processing...' : 'Submit Form'}
                  </Button>
                </div>
              </ComponentPreview>

              {/* Gradient Variants */}
              <ComponentPreview
                title="Gradient Variants"
                description="Eye-catching gradient button styles."
                code={`<Button variant="gradient">Gradient</Button>
<Button variant="gradient-success">Success Gradient</Button>
<Button variant="gradient-warning">Warning Gradient</Button>
<Button variant="gradient-info">Info Gradient</Button>`}
              >
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <Button variant="gradient">Gradient</Button>
                  <Button variant="gradient-success">Success</Button>
                  <Button variant="gradient-warning">Warning</Button>
                  <Button variant="gradient-info">Info</Button>
                </div>
              </ComponentPreview>


            </div>
          </section>

          {/* ComboBox Component Showcase */}
          <section className="bg-white dark:bg-slate-800 rounded-xl p-8 shadow-lg">
            <div className="mb-8">
              <h2 className="text-3xl font-bold mb-3 text-slate-900 dark:text-slate-100">
                ComboBox Component
              </h2>
              <p className="text-lg text-slate-600 dark:text-slate-400">
                A searchable select component with rich options and grouping support.
              </p>
            </div>

            <div className="space-y-8">
              {/* Basic ComboBox */}
              <ComponentPreview
                title="Basic ComboBox"
                description="Simple dropdown with search functionality."
                code={`import { ComboBox } from '@/components/ui/combobox'

const [selectedValue, setSelectedValue] = useState('')

const options = [
  { value: 'option1', label: 'Option 1' },
  { value: 'option2', label: 'Option 2' },
  { value: 'option3', label: 'Option 3' },
]

<ComboBox
  options={options}
  value={selectedValue}
  onChange={setSelectedValue}
  placeholder="Select option..."
  clearable
/>`}
              >
                <div className="max-w-sm">
                  <ComboBox
                    options={simpleOptions}
                    value={selectedValueSmall}
                    onChange={setSelectedValueSmall}
                    placeholder="Select option..."
                    clearable
                  />
                  <p className="text-xs text-slate-500 mt-2">Selected: {selectedValueSmall || 'None'}</p>
                </div>
              </ComponentPreview>
              {/* Rich Options */}
              <ComponentPreview
                title="Rich Options with Icons"
                description="ComboBox with icons, descriptions, and rich content."
                code={`const richOptions = [
  {
    value: 'user1',
    label: 'John Doe',
    description: 'Software Engineer • San Francisco, CA',
    icon: <User className="h-4 w-4" />
  },
  {
    value: 'user2',
    label: 'Jane Smith',
    description: 'Product Manager • New York, NY',
    icon: <User className="h-4 w-4" />
  }
]

<ComboBox
  options={richOptions}
  value={selectedValue}
  onChange={setSelectedValue}
  placeholder="Select team member..."
  clearable
/>`}
              >
                <div className="max-w-sm">
                  <ComboBox
                    options={richOptions}
                    value={selectedValueRich}
                    onChange={setSelectedValueRich}
                    placeholder="Select team member..."
                    clearable
                  />
                  <p className="text-xs text-slate-500 mt-2">Selected: {selectedValueRich || 'None'}</p>
                </div>
              </ComponentPreview>

              {/* Sizes */}
              <ComponentPreview
                title="Sizes"
                description="Different ComboBox sizes for various use cases."
                code={`<ComboBox size="sm" placeholder="Small" />
<ComboBox size="md" placeholder="Medium (default)" />
<ComboBox size="lg" placeholder="Large" />`}
              >
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">Small</label>
                    <ComboBox
                      size="sm"
                      options={simpleOptions}
                      value={selectedValueSmall}
                      onChange={setSelectedValueSmall}
                      placeholder="Small size..."
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">Medium</label>
                    <ComboBox
                      size="md"
                      options={simpleOptions}
                      value={selectedValueMedium}
                      onChange={setSelectedValueMedium}
                      placeholder="Medium size..."
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">Large</label>
                    <ComboBox
                      size="lg"
                      options={simpleOptions}
                      value={selectedValueLarge}
                      onChange={setSelectedValueLarge}
                      placeholder="Large size..."
                    />
                  </div>
                </div>
              </ComponentPreview>

              {/* Feature Variants */}
              <div>
                <h3 className="text-lg font-medium mb-4 text-slate-700 dark:text-slate-300">Feature Variants</h3>
                <p className="text-sm text-slate-600 dark:text-slate-400 mb-4">
                  Different features and states - each maintains its own selection.
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">Clearable</label>
                    <ComboBox
                      options={simpleOptions}
                      value={selectedValueClearable}
                      onChange={setSelectedValueClearable}
                      placeholder="Clearable..."
                      clearable
                    />
                    <p className="text-xs text-slate-500 mt-1">Selected: {selectedValueClearable || 'None'}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">Disabled</label>
                    <ComboBox
                      options={simpleOptions}
                      value=""
                      onChange={() => {}}
                      placeholder="Disabled..."
                      disabled
                    />
                    <p className="text-xs text-slate-500 mt-1">State: Disabled</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">Loading</label>
                    <ComboBox
                      options={[]}
                      value=""
                      onChange={() => {}}
                      placeholder="Loading..."
                      loading
                    />
                    <p className="text-xs text-slate-500 mt-1">State: Loading</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">Rich Options</label>
                    <ComboBox
                      options={richOptions}
                      value={selectedValueRich}
                      onChange={setSelectedValueRich}
                      placeholder="Select user..."
                      clearable
                    />
                    <p className="text-xs text-slate-500 mt-1">Selected: {selectedValueRich || 'None'}</p>
                  </div>
                </div>
              </div>

              {/* Real-world Examples */}
              <div>
                <h3 className="text-lg font-medium mb-4 text-slate-700 dark:text-slate-300">Real-world Examples</h3>
                <p className="text-sm text-slate-600 dark:text-slate-400 mb-4">
                  Practical use cases with independent selections and rich data.
                </p>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">🌍 Location Selector</label>
                    <ComboBox
                      options={locationOptions}
                      value={selectedValueLocation}
                      onChange={setSelectedValueLocation}
                      placeholder="Select location..."
                      clearable
                      size="lg"
                    />
                    <p className="text-xs text-slate-500 mt-1">Selected: {selectedValueLocation || 'None'}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">📞 Contact Method</label>
                    <ComboBox
                      options={contactOptions}
                      value={selectedValueContact}
                      onChange={setSelectedValueContact}
                      placeholder="Select contact..."
                      clearable
                    />
                    <p className="text-xs text-slate-500 mt-1">Selected: {selectedValueContact || 'None'}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">👥 Team Members</label>
                    <ComboBox
                      options={richOptions}
                      value={selectedValueTeam}
                      onChange={setSelectedValueTeam}
                      placeholder="Select team member..."
                      clearable
                      searchPlaceholder="Search by name or location..."
                    />
                    <p className="text-xs text-slate-500 mt-1">Selected: {selectedValueTeam || 'None'}</p>
                  </div>
                </div>
              </div>

              {/* Grouped Options */}
              <ComponentPreview
                title="Grouped Options"
                description="Organize options into logical groups with headers."
                code={`const groupedOptions = [
  {
    label: "Actions",
    options: [
      { value: "save", label: "Save", icon: <Save /> },
      { value: "download", label: "Download", icon: <Download /> }
    ]
  },
  {
    label: "Settings",
    options: [
      { value: "profile", label: "Profile", icon: <User /> },
      { value: "preferences", label: "Preferences", icon: <Settings /> }
    ]
  }
]

<ComboBox
  groups={groupedOptions}
  value={selectedValue}
  onChange={setSelectedValue}
  placeholder="Select action..."
  clearable
/>`}
              >
                <div className="max-w-sm">
                  <ComboBox
                    groups={groupedOptions}
                    value={selectedGroupValue}
                    onChange={setSelectedGroupValue}
                    placeholder="Select action..."
                    clearable
                  />
                  <p className="text-xs text-slate-500 mt-2">Selected: {selectedGroupValue || 'None'}</p>
                </div>
              </ComponentPreview>

              {/* States */}
              <ComponentPreview
                title="States"
                description="Different ComboBox states including disabled and loading."
                code={`// Disabled state
<ComboBox disabled placeholder="Disabled" />

// Loading state
<ComboBox loading placeholder="Loading..." />

// Clearable
<ComboBox clearable placeholder="Clearable" />`}
              >
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">Disabled</label>
                    <ComboBox
                      options={simpleOptions}
                      value=""
                      onChange={() => {}}
                      placeholder="Disabled..."
                      disabled
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">Loading</label>
                    <ComboBox
                      options={[]}
                      value=""
                      onChange={() => {}}
                      placeholder="Loading..."
                      loading
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">Clearable</label>
                    <ComboBox
                      options={simpleOptions}
                      value={selectedValueClearable}
                      onChange={setSelectedValueClearable}
                      placeholder="Clearable..."
                      clearable
                    />
                  </div>
                </div>
              </ComponentPreview>
            </div>
          </section>

          {/* Toast Component Showcase */}
          <section className="bg-white dark:bg-slate-800 rounded-xl p-8 shadow-lg">
            <div className="mb-8">
              <h2 className="text-3xl font-bold mb-3 text-slate-900 dark:text-slate-100">
                Toast Component
              </h2>
              <p className="text-lg text-slate-600 dark:text-slate-400">
                Beautiful toast notifications with rich content and actions.
              </p>
            </div>

            <div className="space-y-8">
              {/* Basic Toasts */}
              <ComponentPreview
                title="Basic Toasts"
                description="Simple toast notifications with different variants."
                code={`import { toast } from '@/components/ui/sonner'

// Basic message
toast.message('This is a message')

// Success toast
toast.success('Operation completed successfully!')

// Error toast
toast.error('Something went wrong!')

// Warning toast
toast.warning('Please check your input!')

// Info toast
toast.info('New update available!')`}
              >
                <div className="grid grid-cols-2 md:grid-cols-5 gap-3">
                  <Button onClick={() => toast.message('📝 Basic message toast')} variant="outline" size="sm">
                    Message
                  </Button>
                  <Button onClick={() => toast.success('✅ Operation completed successfully!')} variant="success" size="sm">
                    Success
                  </Button>
                  <Button onClick={() => toast.error('❌ Something went wrong!')} variant="destructive" size="sm">
                    Error
                  </Button>
                  <Button onClick={() => toast.warning('⚠️ Please check your input!')} variant="warning" size="sm">
                    Warning
                  </Button>
                  <Button onClick={() => toast.info('ℹ️ New update available!')} variant="info" size="sm">
                    Info
                  </Button>
                </div>
              </ComponentPreview>

              {/* Rich Toasts with Actions */}
              <ComponentPreview
                title="Rich Toasts with Actions"
                description="Toast notifications with custom content and action buttons."
                code={`toast.success({
  title: "Payment Successful",
  description: "Your payment has been processed.",
  action: {
    label: "View Receipt",
    onClick: () => navigate('/receipt')
  },
  cancel: {
    label: "Dismiss",
    onClick: () => toast.dismiss()
  }
})`}
              >
                <div className="flex flex-wrap gap-4">
                  <Button onClick={showRichToastExamples} variant="gradient">
                    💳 Payment Toast
                  </Button>
                  <Button onClick={showEmailToast} variant="info">
                    📧 Email Toast
                  </Button>
                  <Button onClick={showCalendarToast} variant="warning">
                    📅 Calendar Toast
                  </Button>
                  <Button onClick={showLocationToast} variant="success">
                    📍 Location Toast
                  </Button>
                </div>
              </ComponentPreview>

              {/* Loading & Promise Toasts */}
              <ComponentPreview
                title="Loading & Promise Toasts"
                description="Dynamic toasts that update based on async operations."
                code={`// Loading toast
toast.loading('Uploading file...')

// Progress toast
const toastId = toast.loading('Uploading... 0%')
// Update progress
toast.loading('Uploading... 50%', { id: toastId })
// Complete
toast.success('Upload complete!', { id: toastId })

// Promise toast
const promise = fetch('/api/data')
toast.promise(promise, {
  loading: 'Loading data...',
  success: 'Data loaded successfully!',
  error: 'Failed to load data'
})`}
              >
                <div className="flex flex-wrap gap-4">
                  <Button onClick={showProgressToast} variant="info">
                    📤 Progress Toast
                  </Button>
                  <Button onClick={showPromiseToast} variant="outline">
                    🔄 Promise Toast
                  </Button>
                  <Button onClick={() => toast.loading('Loading operation...')} variant="secondary">
                    ⏳ Loading Toast
                  </Button>
                </div>
              </ComponentPreview>

            </div>
          </section>

          {/* Footer */}
          <div className="text-center py-8">
            <p className="text-slate-600 dark:text-slate-400">
              Enhanced shadcn components with comprehensive variants and advanced features.
            </p>
          </div>
        </div>
      </div>
    </AppLayout>
  )
}
