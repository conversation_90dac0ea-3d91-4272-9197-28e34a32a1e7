<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use App\Http\Controllers\Accounts\AccountTypeController;
use App\Http\Controllers\Accounts\AccountController;
use App\Http\Controllers\JournalEntry\JournalEntryController;
use App\Http\Controllers\ChequeEntry\ChequeController;
use Illuminate\Support\Facades\Auth;

Route::get('/', function () {
    return Inertia::render('home');
})->name('home');

// API endpoints moved to api.php. Only keep Inertia page route here.
Route::middleware(['auth', 'verified'])->group(function () {
    
    // API Documentation Route
    Route::get('/api-documentation', function () {
        return Inertia::render('ApiDocumentation');
    })->name('api.documentation');

    
    Route::get('dashboard', function () {
        return Inertia::render('dashboard');
    })->name('dashboard');
    
    // Accounts list route (fixed to use accounts/index)
    Route::get('accounts', function () {
        return Inertia::render('accounts/index');
    })->name('accounts.index');

    // Journal Entry (Single)
    Route::get('journal-entry', function () {
        $user = Auth::user();
        if (!$user || !in_array($user->role, ['Admin', 'Editor'])) {
            abort(403, 'Unauthorized');
        }
        return Inertia::render('journalEntry/single');
    })->name('journal.entry');

    // Bank Payment Entry
    Route::get('bank-payment', function () {
        $user = Auth::user();
        if (!$user || !in_array($user->role, ['Admin', 'Editor'])) {
            abort(403, 'Unauthorized');
        }
        return Inertia::render('journalEntry/bank-payment-entry');
    })->name('journal.bank.payment');

    // Bank Receive Entry
    Route::get('bank-receive', function () {
        $user = Auth::user();
        if (!$user || !in_array($user->role, ['Admin', 'Editor'])) {
            abort(403, 'Unauthorized');
        }
        return Inertia::render('journalEntry/bank-receive-entry');
    })->name('journal.bank.receive');

    // Cash Payment Entry
    Route::get('cash-payment', function () {
        $user = Auth::user();
        if (!$user || !in_array($user->role, ['Admin', 'Editor'])) {
            abort(403, 'Unauthorized');
        }
        return Inertia::render('journalEntry/cash-payment-entry');
    })->name('journal.cash.payment');

    // Cash Receive Entry
    Route::get('cash-receive', function () {
        $user = Auth::user();
        if (!$user || !in_array($user->role, ['Admin', 'Editor'])) {
            abort(403, 'Unauthorized');
        }
        return Inertia::render('journalEntry/cash-receive-entry');
    })->name('journal.cash.receive');
    
    // Journal Multiple Entry
    Route::get('journal-multiple-entry', function () {
        $user = Auth::user();
        if (!$user || !in_array($user->role, ['Admin', 'Editor'])) {
            abort(403, 'Unauthorized');
        }
        return Inertia::render('journalEntry/multiple');
    })->name('journal.multiple.entry');

    // Journal Entry Edit
    Route::get('/journal-entry/edit/{id}', function ($id) {
        $user = Auth::user();
        if (!$user || !in_array($user->role, ['Admin', 'Editor'])) {
            abort(403, 'Unauthorized');
        }
        return Inertia::render('journalEntry/EditJournalEntry', ['entryId' => $id]);
    })->name('journal-entry.edit');

    // Daily Book Report
    Route::get('daily-book', function () {
        $user = Auth::user();
        if (!$user || !in_array($user->role, ['Admin', 'Editor'])) {
            abort(403, 'Unauthorized');
        }
        return Inertia::render('Reports/DailyBook');
    })->name('reports.daily-book');

    // Ledgers Report
    Route::get('/ledgers-pkr', function () {
        $user = Auth::user();
        if (!$user || !in_array($user->role, ['Admin', 'Editor'])) {
            abort(403, 'Unauthorized');
        }
        return Inertia::render('Reports/LedgersPkr');
    })->name('reports.ledgers-pkr');

    // Route for Ledger PKR PDF Generation
    Route::get('/ledgers-pkr/pdf', function(Request $request) {
        $user = Auth::user();
        if (!$user || !in_array($user->role, ['Admin', 'Editor'])) {
            abort(403, 'Unauthorized');
        }
        // Manually instantiate or resolve the controller and call the method
        return app(JournalEntryController::class)->generateLedgerPdf($request);
    })->name('ledgers.pkr.pdf');

    // Route for Ledger PKR Print View
    Route::get('/ledgers-pkr/print', function(Request $request) {
        $user = Auth::user();
        if (!$user || !in_array($user->role, ['Admin', 'Editor'])) {
            abort(403, 'Unauthorized');
        }
        return app(JournalEntryController::class)->generateLedgerPrint($request);
    })->name('ledgers.pkr.print');

    // Cheque Entry Inertia page routes
    Route::get('cheque-entry', function () {
        $user = Auth::user();
        if (!$user || !in_array($user->role, ['Admin', 'Editor'])) {
            abort(403, 'Unauthorized');
        }
        return Inertia::render('cheque/Entry');
    })->name('cheque.entry');

    // Cheque List Page
    Route::get('cheque-list', function (Request $request) { // Inject Request here
        $user = Auth::user();
        if (!$user || !in_array($user->role, ['Admin', 'Editor'])) {
            abort(403, 'Unauthorized');
        }
        return Inertia::render('cheque/List');
    })->name('cheque.list');

    // Cheque Edit Page
    // Multiple Cheque Entries
    Route::get('cheque-multiple', function () {
        $user = Auth::user();
        if (!$user || !in_array($user->role, ['Admin', 'Editor'])) {
            abort(403, 'Unauthorized');
        }
        return Inertia::render('cheque/multiple');
    })->name('cheque.multiple');

    Route::get('cheque/edit/{chequeId}', function ($chequeId) {
        $user = Auth::user();
        if (!$user || !in_array($user->role, ['Admin', 'Editor'])) {
            abort(403, 'Unauthorized');
        }
        return Inertia::render('cheque/Edit', ['chequeId' => $chequeId]);
    })->name('cheque.edit');

    // Purchase Entry
    Route::get('purchase-entry', function () {
        $user = Auth::user();
        if (!$user || !in_array($user->role, ['Admin', 'Editor'])) {
            abort(403, 'Unauthorized');
        }
        return Inertia::render('Transaction/Purchase');
    })->name('purchase.create');

    // Purchase List
    Route::get('purchase-list', function () {
        $user = Auth::user();
        if (!$user || !in_array($user->role, ['Admin', 'Editor'])) {
            abort(403, 'Unauthorized');
        }
        return Inertia::render('Transaction/PurchaseList');
    })->name('purchase.list');
    

    // Purchase Edit
    Route::get('purchase-entry/edit/{purchaseId}', function ($purchaseId) {
        $user = Auth::user();
        if (!$user || !in_array($user->role, ['Admin', 'Editor'])) {
            abort(403, 'Unauthorized');
        }
        return Inertia::render('Transaction/EditPurchase', ['purchaseId' => $purchaseId]);
    })->name('purchase.edit');

    // Sale Entry
    Route::get('sale-entry', function () {
        $user = Auth::user();
        if (!$user || !in_array($user->role, ['Admin', 'Editor'])) {
            abort(403, 'Unauthorized');
        }
        return Inertia::render('Transaction/Sale');
    })->name('sale.create');

    // Sale Edit
    Route::get('sale-entry/edit/{saleId}', function ($saleId) {
        $user = Auth::user();
        if (!$user || !in_array($user->role, ['Admin', 'Editor'])) {
            abort(403, 'Unauthorized');
        }
        return Inertia::render('Transaction/EditSale', ['saleId' => $saleId]);
    })->name('sale.edit');

    // Sale List
    Route::get('sale-list', function () {
        $user = Auth::user();
        if (!$user || !in_array($user->role, ['Admin', 'Editor'])) {
            abort(403, 'Unauthorized');
        }
        return Inertia::render('Transaction/SaleList');
    })->name('sale.list');
    

    // Balance Sheet Report
    Route::get('/balance-sheet', function () {
        return Inertia::render('Reports/BalanceSheet');
    })->name('reports.balance-sheet');
    
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
