<?php

namespace App\Http\Controllers\Transaction;

use App\Http\Controllers\Controller;
use App\Models\Account;
use App\Models\Currency; // Not directly used in this version but good to have
use App\Models\JournalEntry;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str; // For TID generation if needed
use App\Jobs\ProcessInventoryRecalculationJob;
use App\Models\TransactionAuditLog;
use Illuminate\Support\Facades\Log;

class PurchaseController extends Controller
{
    // Helper: Generate a unique Transaction ID (TID) for linking journal entries
    private function generateNewTid(int $paymentTypeId): string
    {
        // Using the same approach as JournalEntryController's generateTransactionNumber
        $lastEntry = JournalEntry::lockForUpdate()
            ->latest('TID') // Assuming TID is structured to be sortable like this
            ->first();

        $lastSequence = 0;
        if ($lastEntry) {
            $lastTID = $lastEntry->TID;
            // Extract numeric sequence from last TID, assuming format YYMMSEQ (e.g., 23120001)
            // This needs to be robust. If TID format is different, this logic must change.
            // Current logic: TID = date (ym) + sequence (4 digits)
            if (strlen($lastTID) >= 6) { // ymXXXX = 6 chars
                // Check if the first 2 chars are numeric (year part of date)
                if(is_numeric(substr($lastTID, 0, 2))) {
                    $lastSequence = (int)substr($lastTID, 4); // Get sequence part if TID is like YYMMNNNN
                } else {
                    // Fallback or different logic if TID format is not YYMM based
                    // For now, assume it's just a sequence if not YYMM based
                    $lastSequence = (int)$lastTID; 
                }
            } else {
                 $lastSequence = (int)$lastTID; // If TID is shorter, assume it's just a sequence.
            }
        }

        $newSequence = $lastSequence + 1;
        $datePrefix = now()->format('ym');
        $tid = $datePrefix . str_pad($newSequence, 4, '0', STR_PAD_LEFT);

        // Ensure the TID is unique for the given payment type
        while (JournalEntry::where('TID', $tid)->where('payment_type_id', $paymentTypeId)->exists()) {
            $newSequence++;
            $tid = $datePrefix . str_pad($newSequence, 4, '0', STR_PAD_LEFT);
        }

        return $tid;
    }

    // Helper: Generate Purchase Order Number (P-NO)
    private function generatePurchaseOrderNumber(Account $currencyAccount): string
    {
        $currencyCode = $currencyAccount->currency->code ?? 'GEN'; // Fallback generic code
        $prefix = "P-" . strtoupper($currencyCode);

        // Find the last P-NO for this currency
        $latestEntry = JournalEntry::where('inv_no', 'LIKE', $prefix . '%')
            ->where('payment_type_id', 10) // Assuming 10 is Purchase
            ->orderByRaw('CAST(SUBSTRING(inv_no, LENGTH(?) + 1) AS UNSIGNED) DESC', [$prefix]) // Order by numeric part
            ->first();
            
        $sequence = 1;
        if ($latestEntry) {
            $lastNumPart = str_replace($prefix, '', $latestEntry->inv_no);
            if (is_numeric($lastNumPart)) {
                $sequence = (int)$lastNumPart + 1;
            }
            // If not numeric (e.g. P-USDXYZ1), this basic sequence might need adjustment or a different strategy
        }
        return $prefix . $sequence;
    }


    // Helper: Get financial summary for a currency account
    private function getAccountFinancialSummary(Account $account): array
    {
        $rawEntries = JournalEntry::where('account_id', $account->id)
            ->orderBy('date', 'asc')
            ->orderBy('id', 'asc')
            ->get();

        $totalCurrencyBalance = 0;
        $totalPkrBalance = 0;

        foreach ($rawEntries as $entry) {
            $currencyAmountForEntry = $entry->currency_amount ?? 0; // Using updated column name
            
            if ($entry->is_credit) { // Outflow from this account
                $totalCurrencyBalance -= $currencyAmountForEntry;
            } else { // Inflow to this account
                $totalCurrencyBalance += $currencyAmountForEntry;
            }
            $totalPkrBalance += $entry->amount; // amount is already signed PKR
        }
        
        $averageRate = 0;
        if ($account->formula === 'd') { // Divide: Currency / PKR
            if ($totalPkrBalance != 0) {
                $averageRate = $totalCurrencyBalance / $totalPkrBalance;
            }
        } else { // Multiply (default): PKR / Currency
            if ($totalCurrencyBalance != 0) {
                $averageRate = $totalPkrBalance / $totalCurrencyBalance;
            }
        }

        return [
            'total_currency_balance' => $totalCurrencyBalance,
            'total_pkr_balance' => $totalPkrBalance,
            'current_average_rate' => round($averageRate, 2),
            'formula_type' => $account->formula,
        ];
    }
    
    // Helper: Calculate and set running balances for a journal entry
    private function calculateAndSetRunningBalances(JournalEntry $journalEntry): void
    {
        $latestPrevJe = JournalEntry::where('account_id', $journalEntry->account_id)
            ->where(function ($query) use ($journalEntry) {
                $query->where('date', '<', $journalEntry->date)
                      ->orWhere(function ($query) use ($journalEntry) {
                          $query->where('date', '=', $journalEntry->date)
                                ->where('id', '<', $journalEntry->id);
                      });
            })
            ->orderBy('date', 'desc')
            ->orderBy('id', 'desc')
            ->first();

        $prevRunningCurrency = $latestPrevJe ? $latestPrevJe->running_currency_balance : 0; // updated column name
        $prevRunningPkr = $latestPrevJe ? $latestPrevJe->running_pkr_balance : 0;          // updated column name

        $transactionCurrencyAmount = $journalEntry->currency_amount ?? 0; // updated column name
        $transactionPkrAmount = $journalEntry->amount; // This is already signed

        if ($journalEntry->is_credit) { // Outflow from this account
            $journalEntry->running_currency_balance = $prevRunningCurrency - $transactionCurrencyAmount;
        } else { // Inflow to this account
            $journalEntry->running_currency_balance = $prevRunningCurrency + $transactionCurrencyAmount;
        }
        $journalEntry->running_pkr_balance = $prevRunningPkr + $transactionPkrAmount;
    }

    // New method for GET /api/purchase-entries/get-details
    public function getPurchaseEntryDetails(Request $request)
    {
        $request->validate(['currency_account_id' => 'required|exists:accounts,id']);
        $currencyAccount = Account::with('currency')->find($request->currency_account_id);

        if (!$currencyAccount || !$currencyAccount->currency_id) {
            return response()->json(['error' => 'Selected account is not a valid currency account or currency relationship is missing.'], 422);
        }

        $pNo = $this->generatePurchaseOrderNumber($currencyAccount);
        $summary = $this->getAccountFinancialSummary($currencyAccount);

        return response()->json([
            'transaction_number' => $pNo, // P-NO
            'currency_balance' => $summary['total_currency_balance'],
            'pkr_balance' => $summary['total_pkr_balance'],
            'average_rate' => $summary['current_average_rate'],
            'formula_type' => $currencyAccount->formula,
        ]);
    }

    public function index(Request $request)
    {
        $query = JournalEntry::query()
            ->select(
                'je_debit.id as debit_id', // Unique key for React if inv_no repeats in some edge case (should not for purchases)
                'je_debit.inv_no as order_id', // This is our P-NO, used as the main ID for the list
                'je_debit.date as date',
                'je_debit.description as debit_description', // To parse ref_no from
                'je_debit.currency_amount as currency_amount',
                'je_debit.exchange_rate as rate',
                DB::raw('ABS(je_debit.amount) as pkr_amount'), // amount on debit is positive
                'currency_account.name as currency_account_name',
                'customer_account.name as customer_account_name',
                'currencies.code as currency_code'
            )
            ->from('journal_entries as je_debit')
            ->join('accounts as currency_account', 'je_debit.account_id', '=', 'currency_account.id')
            ->leftJoin('currencies', 'currency_account.currency_id', '=', 'currencies.id')
            ->join('journal_entries as je_credit', function ($join) {
                $join->on('je_debit.inv_no', '=', 'je_credit.inv_no')
                     ->on('je_debit.TID', '=', 'je_credit.TID') // Ensure they are part of the same original transaction
                     ->where('je_credit.is_credit', '=', 1)
                     ->where('je_credit.payment_type_id', '=', 10); // Credit leg is also purchase
            })
            ->join('accounts as customer_account', 'je_credit.account_id', '=', 'customer_account.id')
            ->where('je_debit.is_credit', 0)
            ->where('je_debit.payment_type_id', 10); // Debit leg is purchase

        // Date From Filter
        if ($request->filled('date_from')) {
            $query->where('je_debit.date', '>=', $request->date_from);
        }

        // Date To Filter
        if ($request->filled('date_to')) {
            $query->where('je_debit.date', '<=', $request->date_to);
        }

        // Customer Account Filter
        if ($request->filled('customer_account_id')) {
            $query->where('je_credit.account_id', $request->customer_account_id);
        }

        // Currency Account Filter
        if ($request->filled('currency_account_id')) {
            $query->where('je_debit.account_id', $request->currency_account_id);
        }
        
        // Group by to ensure one row per purchase order (P-NO)
        // All selected fields must be in group by or aggregate functions if they differ between debit/credit raw entries (they shouldn't with this join structure)
        $query->groupBy(
            'je_debit.id',
            'je_debit.inv_no',
            'je_debit.date',
            'je_debit.description',
            'je_debit.currency_amount',
            'je_debit.exchange_rate',
            'je_debit.amount', // pkr_amount is derived from this
            'currency_account.name',
            'customer_account.name',
            'currencies.code'
        );

        // Sorting
        $sortBy = $request->input('sort_by', 'je_debit.date'); // Default sort
        $sortDirection = $request->input('sort_direction', 'desc');

        // Validate sort_by to prevent SQL injection and ensure it's a valid column
        $validSortColumns = [
            'date' => 'je_debit.date',
            'order_id' => 'je_debit.inv_no',
            'currency_amount' => 'je_debit.currency_amount',
            'rate' => 'je_debit.exchange_rate',
            'pkr_amount' => DB::raw('ABS(je_debit.amount)') // or just 'je_debit.amount' if always positive
            // Add other frontend accessorKeys if needed
        ];

        if (array_key_exists($sortBy, $validSortColumns)) {
            $query->orderBy($validSortColumns[$sortBy], $sortDirection);
        } else {
            $query->orderBy('je_debit.date', 'desc'); // Fallback default sort
        }
        
        $perPage = $request->input('per_page', 15);
        $purchases = $query->paginate($perPage);

        // Transform data to include ref_no and match frontend structure
        $purchases->getCollection()->transform(function ($purchase) {
            $refNo = '';
            if ($purchase->debit_description) {
                $parts = explode(' | ', $purchase->debit_description);
                foreach ($parts as $part) {
                    if (Str::startsWith($part, 'Ref: ')) {
                        $refNo = Str::after($part, 'Ref: ');
                        break;
                    }
                }
            }
            return [
                'id' => $purchase->order_id, // Use order_id (P-NO) as the unique ID for the row as expected by frontend edit link
                'date' => $purchase->date->format('Y-m-d'),
                'ref_no' => $refNo,
                'order_id' => $purchase->order_id,
                'customer_account_name' => $purchase->customer_account_name,
                'currency_account_name' => $purchase->currency_account_name,
                'currency_code' => $purchase->currency_code,
                'currency_amount' => (float)$purchase->currency_amount,
                'rate' => (float)$purchase->rate,
                'pkr_amount' => (float)$purchase->pkr_amount,
            ];
        }); 

        return response()->json($purchases);
    }

    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'date' => 'required|date_format:Y-m-d',
            'selectDr' => 'required|exists:accounts,id', // Currency Account ID
            'selectCr' => 'required|exists:accounts,id', // Customer Account ID
            'currency_amount' => 'required|numeric|gt:0',
            'rate' => 'required|numeric|gt:0',
            'pkr_amount' => 'required|numeric', 
            'description' => 'nullable|string|max:255',
            'refno' => 'nullable|string|max:100',
            'orderid' => 'required|string|max:100', // P-NO
            'is_debit' => 'required|in:0,1', // For BANAM entry
        ]);

        $currencyAccount = Account::with('currency')->find($validatedData['selectDr']);
        $customerAccount = Account::find($validatedData['selectCr']);

        if (!$currencyAccount || !$currencyAccount->currency_id) {
            return response()->json(['error' => 'Debit account must be a currency account.'], 422);
        }
        if ($customerAccount && $customerAccount->currency_id) {
            return response()->json(['error' => 'Credit account must be a PKR account.'], 422);
        }
        
        // Ensure formula is lowercase for comparison
        $formula = strtolower($currencyAccount->formula);
        $calculatedPkr = $formula === 'd' 
            ? round($validatedData['currency_amount'] / $validatedData['rate'], 2)
            : round($validatedData['currency_amount'] * $validatedData['rate'], 2);
        
        if (abs($calculatedPkr - round($validatedData['pkr_amount'], 2)) > 0.01) {
            return response()->json(['error' => 'PKR amount calculation mismatch. Expected: ' . $calculatedPkr . ' Received: ' . $validatedData['pkr_amount']], 422);
        }

        DB::beginTransaction();
        try {
            $tid = $this->generateNewTid(10);
            $userId = Auth::id();
            $paymentTypeId = 10; // Hardcoded: 10 for Purchase

            $financialSummaryBeforeTx = $this->getAccountFinancialSummary($currencyAccount);
            $avgRateBeforeTx = $financialSummaryBeforeTx['current_average_rate'];

            $currencyCode = $currencyAccount->currency->code ?? 'CUR';
            $baseNarration = "Purchase From {$customerAccount->name} {$currencyCode}{$validatedData['currency_amount']}@{$validatedData['rate']}";
            $fullNarration = $baseNarration;
            if ($validatedData['refno']) $fullNarration .= " | Ref: {$validatedData['refno']}";
            if ($validatedData['description']) $fullNarration .= " | {$validatedData['description']}";
            
            // Debit Journal Entry (Currency Account)
            $debitJe = new JournalEntry([
                'TID' => $tid,
                'date' => $validatedData['date'],
                'payment_type_id' => $paymentTypeId,
                'account_id' => $currencyAccount->id,
                'is_credit' => 0, // Debit
                'description' => $fullNarration,
                'inv_no' => $validatedData['orderid'], // P-NO
                'currency_amount' => $validatedData['currency_amount'], // updated column name
                'amount' => $calculatedPkr, // Use calculated PKR, positive
                'exchange_rate' => round($validatedData['rate'], 2),             // updated column name
                'formula_type' => $currencyAccount->formula,         // updated column name
                'avg_rate' => round($avgRateBeforeTx, 2),                      // updated column name
                'created_by' => $userId,
            ]);
            $debitJe->save(); 
            $this->calculateAndSetRunningBalances($debitJe);
            $debitJe->save();

            // Credit Journal Entry (Customer Account)
            $creditJe = new JournalEntry([
                'TID' => $tid,
                'date' => $validatedData['date'],
                'payment_type_id' => $paymentTypeId,
                'account_id' => $customerAccount->id,
                'is_credit' => 1, // Credit
                'description' => $fullNarration, // Same narration for both legs
                'inv_no' => $validatedData['orderid'],
                'amount' => -$calculatedPkr, // Negative PKR amount
                'exchange_rate' => round($validatedData['rate'], 2), // Store for reference
                'formula_type' => $currencyAccount->formula, // Store for reference
                'avg_rate' => round($avgRateBeforeTx, 2), // Store for reference
                'currency_amount' => null, // Not applicable for PKR leg
                'running_currency_balance' => null, 
                'running_pkr_balance' => null, // Or calculate if this account also needs PKR running balance
                'created_by' => $userId,
            ]);
            $creditJe->save();
            // If customerAccount also needs running balances:
            // $this->calculateAndSetRunningBalances($creditJe); $creditJe->save(); // For now Not needed

            if ($validatedData['is_debit'] === "1") {
                // BANAM Entry - Future
                // ExchangeEntry::create([...]);
            }

            DB::commit();

            // Log the creation in audit trail
            TransactionAuditLog::logTransactionChange(
                'purchase',
                $tid,
                $validatedData['orderid'],
                'created',
                [],
                [
                    'date' => $validatedData['date'],
                    'currency_account_id' => $validatedData['selectDr'],
                    'customer_account_id' => $validatedData['selectCr'],
                    'currency_amount' => $validatedData['currency_amount'],
                    'rate' => $validatedData['rate'],
                    'pkr_amount' => $calculatedPkr,
                    'description' => $validatedData['description'],
                    'refno' => $validatedData['refno']
                ],
                Auth::id(),
                "New purchase transaction created"
            );

            // --- Dispatch Job AFTER commit ---
            $currencyAccountId = (int)$validatedData['selectDr'];
            $transactionDate = $validatedData['date'];
            $this->logRecalculationRequest($currencyAccountId, $transactionDate, 'purchase_create', "Purchase P-NO: {$validatedData['orderid']}");
            ProcessInventoryRecalculationJob::dispatch($currencyAccountId, $transactionDate, $tid)
                ->onQueue('inventory')
                ->delay(now()->addSeconds(2)); // Small delay to ensure transaction is committed
            Log::info("Dispatched recalculation job for new Purchase P-NO: {$validatedData['orderid']}");

            return response()->json(['message' => 'Purchase transaction recorded. Balance recalculation is processing in the background.', 'TID' => $tid, 'P_NO' => $validatedData['orderid']], 201);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['error' => 'Failed to record purchase transaction.', 'details' => $e->getMessage()], 500);
        }
    }

    // Show purchase entry for editing. $id is P-NO (inv_no)
    public function show($id)
    {
        $journalEntries = JournalEntry::with(['account', 'account.currency', 'createdBy'])
            ->where('inv_no', $id)
            ->where('payment_type_id', 10) // Purchase
            ->orderBy('is_credit', 'asc') // Debit first
            ->get();

        if ($journalEntries->count() < 1) { // Can be 1 if only DR leg exists due to some issue, or 2
            return response()->json(['error' => 'Purchase transaction not found or incomplete.'], 404);
        }

        $debitEntry = $journalEntries->firstWhere('is_credit', 0);
        $creditEntry = $journalEntries->firstWhere('is_credit', 1); // Might be null if only debit exists
        
        if(!$debitEntry) {
             return response()->json(['error' => 'Could not find the debit part of the purchase transaction.'], 404);
        }
        
        // Attempt to parse original description elements
        $parsedDescription = $debitEntry->description;
        $originalDesc = '';
        $refNoFromDesc = '';

        // Example parsing (can be made more robust)
        $parts = explode(' | ', $parsedDescription);
        // Assuming "Purchase From CUST CURR_AMOUNT@RATE" is the first part
        if (count($parts) > 0) {
            // Basic heuristic: the part before "Ref:" and after "RATE"
            $rateMarker = '@' . number_format($debitEntry->exchange_rate, 2); // or 5 for more precision
            $baseNarrationEndPos = strpos($parts[0], $rateMarker);
            if ($baseNarrationEndPos !== false) {
                 // Not strictly needed to send back, form has its own description
            }
        }
        foreach ($parts as $part) {
            if (Str::startsWith($part, 'Ref: ')) {
                $refNoFromDesc = Str::after($part, 'Ref: ');
            } elseif (!Str::startsWith($part, 'Purchase From') && !Str::startsWith($part, 'Ref: ')) {
                $originalDesc = $part; // This assumes description is the last non-standard part
            }
        }


        return response()->json([
            'date' => $debitEntry->date->format('Y-m-d'),
            'selectDr' => $debitEntry->account_id,
            'selectCr' => $creditEntry ? $creditEntry->account_id : null,
            'currency_amount' => $debitEntry->currency_amount,
            'rate' => round($debitEntry->exchange_rate, 2),
            'pkr_amount' => abs($debitEntry->amount), 
            'description' => $originalDesc, // Parsed original description
            'refno' => $refNoFromDesc, // Parsed reference number
            'orderid' => $debitEntry->inv_no, // P-NO
            'original_purchase_rate' => round($debitEntry->exchange_rate, 2), // For display in edit mode alert
             // 'is_debit' - this is tricky. If BANAM entry was created, it should be "1".
             // This might require checking the exchange_entries table for $id (P-NO)
        ]);
    }

    public function update(Request $request, $id) // $id is P-NO (inv_no)
    {
        $validatedData = $request->validate([
            'date' => 'required|date_format:Y-m-d',
            'selectDr' => 'required|exists:accounts,id',
            'selectCr' => 'required|exists:accounts,id',
            'currency_amount' => 'required|numeric|gt:0',
            'rate' => 'required|numeric|gt:0',
            'pkr_amount' => 'required|numeric',
            'description' => 'nullable|string|max:255',
            'refno' => 'nullable|string|max:100',
            // 'is_debit' might be part of the update if it controls BANAM logic
        ]);
        
        $currencyAccount = Account::with('currency')->find($validatedData['selectDr']);
        $customerAccount = Account::find($validatedData['selectCr']);

        if (!$currencyAccount || !$currencyAccount->currency_id) {
            return response()->json(['error' => 'Debit account must be a currency account.'], 422);
        }
        if ($customerAccount && $customerAccount->currency_id) {
            return response()->json(['error' => 'Credit account must be a PKR account.'], 422);
        }

        // Ensure formula is lowercase for comparison
        $formula = strtolower($currencyAccount->formula);
        $calculatedPkr = $formula === 'd' 
            ? round($validatedData['currency_amount'] / round($validatedData['rate'], 2), 2)
            : round($validatedData['currency_amount'] * round($validatedData['rate'], 2), 2);
        
        if (abs($calculatedPkr - round($validatedData['pkr_amount'],2)) > 0.01) {
            return response()->json(['error' => 'PKR amount calculation mismatch on update.'], 422);
        }

        DB::beginTransaction();
        try {
            // IMPORTANT: Updating financial transactions is complex.
            // True "update" that changes amounts/accounts requires recalculating all subsequent running balances.
            // A common pattern is to reverse the old transaction and create a new one.
            // This implementation attempts a direct update and re-calculates only for the current entry.
            // A full recalculation of subsequent entries is NOT implemented here.

            $journalEntries = JournalEntry::where('inv_no', $id) // $id is P-NO
                                        ->where('payment_type_id', 10) // Purchase
                                        ->orderBy('is_credit', 'asc')
                                        ->get();

            if ($journalEntries->count() < 1) {
                DB::rollBack();
                return response()->json(['error' => 'Purchase transaction not found or incomplete for update.'], 404);
            }

            $debitJe = $journalEntries->firstWhere('is_credit', 0);
            $creditJe = $journalEntries->firstWhere('is_credit', 1); // Could be null if it was missing

            if(!$debitJe) {
                DB::rollBack();
                 return response()->json(['error' => 'Could not find the debit part of the purchase transaction for update.'], 404);
            }
            
            $originalDate = $debitJe->date->format('Y-m-d');
            $originalAccountId = $debitJe->account_id;
            $originalCurrencyAmount = $debitJe->currency_amount;
            $originalRate = $debitJe->exchange_rate;

            $currencyCode = $currencyAccount->currency->code ?? 'CUR';
            $baseNarration = "Purchase From {$customerAccount->name} {$currencyCode}{$validatedData['currency_amount']}@{$validatedData['rate']}";
            $fullNarration = $baseNarration;
            if ($validatedData['refno']) $fullNarration .= " | Ref: {$validatedData['refno']}";
            if ($validatedData['description']) $fullNarration .= " | {$validatedData['description']}";
            
            // Update Debit Journal Entry
            $debitJe->fill([
                'date' => $validatedData['date'],
                'account_id' => $currencyAccount->id,
                'description' => $fullNarration,
                'currency_amount' => $validatedData['currency_amount'],
                'amount' => $calculatedPkr,
                'exchange_rate' => round($validatedData['rate'], 2),
                'formula_type' => $currencyAccount->formula,
                // avg_rate ideally should be the one at the time of this transaction.
                // Re-fetching it here might be complex if other tx happened between original and edit.
                // For simplicity, we could keep the original avg_rate or re-calculate based on state *before this specific JE*.
                // For now, let's assume it's not re-calculated here, or it's the original from initial store.
            ]);
            // Recalculate running balance for THIS entry.
            // If date or account_id changed, this simple recalculation is insufficient for overall accuracy.
            $this->calculateAndSetRunningBalances($debitJe); 
            $debitJe->save();

            // Update or Create Credit Journal Entry
            if ($creditJe) {
                $creditJe->fill([
                    'date' => $validatedData['date'],
                    'account_id' => $customerAccount->id,
                    'description' => $fullNarration,
                    'amount' => -$calculatedPkr,
                    'exchange_rate' => round($validatedData['rate'], 2),
                    'formula_type' => $currencyAccount->formula,
                ]);
                 // $this->calculateAndSetRunningBalances($creditJe); // if customer PKR account needs it
                $creditJe->save();
            } else { // If credit entry was missing, create it
                $creditJe = new JournalEntry([
                    'TID' => $debitJe->TID, // Use same TID
                    'date' => $validatedData['date'],
                    'payment_type_id' => 10,
                    'account_id' => $customerAccount->id,
                    'is_credit' => 1,
                    'description' => $fullNarration,
                    'inv_no' => $id, // P-NO
                    'amount' => -$calculatedPkr,
                    'exchange_rate' => round($validatedData['rate'], 2),
                    'formula_type' => $currencyAccount->formula,
                    'created_by' => Auth::id(), // Assuming it was created now
                ]);
                $creditJe->save();
            }
            
            // TODO: Implement robust recalculation for ALL SUBSEQUENT journal entries' running balances
            // if key financial data (accounts, amounts, date) has changed for the debit account.
            // This is critical for financial integrity.
            // After the update is saved to DB, determine if a significant change occurred 
            // that requires recalculation of all subsequent entries
            $newDate = $validatedData['date'];
            $newAccountId = (int)$validatedData['selectDr'];
            $newCurrencyAmount = (float)$validatedData['currency_amount'];
            $newRate = (float)$validatedData['rate'];

            $significantChange = $originalDate !== $newDate ||
                                 $originalAccountId !== $newAccountId ||
                                 abs((float)$originalCurrencyAmount - $newCurrencyAmount) > 0.001 ||
                                 abs((float)$originalRate - $newRate) > 0.001;

            // Log the update in audit trail
            TransactionAuditLog::logTransactionChange(
                'purchase',
                $debitJe->TID,
                $id,
                'updated',
                [
                    'date' => $originalDate,
                    'currency_account_id' => $originalAccountId,
                    'currency_amount' => $originalCurrencyAmount,
                    'rate' => $originalRate
                ],
                [
                    'date' => $validatedData['date'],
                    'currency_account_id' => $validatedData['selectDr'],
                    'customer_account_id' => $validatedData['selectCr'],
                    'currency_amount' => $validatedData['currency_amount'],
                    'rate' => $validatedData['rate'],
                    'pkr_amount' => $calculatedPkr,
                    'description' => $validatedData['description'],
                    'refno' => $validatedData['refno']
                ],
                Auth::id(),
                $significantChange ? "Purchase updated with significant changes requiring recalculation" : "Purchase updated with minor changes"
            );

            // Commit the purchase transaction update now
            DB::commit();

            // If there was a significant change, dispatch background recalculation
            if ($significantChange) {
                // Use the earliest date to ensure all affected transactions are updated
                $earliestAffectedDate = min($originalDate, $newDate);

                // Log the recalculation request
                $this->logRecalculationRequest($newAccountId, $earliestAffectedDate, 'purchase_update', "Purchase P-NO: {$id}");

                // Dispatch background job for recalculation
                ProcessInventoryRecalculationJob::dispatch($newAccountId, $earliestAffectedDate, $id)
                    ->onQueue('inventory')
                    ->delay(now()->addSeconds(2)); // Small delay to ensure transaction is committed

                // If account ID changed, recalculate for both old and new accounts
                if ($originalAccountId !== $newAccountId) {
                    $this->logRecalculationRequest($originalAccountId, $earliestAffectedDate, 'purchase_update', "Purchase P-NO: {$id} (old account)");
                    ProcessInventoryRecalculationJob::dispatch($originalAccountId, $earliestAffectedDate, $id)
                        ->onQueue('inventory')
                        ->delay(now()->addSeconds(3));
                }

                Log::info("Dispatched background recalculation jobs for Purchase update P-NO: {$id}");

                return response()->json([
                    'message' => 'Purchase transaction updated successfully. Balance recalculation is processing in the background.',
                    'recalculation_status' => 'processing'
                ]);
            }

            return response()->json([
                'message' => 'Purchase transaction updated successfully.',
                'recalculation_status' => 'not_required'
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['error' => 'Failed to update purchase transaction.', 'details' => $e->getMessage()], 500);
        }
    }

    /**
     * Log recalculation request to the recalculation_logs table
     */
    private function logRecalculationRequest($accountId, $fromDate, $triggerType, $description)
    {
        try {
            DB::table('recalculation_logs')->insert([
                'account_id' => $accountId,
                'from_date' => $fromDate,
                'trigger_type' => $triggerType,
                'description' => $description,
                'status' => 'pending',
                'created_at' => now(),
                'updated_at' => now()
            ]);
        } catch (\Exception $e) {
            Log::warning("Failed to log recalculation request", [
                'error' => $e->getMessage(),
                'account_id' => $accountId
            ]);
        }
    }

    /**
     * Recalculates all subsequent transactions for an account from a given date
     * This is used to immediately update balances without relying on background jobs
     * 
     * @param int $accountId The account ID to recalculate
     * @param string $startDate The date to start recalculation from (Y-m-d format)
     * @return void
     */
    private function recalculateAllSubsequentTransactions(int $accountId, string $startDate): void
    {
        try {
            // Load the account
            $account = Account::with('currency')->findOrFail($accountId);
            if (!$account->currency_id) {
                Log::warning("Account ID {$accountId} is not a currency account. Skipping recalculation.");
                return;
            }

            // Get all transactions ordered by date and ID
            $transactions = JournalEntry::where('account_id', $accountId)
                ->where('date', '>=', $startDate)
                ->orderBy('date', 'asc')
                ->orderBy('id', 'asc')
                ->get();

            if ($transactions->isEmpty()) {
                Log::info("No transactions found for Account ID: {$accountId} on or after {$startDate}. No recalculation needed.");
                return;
            }

            // Find the last transaction before our batch to get starting balances
            $firstTransactionInBatch = $transactions->first();
            $latestTxBeforeBatch = JournalEntry::where('account_id', $accountId)
                ->where(function ($query) use ($firstTransactionInBatch) {
                    $query->where('date', '<', $firstTransactionInBatch->date)
                          ->orWhere(function ($query) use ($firstTransactionInBatch) {
                              $query->where('date', '=', $firstTransactionInBatch->date)
                                    ->where('id', '<', $firstTransactionInBatch->id);
                          });
                })
                ->orderBy('date', 'desc')
                ->orderBy('id', 'desc')
                ->first();

            // Initialize running balances from last transaction or zero
            $currentCurrencyBalance = $latestTxBeforeBatch ? (float)$latestTxBeforeBatch->running_currency_balance : 0.0;
            $currentPkrValue = $latestTxBeforeBatch ? (float)$latestTxBeforeBatch->running_pkr_balance : 0.0;

            Log::debug("Initial state before recalculation - Currency Balance: {$currentCurrencyBalance}, PKR Value: {$currentPkrValue}");

            // Collect sales transactions that need P&L updates
            $salesToUpdate = [];

            // Process each transaction
            DB::beginTransaction();
            foreach ($transactions as $transaction) {
                // Get transaction amounts
                $transactionCurrencyAmount = (float)($transaction->currency_amount ?? 0.0);
                $transactionPkrAmount = (float)$transaction->amount;

                Log::debug("Processing transaction ID: {$transaction->id}, Type: " . 
                        ($transaction->is_credit ? 'Credit' : 'Debit') . 
                        ", Currency Amount: " . ($transactionCurrencyAmount ?: 'NULL') . 
                        ", PKR Amount: {$transactionPkrAmount}");

                // Calculate average rate based on account formula
                $avgRateBeforeThisTx = 0.0;
                if ($account->formula === 'd') { // Direct formula: Currency/PKR
                    if ($currentPkrValue != 0) {
                        $avgRateBeforeThisTx = $currentCurrencyBalance / $currentPkrValue;
                    }
                } else { // Inverse formula: PKR/Currency
                    if ($currentCurrencyBalance != 0) {
                        $avgRateBeforeThisTx = $currentPkrValue / $currentCurrencyBalance;
                    }
                }
                
                $oldAvgRate = (float)($transaction->avg_rate ?? 0.0);
                $newAvgRate = round($avgRateBeforeThisTx, 2);
                
                Log::debug("Calculated AvgRate: {$newAvgRate}, Old value: {$oldAvgRate}");
                
                $transaction->avg_rate = $newAvgRate;

                // For Purchase entry, make sure the PKR amount is properly calculated
                if ($transaction->payment_type_id == 10 && !$transaction->is_credit && $transactionCurrencyAmount > 0) {
                    // This is a purchase debit entry, recalculate PKR amount based on exact exchange rate
                    $currencyExchangeRate = (float)$transaction->exchange_rate; // This is the purchase rate
                    $formula = $account->formula;
                    
                    // Get correct PKR amount based on exact formula and exchange rate
                    $correctPkrAmount = $formula === 'd' 
                        ? round($transactionCurrencyAmount / $currencyExchangeRate, 2)
                        : round($transactionCurrencyAmount * $currencyExchangeRate, 2);
                    
                    if (abs($correctPkrAmount - $transactionPkrAmount) > 0.01) {
                        Log::debug("Updating purchase PKR amount - Old: {$transactionPkrAmount}, New: {$correctPkrAmount}");
                        $transaction->amount = $correctPkrAmount;
                        $transactionPkrAmount = $correctPkrAmount; // Update for running balance calculation
                    }
                }

                // For Sale entry, update based on the new average rate
                if ($transaction->is_credit && 
                    $transaction->payment_type_id == 11 && // Sale
                    $transactionCurrencyAmount > 0) {
                    
                    // Calculate the new COGS based on the new average rate
                    $formula = $account->formula;
                    $newCogsPkrValue = $formula === 'd' 
                        ? round($transactionCurrencyAmount / $newAvgRate, 2)
                        : round($transactionCurrencyAmount * $newAvgRate, 2);
                    
                    Log::debug("Updating sale COGS - Old: {$transactionPkrAmount}, New: -" . abs($newCogsPkrValue));
                    
                    // Store for updating after committing the main transaction
                    $salesToUpdate[] = [
                        'tid' => $transaction->TID,
                        'inv_no' => $transaction->inv_no,
                        'new_cogs_pkr' => abs($newCogsPkrValue), // Ensure positive for calculation
                        'currency_amount' => $transactionCurrencyAmount,
                        'old_avg_rate' => $oldAvgRate,
                        'new_avg_rate' => $newAvgRate
                    ];
                    
                    // Update the PKR amount of the currency credit leg to reflect the new COGS
                    $oldAmount = $transaction->amount;
                    $transaction->amount = -abs($newCogsPkrValue); // Ensure negative for credit
                    $transactionPkrAmount = -abs($newCogsPkrValue); // Update for running balance calculation
                    Log::debug("Updated sale amount from {$oldAmount} to {$transaction->amount}");
                }

                // Update running balances based on transaction type
                if ($transaction->is_credit) { // Credit transaction
                    $currentCurrencyBalance -= $transactionCurrencyAmount;
                    $currentPkrValue += $transactionPkrAmount; // PKR amount is negative for credits, so this correctly subtracts value.
                } else { // Debit transaction
                    $currentCurrencyBalance += $transactionCurrencyAmount;
                    $currentPkrValue += $transactionPkrAmount;
                }

                // Save updated balances with appropriate rounding
                $transaction->running_currency_balance = round($currentCurrencyBalance, 2);
                $transaction->running_pkr_balance = round($currentPkrValue, 2);

                $transaction->save();
                Log::debug("Updated Tx ID: {$transaction->id}. AvgRate: {$transaction->avg_rate}, RunCurrBal: {$transaction->running_currency_balance}, RunPkrBal: {$transaction->running_pkr_balance}");
            }
            DB::commit();

            // Now update all the P&L entries for affected sales
            if (count($salesToUpdate) > 0) {
                Log::info("Updating P&L entries for " . count($salesToUpdate) . " sales affected by rate changes");
                foreach ($salesToUpdate as $saleUpdate) {
                    try {
                        $this->updateSalePnlEntry(
                            $saleUpdate['tid'], 
                            $saleUpdate['inv_no'], 
                            $saleUpdate['new_cogs_pkr']
                        );
                        Log::info("Updated P&L for sale TID: {$saleUpdate['tid']}, InvNo: {$saleUpdate['inv_no']}, New COGS: {$saleUpdate['new_cogs_pkr']}");
                    } catch (\Exception $e) {
                        Log::error("Failed to update P&L for sale TID: {$saleUpdate['tid']}, InvNo: {$saleUpdate['inv_no']}. Error: " . $e->getMessage());
                    }
                }
            }
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Error during manual recalculation for Account ID: {$accountId}, Date: {$startDate}. Error: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Updates the P&L entry associated with a sale.
     * 
     * @param string $tid Transaction ID
     * @param string $invNo Invoice number
     * @param float $newCogsPkrValue New cost of goods sold in PKR
     * @return void
     */
    private function updateSalePnlEntry(string $tid, string $invNo, float $newCogsPkrValue): void
    {
        DB::beginTransaction();
        try {
            // Find customer debit leg (selling price in PKR)
            $customerDebitJe = JournalEntry::where('TID', $tid)
                ->where('inv_no', $invNo)
                ->where('payment_type_id', 11) // Sale transaction
                ->where('is_credit', 0)        // Debit entry
                ->whereNull('currency_amount') // PKR transaction (no currency amount)
                ->first();

            if (!$customerDebitJe) {
                Log::warning("Could not find customer debit leg for sale TID: {$tid}, InvNo: {$invNo}");
                DB::rollBack();
                return;
            }

            // Get selling price in PKR
            $pkrAtSellingRate = (float)$customerDebitJe->amount;

            // Find currency credit leg to identify P&L entry and update its PKR amount
            $currencyCreditJe = JournalEntry::where('TID', $tid)
                ->where('inv_no', $invNo)
                ->where('payment_type_id', 11)
                ->where('is_credit', 1)
                ->whereNotNull('currency_amount')
                ->first();
                
            if(!$currencyCreditJe){
                Log::warning("Could not find currency credit leg for sale TID: {$tid}, InvNo: {$invNo}");
                DB::rollBack();
                return;
            }

            // Find P&L entry (not the customer debit leg or currency credit leg)
            $pnlJe = JournalEntry::where('TID', $tid)
                ->where('inv_no', $invNo)
                ->where('payment_type_id', 11)
                ->whereNot('id', $customerDebitJe->id)
                ->whereNot('id', $currencyCreditJe->id)
                ->first();

            if ($pnlJe) {
                // Calculate actual profit/loss
                $actualProfitLoss = $pkrAtSellingRate - $newCogsPkrValue;
                $oldPnlAmount = (float)$pnlJe->amount;

                // Update P&L entry with new amount and correct credit/debit flag
                $pnlJe->amount = $actualProfitLoss >= 0 ? -round($actualProfitLoss, 2) : round(abs($actualProfitLoss), 2);
                $pnlJe->is_credit = $actualProfitLoss >= 0 ? 1 : 0; // Profit is Credit to P&L
                $pnlJe->save();
                
                // Update description to reflect the new margin
                $description = $pnlJe->description;
                if (preg_match('/\| Margin: [\d\.\-]+/', $description, $matches)) {
                    $newDescription = str_replace(
                        $matches[0], 
                        "| Margin: " . round($actualProfitLoss, 2),
                        $description
                    );
                    $pnlJe->description = $newDescription;
                    $pnlJe->save();
                }
                
                Log::debug("Updated P&L amount from {$oldPnlAmount} to {$pnlJe->amount}");
            } else {
                Log::warning("Could not find P&L leg for sale TID: {$tid}, InvNo: {$invNo}");
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Error updating P&L for sale TID: {$tid}, InvNo: {$invNo}. Error: " . $e->getMessage());
            throw $e;
        }
    }

    public function destroy($id) // $id is P-NO (inv_no)
    {
        DB::beginTransaction();
        try {
            $journalEntries = JournalEntry::where('inv_no', $id) // $id is P-NO
                                        ->where('payment_type_id', 10) // Purchase
                                        ->get();

            if ($journalEntries->isEmpty()) {
                DB::rollBack();
                return response()->json(['error' => 'Purchase transaction not found.'], 404);
            }

            // Collect account IDs and dates before deletion for potential recalculation hooks
            $affectedAccounts = [];
            $earliestDate = null;
            
            foreach ($journalEntries as $entry) {
                // Only include currency accounts (those with currency transactions)
                if ($entry->currency_amount !== null) {
                    $accountId = $entry->account_id;
                    $date = $entry->date->format('Y-m-d');
                    
                    $affectedAccounts[$accountId] = $date;
                    
                    // Track the earliest date for recalculation
                    if ($earliestDate === null || $date < $earliestDate) {
                        $earliestDate = $date;
                    }
                }
                
                // Delete the entry
                $entry->delete();
            }
            
            // TODO: Implement robust recalculation for subsequent journal entries' running balances
            // for all affected accounts from their respective transaction dates.

            // Delete BANAM entries if they exist
            // ExchangeEntry::where('ps_number', $id)->delete();

            DB::commit();

            // Recalculate balances for all affected accounts
            foreach ($affectedAccounts as $accountId => $date) {
                try {
                    Log::info("Performing immediate recalculation for Account ID: {$accountId} from Date: {$date} after deleting P-NO: {$id}");
                    $this->recalculateAllSubsequentTransactions($accountId, $date);
                } catch (\Exception $e) {
                    Log::error("Error recalculating balances for Account ID: {$accountId} after deletion: " . $e->getMessage());
                    // Continue with other accounts even if one fails
                }
            }

            return response()->json(['message' => 'Purchase transaction deleted. All affected balances have been recalculated.']);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['error' => 'Failed to delete purchase transaction.', 'details' => $e->getMessage()], 500);
        }
    }

}
