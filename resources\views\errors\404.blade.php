@php
    $isJson = request()->expectsJson();
@endphp
@if ($isJson)
    @php
        echo response()->json(['message' => 'Not Found: The requested page does not exist.'], 404);
        exit;
    @endphp
@else
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Page Not Found</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;700&display=swap" rel="stylesheet">
    <style>
        body { background: linear-gradient(135deg, #f6faff 0%, #e6fff9 100%); color: #1b1b18; font-family: 'Inter', sans-serif; margin: 0; }
        .center { min-height: 100vh; display: flex; flex-direction: column; align-items: center; justify-content: center; }
        .card { background: rgba(255,255,255,0.95); border-radius: 1.5rem; box-shadow: 0 8px 32px rgba(0,146,194,0.08); padding: 2.5rem 2rem; border: 1px solid #e0f7ff; display: flex; flex-direction: column; align-items: center; }
        .icon { width: 5rem; height: 5rem; color: #0092c2; margin-bottom: 1.5rem; }
        h1 { font-size: 2.5rem; color: #0092c2; margin-bottom: 0.5rem; }
        p { font-size: 1.2rem; color: #706f6c; margin-bottom: 2rem; text-align: center; }
        a.button { display: inline-block; padding: 0.75rem 2rem; border-radius: 0.5rem; background: #0092c2; color: #fff; font-weight: 700; text-decoration: none; box-shadow: 0 2px 8px rgba(0,146,194,0.08); transition: background 0.2s; }
        a.button:hover { background: #007ba1; }
        @media (max-width: 600px) { .card { padding: 1.5rem 0.5rem; } h1 { font-size: 2rem; } }
    </style>
</head>
<body>
    <div class="center">
        <div class="card">
            <svg class="icon" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 48 48">
                <circle cx="22" cy="22" r="14" stroke="#0092c2" stroke-width="2" fill="#0092c2" fill-opacity="0.12" />
                <circle cx="22" cy="22" r="8" stroke="#0092c2" stroke-width="2" fill="#fff" />
                <line x1="32" y1="32" x2="44" y2="44" stroke="#0092c2" stroke-width="3" stroke-linecap="round" />
            </svg>
            <h1>Page Not Found</h1>
            <p>Sorry, the page you are looking for does not exist.<br>You may have mistyped the address or the page may have moved.</p>
            <a href="/" class="button">Go to Home</a>
        </div>
    </div>
</body>
</html>
@endif 