<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comprehensive Guide</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <header>
        <h1>Comprehensive Guide to Laravel 12, React, MySQL Optimization, and Financial Accounting</h1>
    </header>

    <nav id="table-of-contents">
        <h2>Table of Contents</h2>
        <ul>
            <li><a href="#laravel-12-features">Laravel 12 Features</a></li>
            <li><a href="#laravel-12-with-react-integration">Laravel 12 with React Integration</a></li>
            <li><a href="#mysql-optimization-techniques">MySQL Optimization Techniques for Laravel Financial Applications</a></li>
            <li><a href="#financial-double-accounting-packages">Financial Double Accounting Packages</a></li>
            <li><a href="#financial-app-implementation">Financial Double Accounting Application Implementation</a></li>
            <li><a href="#conclusion">Conclusion</a></li>
        </ul>
    </nav>

    <main>
        <section id="introduction">
            <h2>Introduction</h2>
            <p>This comprehensive guide compiles extensive research and practical implementation details on Laravel 12 features, React integration, MySQL optimization strategies, and financial double accounting packages for Laravel applications. It is designed to provide developers with a complete resource for building modern, efficient, and scalable financial applications with Laravel 12, covering both theoretical concepts and practical implementation steps.</p>
        </section>

        <section id="laravel-12-features">
            <h2>Laravel 12 Features</h2>
            <p>Laravel 12, released on March 12, 2024, introduces significant improvements that enhance developer experience, application performance, and code simplicity.</p>

            <h3>Code Simplification</h3>
            <h4>Simplified Validation</h4>
            <p>Laravel 12 introduces a more concise validation syntax:</p>
            <pre><code class="language-php">// Laravel 12 approach
$validated = $request->validate([
    'name' => 'required|string|max:255',
    'email' => 'required|email|unique:users',
]);

// Previously you needed to handle validation manually
</code></pre>

            <h4>Invokable Route Controllers</h4>
            <p>Laravel 12 enhances invokable controllers, making single-action controllers more elegant:</p>
            <pre><code class="language-php">// routes/web.php
Route::get('/dashboard', DashboardController::class);

// app/Http/Controllers/DashboardController.php
class DashboardController
{
    public function __invoke(Request $request)
    {
        return view('dashboard');
    }
}
</code></pre>

            <h4>Improved Type Hinting</h4>
            <p>Laravel 12 leverages PHP 8.2+ features, offering better type hints and return types:</p>
            <pre><code class="language-php">public function store(StoreUserRequest $request): RedirectResponse
{
    $user = User::create($request->validated());

    return redirect()->route('users.show', $user);
}
</code></pre>

            <h3>Routing Improvements</h3>
            <h4>Route Groups with Attributes</h4>
            <p>Laravel 12 introduces a cleaner way to define route groups using PHP attributes:</p>
            <pre><code class="language-php">#[RouteGroup([
    'middleware' => ['auth', 'verified'],
    'prefix' => 'admin',
    'as' => 'admin.',
])]
class AdminRoutes
{
    #[Get('dashboard')]
    public function dashboard()
    {
        return view('admin.dashboard');
    }

    #[Post('users')]
    public function storeUser(StoreUserRequest $request)
    {
        // Create user
    }
}
</code></pre>

            <h4>Route Caching Improvements</h4>
            <p>Laravel 12 enhances route caching, significantly improving application bootstrap time:</p>
            <pre><code class="language-bash"># Generate optimized route cache
php artisan route:cache

# Clear route cache
php artisan route:clear
</code></pre>

            <h4>API Versioning Support</h4>
            <p>Laravel 12 simplifies API versioning with built-in support:</p>
            <pre><code class="language-php">// routes/api.php
Route::prefix('v1')->group(function () {
    Route::apiResource('users', Api\V1\UserController::class);
});

Route::prefix('v2')->group(function () {
    Route::apiResource('users', Api\V2\UserController::class);
});
</code></pre>

            <h3>Performance Enhancements</h3>
            <h4>Just-In-Time Compilation</h4>
            <p>Laravel 12 introduces JIT compilation for Blade templates, significantly reducing render times:</p>
            <pre><code class="language-bash"># Enable JIT compilation for Blade templates
php artisan view:cache
</code></pre>

            <h4>Lazy Collections</h4>
            <p>Laravel 12 enhances lazy collections for memory-efficient processing of large datasets:</p>
            <pre><code class="language-php">// Process millions of records without memory issues
User::cursor()->filter(function ($user) {
    return $user->active;
})->each(function ($user) {
    $user->sendNewsletterEmail();
});
</code></pre>

            <h4>Route Registration Optimization</h4>
            <p>Laravel 12 optimizes route registration process, enhancing application bootstrap time:</p>
            <pre><code class="language-php">// Blazing fast route registration
Route::get('/user/{id}', [UserController::class, 'show']);
</code></pre>

            <h3>Database Optimizations</h3>
            <h4>Optimized Eloquent Query Builder</h4>
            <p>Laravel 12 features an optimized query builder with reduced overhead:</p>
            <pre><code class="language-php">// More efficient query execution
$users = User::query()
    ->where('active', true)
    ->whereHas('subscriptions', function ($query) {
        $query->where('status', 'active');
    })
    ->with(['profile', 'roles'])
    ->paginate(20);
</code></pre>

            <h4>Enhanced Relationship Loading</h4>
            <p>Laravel 12 improves relationship loading with better caching and reduced queries:</p>
            <pre><code class="language-php">// More efficient eager loading
$posts = Post::with(['author', 'comments.author', 'tags'])
    ->latest()
    ->paginate(15);
</code></pre>

            <h4>Database Connection Pooling Support</h4>
            <p>Laravel 12 adds support for database connection pooling, improving handling of concurrent connections. This can be configured in <code>config/database.php</code>:</p>
            <pre><code class="language-php">// config/database.php
'mysql' => [
    // ...
    'pool' => [
        'enabled' => true,
        'min' => 5,
        'max' => 20,
    ],
],
</code></pre>

            <h3>UI Improvements</h3>
            <h4>Optimistic UI Updates</h4>
            <p>Laravel 12 with Inertia.js enables seamless optimistic UI updates. This involves updating the UI immediately after a user action and then performing the actual update in the background, reverting the UI if an error occurs.</p>
            <pre><code class="language-php">// Controller example
public function update(Request $request, Post $post)
{
    $validated = $request->validate([
        'title' => 'required|string|max:255',
        'content' => 'required|string',
    ]);

    $post->update($validated);

    return back();
}
</code></pre>
            <pre><code class="language-jsx">// React component with optimistic updates example
import React, { useState } from 'react';
import { Inertia } from '@inertiajs/inertia';

export default function Edit({ post }) {
    const [formData, setFormData] = useState({
        title: post.title,
        content: post.content,
    });

    const [isSubmitting, setIsSubmitting] = useState(false);

    const handleSubmit = async (e) => {
        e.preventDefault();
        setIsSubmitting(true);

        // Optimistically show success state
        const originalPost = {...post};
        post.title = formData.title;
        post.content = formData.content;

        try {
            await Inertia.put(`/posts/${post.id}`, formData);
        } catch (error) {
            // Revert to original data if error occurs
            post.title = originalPost.title;
            post.content = originalPost.content;
            alert('Failed to update post.');
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <form onSubmit={handleSubmit}>
            {/* Form fields */}
            <button
                type="submit"
                disabled={isSubmitting}
            >
                {isSubmitting ? 'Updating...' : 'Update Post'}
            </button>
        </form>
    );
}
</code></pre>

            <h4>Improved Vite Integration</h4>
            <p>Laravel 12 features enhanced Vite.js integration for faster asset compilation. Configuration is typically done in <code>vite.config.js</code>:</p>
            <pre><code class="language-javascript">// vite.config.js
import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';
import react from '@vitejs/plugin-react';

export default defineConfig({
    plugins: [
        laravel({
            input: ['resources/css/app.css', 'resources/js/app.jsx'],
            refresh: true,
        }),
        react(),
    ],
});
</code></pre>
        </section>

        <section id="laravel-12-with-react-integration">
            <h2>Laravel 12 with React Integration</h2>
            <p>Laravel 12 offers excellent React integration through its official starter kit and the powerful Inertia.js library, enabling the building of modern Single Page Applications (SPAs) with a Laravel backend and React frontend.</p>

            <h3>React Starter Kit Overview</h3>
            <p>Laravel's React Starter Kit provides a complete solution for building modern SPAs. You can create a new project with the starter kit using Composer and Artisan:</p>
            <pre><code class="language-bash"># Create a new Laravel 12 project with React
composer create-project laravel/laravel example-app
cd example-app
composer require laravel/breeze --dev
php artisan breeze:install react
</code></pre>
            <p>The starter kit typically includes:</p>
            <ul>
                <li>React frontend with Vite</li>
                <li>Inertia.js for seamless frontend-backend integration</li>
                <li>Tailwind CSS for styling</li>
                <li>Authentication scaffolding</li>
                <li>API token management</li>
            </ul>

            <h3>Setup and Installation</h3>
            <p>Setting up a Laravel 12 project with React integration involves installing dependencies and starting development servers:</p>
            <pre><code class="language-bash"># Install dependencies
npm install

# Start development servers
php artisan serve
npm run dev
</code></pre>
            <p>The typical directory structure for React components is:</p>
            <ul>
                <li><code>/resources/js</code> - Main React components and logic</li>
                <li><code>/resources/js/Pages</code> - Page components for Inertia.js</li>
                <li><code>/resources/js/Layouts</code> - Layout components</li>
                <li><code>/resources/js/Components</code> - Reusable React components</li>
            </ul>
            <p>You may also add libraries like React Query for efficient data fetching:</p>
            <pre><code class="language-bash">npm install @tanstack/react-query
</code></pre>

            <h3>Inertia.js Integration</h3>
            <p>Inertia.js bridges Laravel and React, allowing server-side routing with client-side rendering. Controllers return Inertia responses that render React components:</p>
            <pre><code class="language-php">// Controller example
use Inertia\Inertia;
use App\Models\User;
use App\Models\Post;

public function index()
{
    return Inertia::render('Dashboard', [
        'stats' => [
            'users' => User::count(),
            'posts' => Post::count(),
        ],
    ]);
}
</code></pre>
            <p>React components receive data as props:</p>
            <pre><code class="language-jsx">// resources/js/Pages/Dashboard.jsx
import React from 'react';
import Layout from '@/Layouts/AuthenticatedLayout';

export default function Dashboard({ stats }) {
    return (
        <Layout>
            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div className="p-6 bg-white border-b border-gray-200">
                            <h2>Dashboard Stats</h2>
                            <p>Users: {stats.users}</p>
                            <p>Posts: {stats.posts}</p>
                        </div>
                    </div>
                </div>
            </div>
        </Layout>
    );
}
</code></pre>

            <h3>Optimistic UI Updates</h3>
            <p>Implementing optimistic UI updates with Laravel 12 and React using Inertia.js allows for a more responsive user experience. (See examples in Laravel 12 Features > UI Improvements > Optimistic UI Updates)</p>

            <h3>React Integration Best Practices</h3>
            <p>Key best practices for integrating React with Laravel include:</p>
            <p>1. <strong>State Management</strong>:</p>
            <ul>
                <li>Use React Query for server state management, handling data fetching, caching, and synchronization.</li>
                <li>Use React's Context API or libraries like Redux for managing complex application-wide state.</li>
                <li>Leverage Inertia.js for managing shared state between the server and client.</li>
            </ul>
            <pre><code class="language-jsx">// Setting up React Query with Laravel
import { QueryClient, QueryClientProvider } from 'react-query';

const queryClient = new QueryClient({
    defaultOptions: {
        queries: {
            refetchOnWindowFocus: false,
            retry: 1,
        },
    },
});

export default function App({ Component, pageProps }) {
    return (
        <QueryClientProvider client={queryClient}>
            <Component {...pageProps} />
        </QueryClientProvider>
    );
}
</code></pre>
            <p>2. <strong>Component Structure</strong>:</p>
            <ul>
                <li>Create reusable UI components to promote consistency and reduce code duplication.</li>
                <li>Separate business logic from presentational UI components.</li>
                <li>Implement patterns like container/presentational components for better organization and separation of concerns.</li>
            </ul>
            <pre><code class="language-jsx">// Presentational component example
const UserCard = ({ user, onEdit }) => (
    <div className="user-card">
        <h3>{user.name}</h3>
        <p>{user.email}</p>
        <button onClick={() => onEdit(user)}>Edit</button>
    </div>
);

// Container component example
import React, { useState, useEffect } from 'react';
import axios from 'axios';
import UserCard from './UserCard'; // Assuming UserCard is in the same directory
import UserEditModal from './UserEditModal'; // Assuming UserEditModal exists

const UserList = () => {
    const [users, setUsers] = useState([]);
    const [selectedUser, setSelectedUser] = useState(null);

    useEffect(() => {
        axios.get('/api/users').then(response => {
            setUsers(response.data);
        });
    }, []);

    const handleEdit = (user) => {
        setSelectedUser(user);
    };

    return (
        <div>
            {users.map(user => (
                <UserCard
                    key={user.id}
                    user={user}
                    onEdit={handleEdit}
                />
            ))}
            {selectedUser && <UserEditModal user={selectedUser} />}
        </div>
    );
};
</code></pre>
            <p>3. <strong>API Handling</strong>:</p>
            <ul>
                <li>Create dedicated API service files to centralize API calls and improve code organization.</li>
                <li>Implement robust error handling and manage loading states for API requests.</li>
                <li>Use Axios interceptors for tasks like automatically adding authentication headers to requests.</li>
            </ul>
            <pre><code class="language-jsx">// api/users.js example
import axios from 'axios';

export const getUsers = () => axios.get('/api/users');
export const getUser = (id) => axios.get(`/api/users/${id}`);
export const updateUser = (id, data) => axios.put(`/api/users/${id}`, data);
export const deleteUser = (id) => axios.delete(`/api/users/${id}`);

// Using the API service in a component
import React, { useState, useEffect } from 'react';
import { getUsers } from '@/api/users';

export default function UsersList() {
    const [users, setUsers] = useState([]);

    useEffect(() => {
        getUsers().then(response => {
            setUsers(response.data);
        });
    }, []);

    // ... rest of the component
}
</code></pre>
        </section>

        <section id="mysql-optimization-techniques">
            <h2>MySQL Optimization Techniques for Laravel Financial Applications</h2>
            <p>This guide focuses specifically on MySQL optimization techniques for Laravel financial applications that handle large volumes of transaction data, aiming to improve performance and scalability.</p>

            <h3>Database Design Optimization</h3>
            <h4>Optimal Data Types</h4>
            <p>Choosing the most efficient data types is crucial to minimize storage space and improve query performance. Use appropriate types like <code>UNSIGNED</code> integers, smallest possible integer types, fixed-length types for financial data (e.g., <code>DECIMAL</code>), and <code>VARCHAR</code> with realistic limits. Use <code>INT</code> for foreign keys and <code>TINYINT(1)</code> for booleans. Use <code>DATE</code> when time is not required.</p>
            <pre><code class="language-php">// Migration with optimized data types example
Schema::create('transactions', function (Blueprint $table) {
    $table->id();
    $table->unsignedBigInteger('user_id'); // Use UNSIGNED for positive-only numbers
    $table->smallInteger('fiscal_year')->unsigned(); // Smallest possible integer type
    $table->decimal('amount', 13, 4); // Fixed-length for financial data
    $table->string('reference', 50); // VARCHAR with realistic limits
    $table->foreignId('entity_id')->constrained(); // Use INT for foreign keys
    $table->boolean('is_reconciled')->default(false); // Use TINYINT(1) for boolean flags
    $table->date('transaction_date'); // Use DATE instead of DATETIME
    $table->timestamps();
});
</code></pre>

            <h4>Normalization vs. Denormalization</h4>
            <p>Proper normalization is essential for data integrity in financial systems, but it can impact query performance due to joins. Consider denormalization for frequently accessed summary data to improve read performance, while carefully managing data consistency.</p>
            <pre><code class="language-php">// Normalized schema example (Accounts, Transactions, Transaction Entries)
Schema::create('accounts', function (Blueprint $table) {
    $table->id();
    $table->foreignId('chart_id')->constrained();
    $table->string('code', 20);
    $table->string('name');
    $table->string('type', 20);
    $table->timestamps();
});

Schema::create('transactions', function (Blueprint $table) {
    $table->id();
    $table->string('reference', 50);
    $table->text('description');
    $table->date('transaction_date');
    $table->timestamps();
});

Schema::create('transaction_entries', function (Blueprint $table) {
    $table->id();
    $table->foreignId('transaction_id')->constrained()->onDelete('cascade');
    $table->foreignId('account_id')->constrained();
    $table->decimal('amount', 13, 4);
    $table->boolean('is_credit')->default(false);
    $table->timestamps();
});
</code></pre>
            <h4>Denormalized Summary Tables</h4>
            <p>For frequently accessed summary data, such as account balances, consider creating denormalized summary tables that are updated periodically. This can significantly speed up reporting queries.</p>
            <pre><code class="language-php">// Denormalized account balances table example
Schema::create('account_balances', function (Blueprint $table) {
    $table->id();
    $table->foreignId('account_id')->constrained();
    $table->date('balance_date');
    $table->decimal('debit_total', 13, 4)->default(0);
    $table->decimal('credit_total', 13, 4)->default(0);
    $table->decimal('balance', 13, 4)->default(0);
    $table->timestamps();

    // Composite index for fast lookups
    $table->unique(['account_id', 'balance_date']);
});
</code></pre>

            <h3>Indexing Strategies</h3>
            <p>Effective indexing is critical for fast query performance in financial applications with large datasets.</p>
            <h4>Strategic Column Indexing</h4>
            <p>Identify and index columns frequently used in <code>WHERE</code>, <code>JOIN</code>, <code>ORDER BY</code>, and <code>GROUP BY</code> clauses. For financial data, this often includes account IDs, transaction dates, and reference numbers.</p>
            <pre><code class="language-php">// Add indexes for commonly queried columns example
Schema::table('transaction_entries', function (Blueprint $table) {
    $table->index('account_id'); // For filtering transactions by account
    $table->index('is_credit'); // For filtering by transaction type
    $table->index(['account_id', 'is_credit']); // Composite index for reports
});

Schema::table('transactions', function (Blueprint $table) {
    $table->index('transaction_date'); // For date range queries
    $table->index('reference'); // For searching by reference number
});
</code></pre>

            <h4>Composite Indexes</h4>
            <p>Create composite indexes for multi-column conditions that appear together in queries, following the order of columns in the <code>WHERE</code> clause for optimal use.</p>
            <pre><code class="language-php">// Composite index for common financial reporting query example
Schema::table('transaction_entries', function (Blueprint $table) {
    $table->index(['account_id', 'transaction_id']); // For queries filtering by account and transaction
});

Schema::table('transactions', function (Blueprint $table) {
    $table->index(['entity_id', 'transaction_date']); // For filtering transactions by entity and date
});
</code></pre>

            <h4>Foreign Key Indexes</h4>
            <p>Always index foreign key columns to significantly speed up join operations between related tables.</p>
            <pre><code class="language-php">Schema::table('transaction_entries', function (Blueprint $table) {
    $table->foreign('transaction_id')
          ->references('id')
          ->on('transactions')
          ->onDelete('cascade');

    $table->foreign('account_id')
          ->references('id')
          ->on('accounts');

    // Ensure foreign keys are indexed
    $table->index('transaction_id');
    $table->index('account_id');
});
</code></pre>

            <h4>Analyze Index Usage</h4>
            <p>Regularly analyze index usage to identify indexes that are not being used or are inefficient. This helps in cleaning up unnecessary indexes which can impact write performance.</p>
            <pre><code class="language-sql">-- Find unused indexes example (requires performance schema enabled)
SELECT
    t.TABLE_SCHEMA,
    t.TABLE_NAME,
    ix.INDEX_NAME,
    ix.COLUMN_NAME
FROM
    information_schema.STATISTICS ix
LEFT JOIN
    mysql.schema_index_statistics s
    ON ix.TABLE_SCHEMA = s.table_schema
    AND ix.TABLE_NAME = s.table_name
    AND ix.INDEX_NAME = s.index_name
WHERE
    s.rows_read IS NULL OR s.rows_read = 0
    AND t.TABLE_SCHEMA = 'your_database';
</code></pre>

            <h3>Query Optimization</h3>
            <p>Optimizing your database queries is paramount for performance, especially when dealing with large financial datasets.</p>
            <h4>Efficient Eloquent Queries</h4>
            <p>Use Eloquent features efficiently to avoid common performance pitfalls like the N+1 problem. Eager loading relationships with <code>with()</code> is crucial.</p>
            <pre><code class="language-php">// BAD: Inefficient query with N+1 problem
$transactions = Transaction::where('entity_id', $entityId)
    ->whereBetween('transaction_date', [$startDate, $endDate])
    ->get();

foreach ($transactions as $transaction) {
    // This causes N additional queries
    $entries = $transaction->entries;
    foreach ($entries as $entry) {
        // This causes N*M additional queries
        $account = $entry->account;
    }
}

// GOOD: Efficient query with eager loading
$transactions = Transaction::with(['entries', 'entries.account'])
    ->where('entity_id', $entityId)
    ->whereBetween('transaction_date', [$startDate, $endDate])
    ->get();
</code></pre>

            <h4>Select Only Required Columns</h4>
            <p>Avoid selecting all columns using <code>select('*')</code> or retrieving entire models when only a few columns are needed. Use <code>select()</code> to specify only the necessary columns.</p>
            <pre><code class="language-php">// BAD: Selecting all columns
$accounts = Account::all();

// GOOD: Select only what you need
$accounts = Account::select('id', 'code', 'name', 'type')->get();
</code></pre>

            <h4>Raw Queries for Complex Operations</h4>
            <p>For complex aggregations, calculations, or operations that are difficult or inefficient to express with the Eloquent query builder, consider using raw SQL queries or the DB facade.</p>
            <pre><code class="language-php">// Using raw expressions for complex calculations example
$accountBalances = DB::table('transaction_entries as e')
    ->join('transactions as t', 'e.transaction_id', '=', 't.id')
    ->select('e.account_id')
    ->selectRaw('SUM(CASE WHEN e.is_credit = 1 THEN e.amount ELSE 0 END) as credit_total')
    ->selectRaw('SUM(CASE WHEN e.is_credit = 0 THEN e.amount ELSE 0 END) as debit_total')
    ->whereRaw('t.transaction_date <= ?', [$balanceDate])
    ->groupBy('e.account_id')
    ->get();
</code></pre>

            <h4>Chunking for Large Datasets</h4>
            <p>When processing large numbers of records, use methods like <code>chunk()</code> or <code>chunkById()</code> to retrieve and process data in smaller chunks, preventing excessive memory consumption.</p>
            <pre><code class="language-php">// Process millions of transactions efficiently example
Transaction::where('fiscal_year', 2023)
    ->chunkById(1000, function ($transactions) {
        foreach ($transactions as $transaction) {
            // Process each transaction without memory issues
            ProcessTransaction::dispatch($transaction); // Example: dispatch a job for processing
        }
    });
</code></pre>

            <h4>Query Caching</h4>
            <p>Cache the results of expensive and frequently executed queries that do not change often. Laravel's Cache facade can be used for this purpose.</p>
            <pre><code class="language-php">// Cache financial report queries example
$monthEndReport = Cache::remember('month_end_report_' . $month, now()->addDays(1), function () use ($month) {
    return DB::table('transaction_entries as e')
        ->join('transactions as t', 'e.transaction_id', '=', 't.id')
        ->join('accounts as a', 'e.account_id', '=', 'a.id')
        ->select('a.id', 'a.name', 'a.type')
        ->selectRaw('SUM(CASE WHEN e.is_credit = 1 THEN e.amount ELSE 0 END) as credit_total')
        ->selectRaw('SUM(CASE WHEN e.is_credit = 0 THEN e.amount ELSE 0 END) as debit_total')
        ->whereRaw('t.transaction_date BETWEEN ? AND ?', [
            Carbon::parse($month)->startOfMonth(),
            Carbon::parse($month)->endOfMonth(),
        ])
        ->groupBy('a.id', 'a.name', 'a.type')
        ->get();
});
</code></pre>

            <h4>View Materialization</h4>
            <p>For very complex and frequently accessed reporting queries, consider using materialized views. These are pre-computed result sets that can be queried much faster than running the original complex query. You can use scheduled tasks to refresh materialized views periodically.</p>
            <pre><code class="language-php">// Create a scheduled task to refresh the materialized view example
// Assuming a materialized view named 'account_balances_materialized' exists
$schedule->call(function () {
    DB::statement('TRUNCATE account_balances_materialized');

    DB::statement("
        INSERT INTO account_balances_materialized (account_id, debit_total, credit_total, balance, as_of_date)
        SELECT
            e.account_id,
            SUM(CASE WHEN e.is_credit = 0 THEN e.amount ELSE 0 END) as debit_total,
            SUM(CASE WHEN e.is_credit = 1 THEN e.amount ELSE 0 END) as credit_total,
            SUM(CASE WHEN e.is_credit = 0 THEN e.amount ELSE -e.amount END) as balance,
            CURRENT_DATE() as as_of_date
        FROM transaction_entries e
        JOIN transactions t ON e.transaction_id = t.id
        WHERE t.transaction_date <= CURRENT_DATE()
        GROUP BY e.account_id
    ");
})->daily();
</code></pre>

            <h3>MySQL Server Configuration</h3>
            <p>Optimizing the MySQL server configuration is essential for handling the workload of a financial application.</p>
            <h4>InnoDB Settings</h4>
            <p>Tune InnoDB settings, such as <code>innodb_buffer_pool_size</code> (allocate 70-80% of available RAM), <code>innodb_log_file_size</code>, <code>innodb_flush_log_at_trx_commit</code>, and thread concurrency, based on your server's resources and workload characteristics (read-heavy vs. write-heavy).</p>
            <pre><code class="language-ini"># my.cnf optimized for financial data example
[mysqld]
innodb_buffer_pool_size = 4G # Allocate 70-80% of available RAM
innodb_log_file_size = 512M # For large transaction processing
innodb_log_buffer_size = 16M
innodb_flush_log_at_trx_commit = 2 # For write-heavy financial data
max_connections = 300 # Increase for high traffic
tmp_table_size = 64M # Increase temp table size for complex joins and sorts
max_heap_table_size = 64M
query_cache_type = 0 # Disable query cache (better to use application-level caching)
query_cache_size = 0
innodb_file_per_table = 1 # File-per-table for better management
join_buffer_size = 4M # For complex joins in financial reporting
sort_buffer_size = 4M
innodb_flush_method = O_DIRECT # Optimize IO for high-performance storage
innodb_doublewrite = 1 # Optimize for financial transaction processing
innodb_thread_concurrency = 0
innodb_read_io_threads = 8
innodb_write_io_threads = 8
</code></pre>

            <h4>Connection Pooling</h4>
            <p>Implement connection pooling to manage database connections efficiently, reducing the overhead of establishing new connections for each request. This can be configured in Laravel's database configuration.</p>
            <pre><code class="language-php">// config/database.php with connection pooling example
'mysql' => [
    'driver' => 'mysql',
    'host' => env('DB_HOST', '127.0.0.1'),
    'port' => env('DB_PORT', '3306'),
    'database' => env('DB_DATABASE', 'forge'),
    'username' => env('DB_USERNAME', 'forge'),
    'password' => env('DB_PASSWORD', ''),
    'unix_socket' => env('DB_SOCKET', ''),
    'charset' => 'utf8mb4',
    'collation' => 'utf8mb4_unicode_ci',
    'prefix' => '',
    'prefix_indexes' => true,
    'strict' => true,
    'engine' => null,
    'options' => extension_loaded('pdo_mysql') ? array_filter([
        PDO::MYSQL_ATTR_SSL_CA => env('MYSQL_ATTR_SSL_CA'),
    ]) : [],

    // Connection pooling for production
    'pool' => [
        'enabled' => env('DB_POOL', false),
        'min' => env('DB_POOL_MIN', 5),
        'max' => env('DB_POOL_MAX', 20),
    ],
],
</code></pre>

            <h3>Partitioning</h3>
            <p>Partitioning large tables can improve performance by dividing the data into smaller, more manageable parts.</p>
            <h4>Table Partitioning by Date</h4>
            <p>For financial applications with time-series data like transactions, partitioning by date (e.g., by year or quarter) can significantly speed up queries that filter by date range.</p>
            <pre><code class="language-sql">-- Partition transactions table by date example
ALTER TABLE transactions PARTITION BY RANGE (TO_DAYS(transaction_date)) (
    PARTITION p2022_q1 VALUES LESS THAN (TO_DAYS('2022-04-01')),
    PARTITION p2022_q2 VALUES LESS THAN (TO_DAYS('2022-07-01')),
    PARTITION p2022_q3 VALUES LESS THAN (TO_DAYS('2022-10-01')),
    PARTITION p2022_q4 VALUES LESS THAN (TO_DAYS('2023-01-01')),
    PARTITION p2023_q1 VALUES LESS THAN (TO_DAYS('2023-04-01')),
    PARTITION p2023_q2 VALUES LESS THAN (TO_DAYS('2023-07-01')),
    PARTITION p2023_q3 VALUES LESS THAN (TO_DAYS('2023-10-01')),
    PARTITION p2023_q4 VALUES LESS THAN (TO_DAYS('2024-01-01')),
    PARTITION p2024_q1 VALUES LESS THAN (TO_DAYS('2024-04-01')),
    PARTITION p2024_q2 VALUES LESS THAN (TO_DAYS('2024-07-01')),
    PARTITION p2024_q3 VALUES LESS THAN (TO_DAYS('2024-10-01')),
    PARTITION p2024_q4 VALUES LESS THAN (TO_DAYS('2025-01-01')),
    PARTITION future VALUES LESS THAN MAXVALUE
);
</code></pre>

            <h4>Partitioning by Entity ID</h4>
            <p>In multi-tenant financial systems, partitioning by entity ID can improve performance and manageability by physically separating data for different tenants.</p>
            <pre><code class="language-sql">-- Partition by entity ID for multi-tenant systems example
ALTER TABLE transaction_entries PARTITION BY HASH(account_id) PARTITIONS 8; -- Example using HASH partitioning
</code></pre>

            <h4>Partition Maintenance</h4>
            <p>Regular partition maintenance is necessary to add new partitions for future data and potentially archive or drop old partitions. This can be automated using scheduled tasks in Laravel.</p>
            <pre><code class="language-php">// Create a command to manage partitions example
namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ManageTransactionPartitions extends Command
{
    protected $signature = 'db:manage-partitions';
    protected $description = 'Add new quarterly partitions for transactions table';

    public function handle()
    {
        // Get the latest quarter currently partitioned
        $result = DB::select("
            SELECT partition_description
            FROM information_schema.partitions
            WHERE table_name = 'transactions'
            AND partition_name != 'future'
            ORDER BY partition_ordinal_position DESC
            LIMIT 1
        ");

        if (empty($result)) {
            $this->error('Could not determine last partition');
            return 1;
        }

        $lastPartitionValue = $result[0]->partition_description;
        $lastDate = Carbon::createFromFormat('Y-m-d', substr($lastPartitionValue, 9, 10));

        // Create next 4 quarters if they don't exist
        $nextQuarter = $lastDate->copy()->addMonths(3);

        for ($i = 0; $i < 4; $i++) {
            $partitionDate = $nextQuarter->copy()->addMonths($i * 3);
            $partitionName = 'p' . $partitionDate->format('Y_q') . $partitionDate->quarter;
            $boundaryDate = $partitionDate->format('Y-m-d');

            $this->info("Adding partition {$partitionName} for boundary {$boundaryDate}");

            DB::statement("
                ALTER TABLE transactions REORGANIZE PARTITION future INTO (
                    PARTITION {$partitionName} VALUES LESS THAN (TO_DAYS('{$boundaryDate}')),
                    PARTITION future VALUES LESS THAN MAXVALUE
                )
            ");
        }

        $this->info('Partition management completed');
        return 0;
    }
}
</code></pre>

            <h3>Laravel-Specific Optimizations</h3>
            <p>Leverage Laravel's features and configurations for database optimization.</p>
            <h4>Database Configuration</h4>
            <p>Adjust Laravel's database configuration in <code>config/database.php</code>, such as disabling strict mode in certain cases for performance or enabling emulated prepared statements and buffered queries via the <code>options</code> array.</p>
            <pre><code class="language-php">// config/database.php example with options
'mysql' => [
    // ...
    'strict' => false, // Disable strict mode for better performance in some cases
    'options' => [
        PDO::ATTR_EMULATE_PREPARES => true, // Enable emulated prepared statements
        PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true, // Use buffered queries
    ],
],
</code></pre>

            <h4>Query Builder Optimizations</h4>
            <p>Utilize optimized Query Builder methods like <code>pluck()</code> for retrieving a single column, <code>value()</code> for a single value, and <code>exists()</code> instead of <code>count() > 0</code>.</p>
            <pre><code class="language-php">// Use pluck for retrieving a single column
$userIds = User::where('active', true)->pluck('id');

// Use value for retrieving a single value
$userCount = User::where('active', true)->value('count');

// Use exists instead of count > 0
if (User::where('active', true)->exists()) {
    // ...
}
</code></pre>

            <h4>Eager Loading Strategy</h4>
            <p>Optimize eager loading to fetch relationships efficiently and avoid the N+1 query problem. Use <code>with()</code> to eager load relationships, and consider conditional eager loading or eager loading counts with <code>withCount()</code>.</p>
            <pre><code class="language-php">// Eager load relationships
$users = User::with(['profile', 'orders.items'])->get();

// Conditionally eager load relationships
$users = User::with(['orders' => function ($query) {
    $query->where('status', 'completed');
}])->get();

// Eager load counts
$users = User::withCount(['orders', 'posts'])->get();
</code></pre>

            <h4>Laravel Data Caching</h4>
            <p>Cache frequently accessed data or query results using Laravel's Cache facade to reduce database load.</p>
            <pre><code class="language-php">// Cache user data
$user = Cache::remember('user_' . $userId, now()->addMinutes(10), function () use ($userId) {
    return User::findOrFail($userId);
});

// Cache query results
$activeUsers = Cache::remember('active_users', now()->addHours(1), function () {
    return User::where('active', true)->get();
});
</code></pre>

            <h3>Monitoring and Profiling</h3>
            <p>Regularly monitor and profile your database queries to identify performance bottlenecks.</p>
            <h4>Query Logging</h4>
            <p>Enable MySQL's slow query log to identify queries that take longer than a specified time to execute.</p>
            <pre><code class="language-ini"># my.cnf
[mysqld]
slow_query_log = 1
slow_query_log_file = /var/log/mysql/mysql-slow.log
long_query_time = 1 # Log queries taking longer than 1 second
log_queries_not_using_indexes = 1 # Log queries that don't use indexes
</code></pre>

            <h4>Database Profiling</h4>
            <p>Use MySQL's built-in profiling tools to get detailed information about the execution plan and time spent in different stages of a query.</p>
            <pre><code class="language-sql">-- Enable profiling for the current session
SET profiling = 1;

-- Run your query
SELECT * FROM users WHERE id = 1;

-- Show profile results
SHOW profiles;

-- Show detailed profile for a specific query
SHOW profile FOR query 1;
</code></pre>

            <h4>Laravel Telescope</h4>
            <p>Laravel Telescope provides a convenient and powerful way to monitor database queries within your Laravel application, offering insights into execution time, bindings, and more.</p>
            <pre><code class="language-bash"># Install Telescope
composer require laravel/telescope --dev

# Publish and run migrations
php artisan telescope:install
php artisan migrate
</code></pre>

            <h3>Scaling Strategies</h3>
            <p>For high-traffic or large-scale financial applications, consider database scaling strategies.</p>
            <h4>Read-Write Splitting</h4>
            <p>Configure your application to direct read operations to replica databases and write operations to the primary database. Laravel supports read-write splitting configuration.</p>
            <pre><code class="language-php">// config/database.php with read-write splitting example
'mysql' => [
    'read' => [
        'host' => '***********', // Read replica host
    ],
    'write' => [
        'host' => '***********', // Primary host
    ],
    'sticky' => true, // Optional: keep a sticky connection to the write host for the current request
    // ... other config
],
</code></pre>

            <h4>Sharding for Multi-Tenant</h4>
            <p>For very large multi-tenant applications, sharding can distribute data across multiple database servers based on a sharding key (e.g., tenant ID). This requires careful planning and implementation, often involving custom logic in your application.</p>
            <pre><code class="language-php">// Example sharding logic in a service provider
use App\Models\Tenant;
use App\Observers\TenantObserver;
use Illuminate\Support\Facades\DB;

public function boot()
{
    Tenant::observe(TenantObserver::class);

    Tenant::macro('getConnection', function () {
        $shardKey = $this->id % 8; // Example sharding logic based on tenant ID
        return DB::connection("tenant_shard_{$shardKey}"); // Connect to a specific shard connection
    });
}
</code></pre>

            <h4>Archiving Old Data</h4>
            <p>Periodically archive old financial data that is no longer actively used to a separate database or storage solution. This keeps the primary database smaller and improves query performance on current data. This can be automated with Artisan commands.</p>
            <pre><code class="language-php">// Artisan command to archive old data example
namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Facades\Storage; // Example for storing archived data

class ArchiveOldTransactions extends Command
{
    protected $signature = 'transactions:archive {--days=365}';
    protected $description = 'Archive transactions older than a specified number of days';

    public function handle()
    {
        $days = $this->option('days');
        $cutoffDate = Carbon::now()->subDays($days);

        $this->info("Archiving transactions older than {$cutoffDate->toDateString()}");

        DB::transaction(function () use ($cutoffDate) {
            // Select old transactions
            $oldTransactions = DB::table('transactions')
                ->where('transaction_date', '<', $cutoffDate)
                ->get();

            if ($oldTransactions->isEmpty()) {
                $this->info('No old transactions to archive.');
                return;
            }

            // Insert into archive table (assuming you have an archive_transactions table)
            // Alternatively, save to a file or external storage
            foreach ($oldTransactions as $transaction) {
                 DB::table('archive_transactions')->insert((array) $transaction);
                // Example: Storage::put("archive/transactions/{$transaction->id}.json", json_encode($transaction));
            }

            // Delete from original table
            DB::table('transactions')
                ->where('transaction_date', '<', $cutoffDate)
                ->delete();

            $this->info('Archiving completed.');
        });

        return 0;
    }
}
</code></pre>
        </section>

        <section id="financial-double-accounting-packages">
            <h2>Financial Double Accounting Packages</h2>
            <p>This section compares popular financial double accounting packages for Laravel applications and provides basic usage examples.</p>

            <h3>Package Comparison</h3>
            <p>Several packages are available for implementing double-entry accounting in Laravel. Key factors for comparison include features, ease of use, performance, and community support. Some popular options include Eloquent IFRS, Laravel Ledger, and Scott Laurent's Accounting.</p>

            <h3>Eloquent IFRS</h3>
            <p>Eloquent IFRS is a package that provides a robust framework for implementing IFRS-compliant double-entry accounting.</p>
            <h4>Installation:</h4>
            <pre><code class="language-bash">composer require ekmungai/eloquent-ifrs
php artisan vendor:publish --provider="Ekmungai\IFRS\IFRSServiceProvider"
php artisan migrate
</code></pre>
            <h4>Basic Usage:</h4>
            <p>Creating accounts and journal entries with Eloquent IFRS:</p>
            <pre><code class="language-php">use Ekmungai\IFRS\Models\Account;
use Ekmungai\IFRS\Models\JournalEntry;
use Ekmungai\IFRS\Models\Journal;

// Create accounts
$cash = Account::create(['name' => 'Cash', 'account_type' => 'asset']);
$sales = Account::create(['name' => 'Sales Revenue', 'account_type' => 'revenue']);

// Create a journal entry
$journal = Journal::create(['narration' => 'Sale of goods']);

$journal->journalEntries()->create([
    'account_id' => $cash->id,
    'debit' => 100,
    'credit' => 0,
]);

$journal->journalEntries()->create([
    'account_id' => $sales->id,
    'debit' => 0,
    'credit' => 100,
]);
</code></pre>
            <h4>Financial Reports:</h4>
            <p>Eloquent IFRS provides methods for generating standard financial reports like Trial Balance, Income Statement, and Balance Sheet based on reporting periods.</p>
            <pre><code class="language-php">use Ekmungai\IFRS\Models\ReportingPeriod;

$period = ReportingPeriod::latest()->first();

$trialBalance = $period->getTrialBalance();
$incomeStatement = $period->getIncomeStatement();
$balanceSheet = $period->getBalanceSheet();
</code></pre>

            <h3>Laravel Ledger</h3>
            <p>Laravel Ledger is another package for implementing double-entry accounting, offering a simpler API for common accounting operations.</p>
            <h4>Installation:</h4>
            <pre><code class="language-bash">composer require michael-dyer/laravel-ledger
php artisan vendor:publish --provider="MichaelDyer\LaravelLedger\LedgerServiceProvider" --tag="migrations"
php artisan migrate
</code></pre>
            <h4>Basic Usage:</h4>
            <p>Creating accounts and transactions with Laravel Ledger:</p>
            <pre><code class="language-php">use MichaelDyer\LaravelLedger\Models\Account;
use MichaelDyer\LaravelLedger\Models\Transaction;

// Create accounts
$cash = Account::create(['name' => 'Cash', 'type' => 'asset']);
$sales = Account::create(['name' => 'Sales Revenue', 'type' => 'revenue']);

// Create a transaction
$transaction = Transaction::create(['memo' => 'Sale of goods']);

$transaction->debit($cash, 100);
$transaction->credit($sales, 100);
</code></pre>
            <h4>Running Reports:</h4>
            <p>Laravel Ledger offers a facade for generating reports.</p>
            <pre><code class="language-php">use MichaelDyer\LaravelLedger\Facades\Ledger;

$trialBalance = Ledger::trialBalance();
$incomeStatement = Ledger::incomeStatement();
$balanceSheet = Ledger::balanceSheet();
</code></pre>

            <h3>Scott Laurent's Accounting</h3>
            <p>Scott Laurent's Accounting package provides a flexible approach to double-entry accounting in Laravel.</p>
            <h4>Installation:</h4>
            <pre><code class="language-bash">composer require scottlaurent/accounting
php artisan vendor:publish --provider="Scottlaurent\Accounting\AccountingServiceProvider" --tag="migrations"
php artisan migrate
</code></pre>
            <h4>Basic Usage:</h4>
            <p>Creating accounts and journals with Scott Laurent's Accounting:</p>
            <pre><code class="language-php">use Scottlaurent\Accounting\Models\Account;
use Scottlaurent\Accounting\Models\JournalEntry;
use Scottlaurent\Accounting\Models\Journal;

// Create accounts
$cash = Account::create(['name' => 'Cash', 'account_type' => 'asset']);
$sales = Account::create(['name' => 'Sales Revenue', 'account_type' => 'revenue']);

// Create a journal
$journal = Journal::create(['memo' => 'Sale of goods']);

$journal->credit($sales, 100);
$journal->debit($cash, 100);
</code></pre>
            <h4>Retrieving Data:</h4>
            <p>Retrieve account balances and journal entries using the package's methods.</p>
            <pre><code class="language-php">$cashBalance = $cash->getCurrentBalance();
$journalEntries = $journal->journalEntries;
</code></pre>

            <h3>Integration Strategies</h3>
            <p>Integrating these accounting packages into your Laravel application involves defining your chart of accounts, creating models that interact with the package's accounts and transactions, and building the necessary controllers and services to handle accounting operations.</p>

            <h3>Performance Optimization</h3>
            <p>Optimizing the performance of accounting operations with these packages often involves applying the MySQL optimization techniques discussed earlier, such as proper indexing, efficient queries, and caching, especially when dealing with a large number of transactions.</p>
        </section>

        <section id="financial-app-implementation">
            <h2>Financial Double Accounting Application Implementation</h2>
            <p>This section outlines practical steps and code examples for implementing a financial double accounting system using Laravel 12, React, and MySQL optimization techniques, building upon the concepts discussed in the previous sections.</p>

            <h3>Getting Started</h3>
            <h4>Project Setup</h4>
            <p>Setting up the project involves creating a new Laravel project, installing the React starter kit, and adding a financial accounting package (using Eloquent IFRS as an example here). You will also need to install NPM dependencies and potentially add libraries like React Query.</p>
            <pre><code class="language-bash"># Create a new Laravel 12 project
composer create-project laravel/laravel financial-accounting-app

# Navigate to project directory
cd financial-accounting-app

# Install React starter kit
composer require laravel/breeze --dev
php artisan breeze:install react

# Install financial accounting package (using Eloquent IFRS as example)
composer require ekmungai/eloquent-ifrs

# Publish package assets
php artisan vendor:publish --provider="Ekmungai\IFRS\IFRSServiceProvider"

# Run migrations
php artisan migrate

# Install NPM dependencies
npm install

# Add React Query for efficient data fetching
npm install @tanstack/react-query
</code></pre>

            <h4>Database Configuration</h4>
            <p>Configure your database connection in the <code>.env</code> file and optimize MySQL settings for financial data, including considering connection pooling for production environments in <code>config/database.php</code>.</p>
            <pre><code class="language-ini">DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=financial_app
DB_USERNAME=root
DB_PASSWORD=

# Connection pooling for production
DB_POOL=true
DB_POOL_MIN=5
DB_POOL_MAX=20
</code></pre>
            <pre><code class="language-php">// config/database.php
'mysql' => [
    // ...existing config
    'sticky' => true,
    'pool' => [
        'enabled' => env('DB_POOL', false),
        'min' => env('DB_POOL_MIN', 2),
        'max' => env('DB_POOL_MAX', 10),
    ],
],
</code></pre>

            <h3>Database Schema Design</h3>
            <p>Design your database schema with optimized migrations for financial data, including tables for accounting entities, chart of accounts, accounts, journals, and journal entries. Pay attention to data types and indexing for performance.</p>
            <pre><code class="language-php">// Create accounting_entities table
Schema::create('accounting_entities', function (Blueprint $table) {
    $table->id();
    $table->string('name');
    $table->timestamps();
    $table->index('name');
});

// Create chart_of_accounts table
Schema::create('chart_of_accounts', function (Blueprint $table) {
    $table->id();
    $table->foreignId('entity_id')->constrained('accounting_entities');
    $table->string('name');
    $table->timestamps();
    $table->index(['entity_id', 'name']);
});

// Create accounts table with optimized data types
Schema::create('accounts', function (Blueprint $table) {
    $table->id();
    $table->foreignId('chart_id')->constrained('chart_of_accounts');
    $table->string('code', 20);
    $table->string('name');
    $table->string('type', 20); // asset, liability, equity, revenue, expense
    $table->foreignId('parent_id')->nullable()->constrained('accounts');
    $table->timestamps();

    // Add indexes for frequent queries
    $table->index('code');
    $table->index(['chart_id', 'type']);
    $table->index(['chart_id', 'parent_id']);
});

// Create journals table with date partitioning (consider partitioning for large tables)
Schema::create('journals', function (Blueprint $table) {
    $table->id();
    $table->foreignId('entity_id')->constrained('accounting_entities');
    $table->string('reference', 50);
    $table->string('description');
    $table->date('journal_date');
    $table->timestamps();

    // Add indexes for reporting queries
    $table->index('reference');
    $table->index(['entity_id', 'journal_date']);
});

// Create journal_entries table with optimal indexing
Schema::create('journal_entries', function (Blueprint $table) {
    $table->id();
    $table->foreignId('journal_id')->constrained()->onDelete('cascade');
    $table->foreignId('account_id')->constrained('accounts');
    $table->decimal('amount', 13, 4);
    $table->boolean('is_credit')->default(false);
    $table->timestamps();

    // Add indexes for balance calculations
    $table->index(['account_id', 'is_credit']);
    $table->index(['journal_id', 'account_id']);
});
</code></pre>

            <h3>Model Definitions</h3>
            <p>Create optimized Eloquent models for your accounting entities, chart of accounts, accounts, journals, and journal entries. Include relationships and methods for calculating balances efficiently.</p>
            <pre><code class="language-php">// app/Models/AccountingEntity.php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class AccountingEntity extends Model
{
    protected $fillable = ['name'];

    public function chartOfAccounts()
    {
        return $this->hasOne(ChartOfAccounts::class, 'entity_id');
    }

    public function journals()
    {
        return $this->hasMany(Journal::class, 'entity_id');
    }
}

// app/Models/ChartOfAccounts.php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ChartOfAccounts extends Model
{
    protected $fillable = ['entity_id', 'name'];

    public function entity()
    {
        return $this->belongsTo(AccountingEntity::class, 'entity_id');
    }

    public function accounts()
    {
        return $this->hasMany(Account::class, 'chart_id');
    }
}

// app/Models/Account.php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Carbon\Carbon;

class Account extends Model
{
    protected $fillable = ['chart_id', 'code', 'name', 'type', 'parent_id'];

    public function chart()
    {
        return $this->belongsTo(ChartOfAccounts::class, 'chart_id');
    }

    public function parent()
    {
        return $this->belongsTo(Account::class, 'parent_id');
    }

    public function children()
    {
        return $this->hasMany(Account::class, 'parent_id');
    }

    public function entries()
    {
        return $this->hasMany(JournalEntry::class, 'account_id');
    }

    // Efficiently calculate balance for a date range
    public function balance($startDate = null, $endDate = null)
    {
        $query = $this->entries();

        if ($startDate) {
            $query->whereHas('journal', function ($q) use ($startDate) {
                $q->where('journal_date', '>=', $startDate);
            });
        }

        if ($endDate) {
            $query->whereHas('journal', function ($q) use ($endDate) {
                $q->where('journal_date', '<=', $endDate);
            });
        }

        $credits = (clone $query)->where('is_credit', true)->sum('amount');
        $debits = (clone $query)->where('is_credit', false)->sum('amount');

        // Calculate balance based on account type (Debit balance for Asset/Expense, Credit balance for Liability/Equity/Revenue)
        if (in_array($this->type, ['asset', 'expense'])) {
            return $debits - $credits;
        } else {
            return $credits - $debits;
        }
    }

    // Eager load balance for multiple accounts efficiently
    public static function withBalances($chartId, $date = null)
    {
        return static::where('chart_id', $chartId)
            ->withSum(['entries as credits' => function ($query) use ($date) {
                $query->where('is_credit', true);
                if ($date) {
                    $query->whereHas('journal', function ($q) use ($date) {
                        $q->where('journal_date', '<=', $date);
                    });
                }
            }], 'amount')
            ->withSum(['entries as debits' => function ($query) use ($date) {
                $query->where('is_credit', false);
                if ($date) {
                    $query->whereHas('journal', function ($q) use ($date) {
                        $q->where('journal_date', '<=', $date);
                    });
                }
            }], 'amount');
    }
}

// app/Models/Journal.php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class Journal extends Model
{
    protected $fillable = ['entity_id', 'reference', 'description', 'journal_date'];
    protected $dates = ['journal_date']; // Cast journal_date to Carbon instance

    public function entity()
    {
        return $this->belongsTo(AccountingEntity::class, 'entity_id');
    }

    public function entries()
    {
        return $this->hasMany(JournalEntry::class);
    }

    // Create a balanced journal entry
    public function addEntries(array $entries)
    {
        // Validate that entries balance (total debits must equal total credits)
        $totalDebit = 0;
        $totalCredit = 0;
        foreach ($entries as $entry) {
            $amount = $entry['amount'];
            if (!empty($entry['is_credit'])) {
                $totalCredit += $amount;
            } else {
                $totalDebit += $amount;
            }
        }

        if (abs($totalDebit - $totalCredit) > 0.0001) { // Allow for small floating point differences
            throw new \Exception("Journal entries must balance. Total Debit: {$totalDebit}, Total Credit: {$totalCredit}");
        }

        // Create entries in a transaction to ensure atomicity
        return DB::transaction(function () use ($entries) {
            foreach ($entries as $entry) {
                $this->entries()->create($entry);
            }
            return $this;
        });
    }

    // Helper to post a balanced transaction
    public static function postTransaction($entityId, $reference, $description, $date, array $entries)
    {
        return DB::transaction(function () use ($entityId, $reference, $description, $date, $entries) {
            $journal = static::create([
                'entity_id' => $entityId,
                'reference' => $reference,
                'description' => $description,
                'journal_date' => Carbon::parse($date),
            ]);

            $journal->addEntries($entries);

            return $journal;
        });
    }
}

// app/Models/JournalEntry.php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class JournalEntry extends Model
{
    protected $fillable = ['journal_id', 'account_id', 'amount', 'is_credit'];

    public function journal()
    {
        return $this->belongsTo(Journal::class);
    }

    public function account()
    {
        return $this->belongsTo(Account::class);
    }
}
</code></pre>

            <h3>Service Layer</h3>
            <p>Create a service layer to encapsulate accounting logic, such as creating transactions and generating financial reports. This promotes code organization and reusability.</p>
            <pre><code class="language-php">// app/Services/AccountingService.php
namespace App\Services;

use App\Models\Account;
use App\Models\Journal;
use App\Models\AccountingEntity;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB; // Added DB facade

class AccountingService
{
    /**
     * Create a new transaction with balanced entries
     */
    public function createTransaction(int $entityId, string $reference, string $description,
                                     string $date, array $entries)
    {
        return Journal::postTransaction(
            $entityId,
            $reference,
            $description,
            Carbon::parse($date),
            $entries
        );
    }

    /**
     * Get account balance with caching for performance
     */
    public function getAccountBalance(int $accountId, ?string $date = null)
    {
        $cacheKey = "account_balance_{$accountId}_" . ($date ?? 'all');

        return Cache::remember($cacheKey, now()->addHours(1), function () use ($accountId, $date) {
            $account = Account::findOrFail($accountId);
            return $account->balance($date ? null : Carbon::parse($date));
        });
    }

    /**
     * Generate a trial balance
     */
    public function generateTrialBalance(int $entityId, ?string $date = null)
    {
        $cacheKey = "trial_balance_{$entityId}_" . ($date ?? 'current');
        $cacheTime = now()->addMinutes(30);

        return Cache::remember($cacheKey, $cacheTime, function () use ($entityId, $date) {
            $entity = AccountingEntity::findOrFail($entityId);
            $chartId = $entity->chartOfAccounts->id;

            $accounts = Account::withBalances($chartId, $date ? Carbon::parse($date) : null)
                ->get()
                ->map(function ($account) {
                    // Calculate balance based on account type
                    if (in_array($account->type, ['asset', 'expense'])) {
                        $balance = $account->debits - $account->credits;
                    } else {
                        $balance = $account->credits - $account->debits;
                    }

                    return [
                        'id' => $account->id,
                        'code' => $account->code,
                        'name' => $account->name,
                        'type' => $account->type,
                        'balance' => $balance,
                    ];
                });

            return [
                'date' => $date ?? now()->toDateString(),
                'entity' => $entity->name,
                'accounts' => $accounts,
                'total_debits' => $accounts->where('balance', '>', 0)->sum('balance'),
                'total_credits' => abs($accounts->where('balance', '<', 0)->sum('balance')),
            ];
        });
    }

    /**
     * Generate an income statement
     */
    public function generateIncomeStatement(int $entityId, string $startDate, string $endDate)
    {
        $cacheKey = "income_statement_{$entityId}_{$startDate}_{$endDate}";
        $cacheTime = now()->addMinutes(30);

        return Cache::remember($cacheKey, $cacheTime, function () use ($entityId, $startDate, $endDate) {
            $entity = AccountingEntity::findOrFail($entityId);
            $chartId = $entity->chartOfAccounts->id;

            $start = Carbon::parse($startDate);
            $end = Carbon::parse($endDate);

            $revenueAccounts = Account::where('chart_id', $chartId)
                ->where('type', 'revenue')
                ->get()
                ->map(function ($account) use ($start, $end) {
                    return [
                        'id' => $account->id,
                        'code' => $account->code,
                        'name' => $account->name,
                        'balance' => $account->balance($start, $end),
                    ];
                });

            $expenseAccounts = Account::where('chart_id', $chartId)
                ->where('type', 'expense')
                ->get()
                ->map(function ($account) use ($start, $end) {
                    return [
                        'id' => $account->id,
                        'code' => $account->code,
                        'name' => $account->name,
                        'balance' => $account->balance($start, $end),
                    ];
                });

            $totalRevenue = $revenueAccounts->sum('balance');
            $totalExpenses = $expenseAccounts->sum('balance');
            $netIncome = $totalRevenue - $totalExpenses;

            return [
                'start_date' => $startDate,
                'end_date' => $endDate,
                'entity' => $entity->name,
                'revenue_accounts' => $revenueAccounts,
                'expense_accounts' => $expenseAccounts,
                'total_revenue' => $totalRevenue,
                'total_expenses' => $totalExpenses,
                'net_income' => $netIncome,
            ];
        });
    }

    /**
     * Generate a balance sheet
     */
    public function generateBalanceSheet(int $entityId, ?string $date = null)
    {
        $cacheKey = "balance_sheet_{$entityId}_" . ($date ?? 'current');
        $cacheTime = now()->addMinutes(30);

        return Cache::remember($cacheKey, $cacheTime, function () use ($entityId, $date) {
            $entity = AccountingEntity::findOrFail($entityId);
            $chartId = $entity->chartOfAccounts->id;
            $balanceDate = $date ? Carbon::parse($date) : now();

            $assetAccounts = Account::where('chart_id', $chartId)
                ->where('type', 'asset')
                ->get()
                ->map(function ($account) use ($balanceDate) {
                    return [
                        'id' => $account->id,
                        'code' => $account->code,
                        'name' => $account->name,
                        'balance' => $account->balance(null, $balanceDate),
                    ];
                });

            $liabilityAccounts = Account::where('chart_id', $chartId)
                ->where('type', 'liability')
                ->get()
                ->map(function ($account) use ($balanceDate) {
                    return [
                        'id' => $account->id,
                        'code' => $account->code,
                        'name' => $account->name,
                        'balance' => $account->balance(null, $balanceDate),
                    ];
                });

            $equityAccounts = Account::where('chart_id', $chartId)
                ->where('type', 'equity')
                ->get()
                ->map(function ($account) use ($balanceDate) {
                    return [
                        'id' => $account->id,
                        'code' => $account->code,
                        'name' => $account->name,
                        'balance' => $account->balance(null, $balanceDate),
                    ];
                });

            $totalAssets = $assetAccounts->sum('balance');
            $totalLiabilities = $liabilityAccounts->sum('balance');
            $totalEquity = $equityAccounts->sum('balance');

            return [
                'date' => $date ?? now()->toDateString(),
                'entity' => $entity->name,
                'asset_accounts' => $assetAccounts,
                'liability_accounts' => $liabilityAccounts,
                'equity_accounts' => $equityAccounts,
                'total_assets' => $totalAssets,
                'total_liabilities' => $totalLiabilities,
                'total_equity' => $totalEquity,
                'balancing_equation' => $totalAssets - ($totalLiabilities + $totalEquity), // Should be close to zero
            ];
        });
    }
}
</code></pre>

            <h3>API Controllers</h3>
            <p>Create API controllers to expose accounting functionality to the frontend. These controllers will interact with the service layer to perform operations and retrieve data.</p>
            <pre><code class="language-php">// app/Http/Controllers/Api/AccountingController.php
namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\AccountingService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator; // Added Validator facade

class AccountingController extends Controller
{
    protected $accountingService;

    public function __construct(AccountingService $accountingService)
    {
        $this->accountingService = $accountingService;
    }

    /**
     * Create a new transaction
     */
    public function createTransaction(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'entity_id' => 'required|exists:accounting_entities,id',
            'reference' => 'required|string|max:50',
            'description' => 'required|string',
            'date' => 'required|date',
            'entries' => 'required|array',
            'entries.*.account_id' => 'required|exists:accounts,id',
            'entries.*.amount' => 'required|numeric|min:0.01',
            'entries.*.is_credit' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 400);
        }

        $validated = $validator->validated();

        try {
            $transaction = $this->accountingService->createTransaction(
                $validated['entity_id'],
                $validated['reference'],
                $validated['description'],
                $validated['date'],
                $validated['entries']
            );

            return response()->json($transaction, 201);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 400);
        }
    }

    /**
     * Get account balance
     */
    public function getAccountBalance(Request $request, int $accountId)
    {
        $date = $request->query('date');

        try {
            $balance = $this->accountingService->getAccountBalance($accountId, $date);
            return response()->json(['balance' => $balance]);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 404);
        }
    }

    /**
     * Generate trial balance
     */
    public function getTrialBalance(Request $request, int $entityId)
    {
        $date = $request->query('date');

        try {
            $trialBalance = $this->accountingService->generateTrialBalance($entityId, $date);
            return response()->json($trialBalance);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 404);
        }
    }

    /**
     * Generate income statement
     */
    public function getIncomeStatement(Request $request, int $entityId)
    {
        $validator = Validator::make($request->all(), [
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 400);
        }

        $validated = $validator->validated();

        try {
            $incomeStatement = $this->accountingService->generateIncomeStatement(
                $entityId,
                $validated['start_date'],
                $validated['end_date']
            );
            return response()->json($incomeStatement);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 404);
        }
    }

    /**
     * Generate balance sheet
     */
    public function getBalanceSheet(Request $request, int $entityId)
    {
        $date = $request->query('date');

        try {
            $balanceSheet = $this->accountingService->generateBalanceSheet($entityId, $date);
            return response()->json($balanceSheet);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 404);
        }
    }
}
</code></pre>

            <h3>React Frontend with Optimistic UI</h3>
            <p>Build the React frontend to interact with the Laravel API, implementing optimistic UI updates for a better user experience. Use libraries like Inertia.js and React Query for efficient data handling and seamless integration.</p>
            <pre><code class="language-jsx">// resources/js/Pages/Transactions/Create.jsx example
import React, { useState } from 'react';
import { useForm } from '@inertiajs/inertia-react';
import Layout from '@/Layouts/AuthenticatedLayout'; // Assuming an authenticated layout component

export default function CreateTransaction({ accounts, entities }) {
    const { data, setData, post, processing, errors } = useForm({
        entity_id: '',
        reference: '',
        description: '',
        date: '',
        entries: [{ account_id: '', amount: '', is_credit: false }],
    });

    const handleInputChange = (e) => {
        setData(e.target.name, e.target.value);
    };

    const handleEntryChange = (index, e) => {
        const newEntries = [...data.entries];
        newEntries[index][e.target.name] = e.target.value;
        setData('entries', newEntries);
    };

    const addEntry = () => {
        setData('entries', [...data.entries, { account_id: '', amount: '', is_credit: false }]);
    };

    const removeEntry = (index) => {
        const newEntries = [...data.entries];
        newEntries.splice(index, 1);
        setData('entries', newEntries);
    };

    const handleSubmit = (e) => {
        e.preventDefault();
        post('/api/transactions'); // Post data to the Laravel API endpoint
    };

    return (
        <Layout>
            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div className="p-6 bg-white border-b border-gray-200">
                            <h2>Create Transaction</h2>
                            <form onSubmit={handleSubmit}>
                                {/* Form fields for entity, reference, description, date */}
                                <div>
                                    <label htmlFor="entity_id">Entity:</label>
                                    <select id="entity_id" name="entity_id" value={data.entity_id} onChange={handleInputChange}>
                                        <option value="">Select Entity</option>
                                        {entities.map(entity => (
                                            <option key={entity.id} value={entity.id}>{entity.name}</option>
                                        ))}
                                    </select>
                                    {errors.entity_id && <div>{errors.entity_id}</div>}
                                </div>

                                {/* Add other form fields like reference, description, date */}
                                <div>
                                    <label htmlFor="reference">Reference:</label>
                                    <input type="text" id="reference" name="reference" value={data.reference} onChange={handleInputChange} />
                                    {errors.reference && <div>{errors.reference}</div>}
                                </div>

                                <div>
                                    <label htmlFor="description">Description:</label>
                                    <textarea id="description" name="description" value={data.description} onChange={handleInputChange}></textarea>
                                    {errors.description && <div>{errors.description}</div>}
                                </div>

                                <div>
                                    <label htmlFor="date">Date:</label>
                                    <input type="date" id="date" name="date" value={data.date} onChange={handleInputChange} />
                                    {errors.date && <div>{errors.date}</div>}
                                </div>


                                <h3>Journal Entries</h3>
                                {data.entries.map((entry, index) => (
                                    <div key={index}>
                                        <label htmlFor={`account_id_${index}`}>Account:</label>
                                        <select id={`account_id_${index}`} name="account_id" value={entry.account_id} onChange={(e) => handleEntryChange(index, e)}>
                                            <option value="">Select Account</option>
                                            {accounts.map(account => (
                                                <option key={account.id} value={account.id}>{account.name}</option>
                                            ))}
                                        </select>
                                        {errors[`entries.${index}.account_id`] && <div>{errors[`entries.${index}.account_id`]}</div>}

                                        <label htmlFor={`amount_${index}`}>Amount:</label>
                                        <input type="number" id={`amount_${index}`} name="amount" value={entry.amount} onChange={(e) => handleEntryChange(index, e)} step="0.01" />
                                        {errors[`entries.${index}.amount`] && <div>{errors[`entries.${index}.amount`]}</div>}

                                        <label htmlFor={`is_credit_${index}`}>
                                            <input type="checkbox" id={`is_credit_${index}`} name="is_credit" checked={entry.is_credit} onChange={(e) => handleEntryChange(index, e)} />
                                            Credit
                                        </label>
                                        {errors[`entries.${index}.is_credit`] && <div>{errors[`entries.${index}.is_credit`]}</div>}

                                        <button type="button" onClick={() => removeEntry(index)}>Remove</button>
                                    </div>
                                ))}
                                <button type="button" onClick={addEntry}>Add Entry</button>

                                <button type="submit" disabled={processing}>
                                    {processing ? 'Saving...' : 'Save Transaction'}
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </Layout>
    );
}
</code></pre>

            <h3>Optimized Background Processing</h3>
            <p>For tasks that may take a long time, such as generating large financial reports, use Laravel queues to process them in the background. This prevents long-running HTTP requests and improves application responsiveness.</p>
            <pre><code class="language-php">// app/Jobs/GenerateFinancialReport.php example
namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Services\AccountingService;
use App\Models\User;
use Illuminate\Support\Facades\Storage; // Added Storage facade

class GenerateFinancialReport implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $reportType;
    protected $entityId;
    protected $params;
    protected $user;

    /**
     * Create a new job instance.
     */
    public function __construct(string $reportType, int $entityId, array $params, User $user)
    {
        $this->reportType = $reportType;
        $this->entityId = $entityId;
        $this->params = $params;
        $this->user = $user;
    }

    /**
     * Execute the job.
     */
    public function handle(AccountingService $accountingService): void
    {
        $reportData = [];

        switch ($this->reportType) {
            case 'trial_balance':
                $reportData = $accountingService->generateTrialBalance($this->entityId, $this->params['date'] ?? null);
                break;
            case 'income_statement':
                $reportData = $accountingService->generateIncomeStatement($this->entityId, $this->params['start_date'], $this->params['end_date']);
                break;
            case 'balance_sheet':
                $reportData = $accountingService->generateBalanceSheet($this->entityId, $this->params['date'] ?? null);
                break;
            // Add other report types as needed
        }

        // Store the report data or notify the user
        // For example, save to a file and email the user
        $reportFileName = "report_{$this->reportType}_{$this->entityId}_" . now()->format('Ymd_His') . '.json';
        Storage::put("reports/{$reportFileName}", json_encode($reportData));

        // Notify user (e.g., via email or database notification)
        // $this->user->notify(new ReportGenerated($reportFileName));
    }
}
</code></pre>
            <p>Dispatch the job from a controller or service:</p>
            <pre><code class="language-php">use App\Jobs\GenerateFinancialReport;
use App\Models\User;

$user = User::find(auth()->id()); // Get the authenticated user
$entityId = 1; // Example entity ID
$reportParams = ['date' => '2024-12-31']; // Example report parameters

GenerateFinancialReport::dispatch('trial_balance', $entityId, $reportParams, $user);
</code></pre>

            <h3>Deployment Considerations</h3>
            <p>When deploying the application, consider server configuration, database scaling (read replicas, sharding), caching mechanisms (Redis, Memcached), and queue workers for background processing.</p>

            <h3>Monitoring and Optimization</h3>
            <p>Implement continuous monitoring of application and database performance. Use tools like Laravel Telescope, slow query logs, and database profiling to identify and address performance bottlenecks. Regularly review and optimize database indexes and queries.</p>
        </section>

        <section id="conclusion">
            <h2>Conclusion</h2>
            <p>This guide has provided a comprehensive overview of leveraging Laravel 12 features, integrating React for a dynamic frontend, optimizing MySQL for performance with large financial datasets, and implementing double-entry accounting using available packages. By following the principles and techniques outlined, developers can build efficient, scalable, and robust financial applications with Laravel 12.</p>
        </section>
    </main>

    <footer>
        <p>&copy; 2024 Comprehensive Guide</p>
    </footer>

    <script src="script.js"></script>
</body>
</html>