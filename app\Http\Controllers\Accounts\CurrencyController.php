<?php
namespace App\Http\Controllers\Accounts;

use App\Models\Currency;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\Controller;

class CurrencyController extends Controller
{
    // GET /api/currencies
    public function index(): JsonResponse
    {
        return response()->json(Currency::all());
    }

    // POST /api/currencies
    public function store(Request $request): JsonResponse
    {
        $authError = $this->authorizeRole(['Admin', 'Editor'], 'create currencies');
        if ($authError) {
            return response()->json(['message' => $authError['message']], $authError['status']);
        }

        $data = $request->validate([
            'name' => 'required|string|max:255|unique:currencies,name',
            'code' => 'required|string|max:10|unique:currencies,code',
        ]);
        
        $data['user_id'] = $request->user()->id;
        $currency = Currency::create($data);
        return response()->json($currency, 201);
    }

    // PUT /api/currencies/{id}
    public function update(Request $request, Currency $currency): JsonResponse
    {
        $authError = $this->authorizeRole(['Admin', 'Editor'], 'update currencies');
        if ($authError) {
            return response()->json(['message' => $authError['message']], $authError['status']);
        }

        $data = $request->validate([
            'name' => 'required|string|max:255|unique:currencies,name,' . $currency->id,
            'code' => 'required|string|max:10|unique:currencies,code,' . $currency->id,
        ]);
        
        $currency->update($data);
        return response()->json($currency);
    }

    // DELETE /api/currencies/{id}
    public function destroy(Currency $currency): JsonResponse
    {
        $authError = $this->authorizeRole(['Admin'], 'delete currencies');
        if ($authError) {
            return response()->json(['message' => $authError['message']], $authError['status']);
        }

        // Prevent deletion if currency is in use
        if ($currency->accounts()->exists()) {
            return response()->json([
                'message' => 'Cannot delete currency. It is being used by one or more accounts.'
            ], 422);
        }

        $currency->delete();
        return response()->json(['message' => 'Currency deleted successfully.']);
    }
    
    // Helper for role-based authorization
    protected function authorizeRole(array $roles, string $action = 'perform this action')
    {
        $user = Auth::user();
        if (!$user) {
            return ['message' => 'You must be logged in to ' . $action . '.', 'status' => 401];
        }
        if (!in_array($user->role, $roles)) {
            $requiredRoles = implode(' or ', $roles);
            $message = 'Unauthorized. Only users with the ' . $requiredRoles . ' role can ' . $action . '.';
            if ($user->role === 'Viewer') {
                $message = 'Unauthorized. Viewers cannot ' . $action . '.';
            }
            return ['message' => $message, 'status' => 403];
        }
        return null;
    }
}