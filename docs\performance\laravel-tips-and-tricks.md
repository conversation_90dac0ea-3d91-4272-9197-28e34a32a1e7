# Laravel 12 Tips and Best Practices

## Table of Contents
1. [Database Optimization](#database-optimization)
2. [Eloquent Performance](#eloquent-performance)
3. [Query Optimization](#query-optimization)
4. [Real-time Features](#real-time-features)
5. [Code Organization](#code-organization)

## Database Optimization

### Database Structure Best Practices
1. **Plan Before Implementation**
   - Analyze query patterns before designing
   - Consider future scalability
   - Discuss with business stakeholders
   - Document expected data growth

2. **Table Design Decisions**
   ```php
   // Consider JSON columns for flexible attributes
   Schema::create('products', function (Blueprint $table) {
       $table->id();
       $table->string('name');
       $table->json('attributes')->nullable(); // For dynamic attributes
       $table->timestamps();
   });
   ```

3. **Relationship Optimization**
   ```php
   // Choose the right relationship type
   class Product extends Model
   {
       // For one-to-many: More efficient than many-to-many
       public function categories()
       {
           return $this->belongsTo(Category::class);
       }

       // For many-to-many: When necessary
       public function tags()
       {
           return $this->belongsToMany(Tag::class)
               ->withTimestamps()
               ->using(ProductTag::class); // Custom pivot model
       }
   }
   ```

## Eloquent Performance

### 1. N+1 Query Prevention
```php
// BAD: N+1 Query Problem
$users = User::all();
foreach ($users as $user) {
    echo $user->profile->name;
}

// GOOD: Eager Loading
$users = User::with('profile')->get();

// BETTER: Select Specific Columns
$users = User::with(['profile' => function($query) {
    $query->select('id', 'user_id', 'name');
}])->get();
```

### 2. Chunking and Lazy Loading
```php
// For large datasets, use lazy loading
User::lazy()->each(function ($user) {
    // Process user
});

// For batch processing
User::chunk(1000, function ($users) {
    foreach ($users as $user) {
        // Process in batches
    }
});

// Using cursor for memory efficiency
foreach (User::where('active', true)->cursor() as $user) {
    // Process one at a time
}
```

### 3. Query Optimization Techniques
```php
// Use whereIn instead of multiple queries
$users = User::whereIn('id', [1, 2, 3])->get();

// Use joins instead of whereHas for better performance
$users = User::join('posts', 'users.id', '=', 'posts.user_id')
    ->where('posts.published', true)
    ->select('users.*')
    ->distinct()
    ->get();

// Use subqueries for complex conditions
$users = User::addSelect(['last_post' => Post::select('title')
    ->whereColumn('user_id', 'users.id')
    ->latest()
    ->limit(1)
])->get();
```

## Real-time Features

### 1. Laravel Reverb (New WebSocket Server)
```php
// config/broadcasting.php
'reverb' => [
    'driver' => 'reverb',
    'app_id' => env('REVERB_APP_ID'),
    'key' => env('REVERB_APP_KEY'),
    'secret' => env('REVERB_APP_SECRET'),
    'path' => env('REVERB_PATH', 'reverb'),
    'host' => env('REVERB_HOST', '127.0.0.1'),
    'port' => env('REVERB_PORT', 8080),
],

// Broadcasting an event
class TaskCompleted implements ShouldBroadcast
{
    public function broadcastOn(): array
    {
        return [new Channel('tasks')];
    }
}
```

### 2. Redis Integration
```php
// Redis for caching and queues
'redis' => [
    'client' => env('REDIS_CLIENT', 'phpredis'),
    'default' => [
        'host' => env('REDIS_HOST', '127.0.0.1'),
        'password' => env('REDIS_PASSWORD'),
        'port' => env('REDIS_PORT', 6379),
        'database' => env('REDIS_DB', 0),
    ],
    'cache' => [
        'host' => env('REDIS_HOST', '127.0.0.1'),
        'password' => env('REDIS_PASSWORD'),
        'port' => env('REDIS_PORT', 6379),
        'database' => env('REDIS_CACHE_DB', 1),
    ],
],
```

## Code Organization

### 1. Service Pattern Implementation
```php
// app/Services/OrderService.php
class OrderService
{
    public function __construct(
        private readonly OrderRepository $repository,
        private readonly PaymentGateway $paymentGateway
    ) {}

    public function createOrder(array $data): Order
    {
        DB::transaction(function () use ($data) {
            $order = $this->repository->create($data);
            $this->paymentGateway->process($order);
            event(new OrderCreated($order));
            return $order;
        });
    }
}
```

### 2. Custom Exception Handling
```php
// app/Exceptions/CustomExceptionHandler.php
class Handler extends ExceptionHandler
{
    public function register(): void
    {
        $this->reportable(function (OrderException $e) {
            Log::channel('orders')->error($e->getMessage(), [
                'order_id' => $e->orderId,
                'trace' => $e->getTraceAsString()
            ]);
        });

        $this->renderable(function (Exception $e) {
            if ($e instanceof ValidationException) {
                return response()->json([
                    'message' => 'Validation failed',
                    'errors' => $e->errors()
                ], 422);
            }
        });
    }
}
```

### 3. Form Request Validation
```php
class CreateOrderRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'customer_id' => ['required', 'exists:customers,id'],
            'items' => ['required', 'array', 'min:1'],
            'items.*.product_id' => ['required', 'exists:products,id'],
            'items.*.quantity' => ['required', 'integer', 'min:1'],
        ];
    }

    public function messages(): array
    {
        return [
            'items.*.product_id.exists' => 'One or more products do not exist.',
        ];
    }
}
```

### 4. API Resource Transformations
```php
class OrderResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'total' => $this->formatted_total,
            'status' => $this->status->value,
            'items' => OrderItemResource::collection($this->whenLoaded('items')),
            'customer' => new CustomerResource($this->whenLoaded('customer')),
            'created_at' => $this->created_at->toISOString(),
            $this->mergeWhen($request->user()->isAdmin(), [
                'profit_margin' => $this->profit_margin,
                'cost' => $this->total_cost,
            ]),
        ];
    }
}
``` 