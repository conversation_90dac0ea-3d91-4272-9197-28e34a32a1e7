<?php
namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class AccountTypeSeeder extends Seeder
{
    public function run(): void
    {
        $types = [
            ['name' => 'customer', 'label' => 'Customer'],
            ['name' => 'bank', 'label' => 'Bank'],
            ['name' => 'expense', 'label' => 'Expense'],
            ['name' => 'chq_ref_bank', 'label' => 'CHQ Ref Bank'],
            ['name' => 'currency_account', 'label' => 'Currency Account'],
            ['name' => 'employee', 'label' => 'Employee'],
            ['name' => 'currency', 'label' => 'Currency'],
            ['name' => 'cash', 'label' => 'Cash'],
            ['name' => 'income', 'label' => 'Income'],
            ['name' => 'other', 'label' => 'Other'],
            ['name' => 'partnership', 'label' => 'Partnership'],
            ['name' => 'personal', 'label' => 'Personal'],
            ['name' => 'property', 'label' => 'Property'],
            ['name' => 'stock', 'label' => 'Stock'],
        ];
        DB::table('account_types')->insert($types);
    }
}
