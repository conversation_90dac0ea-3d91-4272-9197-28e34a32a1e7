import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ComboBox } from '@/components/ui/combobox';
import { DatePicker } from '@/components/ui/date-picker';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { BreadcrumbItem } from '@/types';
import axios from 'axios';
import { useEffect, useState } from 'react';
import { NumericFormat } from 'react-number-format';
import { toast } from 'sonner';
import { Head } from '@inertiajs/react';

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Bank Payment Entry', href: '/bank-payment-entry' },
];

interface Account {
    id: number;
    name: string;
    description?: string;
    account_type?: { // Add account_type to the interface
        id: number;
        name: string;
    };
}

interface ComboBoxOption {
    value: string;
    label: string;
}

export default function BankPaymentEntry() { // Renamed component
    const [date, setDate] = useState<Date | undefined>(new Date());
    const [loading, setLoading] = useState(false);
    // Removed paymentType state
    const [creditAccount, setCreditAccount] = useState('');
    const [debitAccount, setDebitAccount] = useState('');
    const [description, setDescription] = useState('');
    const [chqNo, setChqNo] = useState('');
    const [amount, setAmount] = useState('');
    const [allAccounts, setAllAccounts] = useState<Account[]>([]); // Store all accounts
    const [filteredCreditAccounts, setFilteredCreditAccounts] = useState<ComboBoxOption[]>([]); // Filtered accounts for Credit
    const [filteredDebitAccounts, setFilteredDebitAccounts] = useState<ComboBoxOption[]>([]); // Filtered accounts for Debit
    // Removed paymentTypes state

    // Hardcoded payment type ID for 'bank-payment'
    // TODO: Replace with actual ID fetched from backend or config if necessary
    const BANK_PAYMENT_TYPE_ID = '3';

    // Fetch accounts
    useEffect(() => {
        const fetchData = async () => {
            try {
                const accountsRes = await axios.get<Account[]>('/api/accounts');

                // Transform accounts data
                setAllAccounts(accountsRes.data);

            } catch (err) {
                console.error('Error fetching data:', err);
                toast.error('Failed to load data');
            }
        };

        fetchData();
    }, []);

    // Effect to filter accounts based on hardcoded payment type
    useEffect(() => {
        const allAccountsOptions = allAccounts.map((account) => ({
            value: account.id.toString(),
            label: account.name,
        }));

        let creditAccounts = allAccountsOptions;
        let debitAccounts = allAccountsOptions;

        // Filtering logic for 'bank payment' based on multiple.tsx
        creditAccounts = allAccounts
            .filter(account => account.account_type?.name === 'bank')
            .map(account => ({
                value: account.id.toString(),
                label: account.name,
            }));
        // Debit accounts remain unfiltered

        setFilteredCreditAccounts(creditAccounts);
        setFilteredDebitAccounts(debitAccounts);

    }, [allAccounts]); // Re-run when allAccounts change

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        // Removed paymentType check
        if (!date || !creditAccount || !debitAccount || !amount) {
            toast.error('Please fill in all required fields');
            return;
        }

        // Helper to format date as YYYY-MM-DD in local time
        const formatDateLocal = (d: Date) => {
            const year = d.getFullYear();
            const month = (d.getMonth() + 1).toString().padStart(2, '0');
            const day = d.getDate().toString().padStart(2, '0');
            return `${year}-${month}-${day}`;
        };

        setLoading(true);
        try {
            await axios.post('/api/journal-entries', {
                date: formatDateLocal(date),
                payment_type_id: BANK_PAYMENT_TYPE_ID, // Use hardcoded ID
                credit_account_id: creditAccount,
                debit_account_id: debitAccount,
                description,
                chq_no: chqNo,
                amount: parseFloat(amount.replace(/,/g, '')),
            });

            toast.success('Journal entry created successfully');
            // Reset form
            setDate(new Date());
            // Removed paymentType reset
            setCreditAccount('');
            setDebitAccount('');
            setDescription('');
            setChqNo('');
            setAmount('');
        } catch (err) {
            console.error('Error creating journal entry:', err);
            toast.error('Failed to create journal entry');
        } finally {
            setLoading(false);
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Bank Payment Entry" /> {/* Updated title */}
            <div className="container mx-auto py-6">
                <Card>
                    <CardHeader>
                        <CardTitle>Bank Payment Entry</CardTitle> {/* Updated title */}
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSubmit} className="max-w-2xl space-y-4">
                            <div className="space-y-4">
                                <div className="grid grid-cols-12 items-center gap-4">
                                    <label className="col-span-3 text-right">Date</label>
                                    <div className="col-span-6 lg:col-span-3">
                                        <DatePicker selected={date} onSelect={setDate} />
                                    </div>
                                </div>

                                {/* Removed Payment Type ComboBox */}

                                <div className="grid grid-cols-12 items-center gap-4">
                                    <label className="col-span-3 text-right">Credit Account</label>
                                    <div className="col-span-6">
                                        <ComboBox
                                            value={creditAccount}
                                            onChange={setCreditAccount}
                                            options={filteredCreditAccounts} // Use filtered accounts
                                            placeholder="Select credit account"
                                        />
                                    </div>
                                </div>

                                <div className="grid grid-cols-12 items-center gap-4">
                                    <label className="col-span-3 text-right">Debit Account</label>
                                    <div className="col-span-6">
                                        <ComboBox
                                            value={debitAccount}
                                            onChange={setDebitAccount}
                                            options={filteredDebitAccounts} // Use filtered accounts
                                            placeholder="Select debit account"
                                        />
                                    </div>
                                </div>

                                <div className="grid grid-cols-12 items-center gap-4">
                                    <label className="col-span-3 text-right">Description</label>
                                    <div className="col-span-6">
                                        <Textarea
                                            value={description}
                                            onChange={(e) => setDescription(e.target.value)}
                                            placeholder="Enter description"
                                        />
                                    </div>
                                </div>

                                <div className="grid grid-cols-12 items-center gap-4">
                                    <label className="col-span-3 text-right">CHQ NO</label>
                                    <div className="col-span-6">
                                        <Input
                                            value={chqNo}
                                            onChange={(e) => setChqNo(e.target.value)}
                                            placeholder="Enter CHQ NO"
                                        />
                                    </div>
                                </div>

                                <div className="grid grid-cols-12 items-center gap-4">
                                    <label className="col-span-3 text-right">Amount</label>
                                    <div className="col-span-6">
                                        <NumericFormat
                                            value={amount}
                                            onValueChange={(values) => setAmount(values.value)}
                                            thousandSeparator={true}
                                            // decimalScale={2}
                                            // fixedDecimalScale={true}
                                            allowNegative={false}
                                            customInput={Input}
                                            placeholder="Enter amount"
                                        />
                                    </div>
                                </div>

                                <div className="grid grid-cols-12 items-center gap-4">
                                    <div className="col-span-9 col-start-4">
                                        <Button type="submit" disabled={loading}>
                                            {loading ? 'Submitting...' : 'Submit Entry'}
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}