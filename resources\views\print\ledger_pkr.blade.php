<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Print Ledger Report - {{ $accountName }}</title>
    <style>
        body {
            font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
            font-size: 10px;
            color: #333;
            line-height: 1.4;
            margin: 10px; /* Add some margin for printing by default */
        }
        .container {
            width: 100%;
            margin: 0 auto;
            padding: 10px;
        }
        .header-container {
            width: 100%;
            border-bottom: 2px solid #333; /* A stronger border */
            padding-bottom: 10px;
            margin-bottom: 20px;
            overflow: auto; /* To contain floats */
        }

        .header-main {
            display: table; /* Using table display for robust two-column layout */
            width: 100%;
        }

        .company-title {
            display: table-cell;
            width: 60%; /* Adjust width as needed */
            vertical-align: top;
        }

        .company-title h1 {
            margin: 0 0 5px 0;
            font-size: 20px; /* Slightly larger */
            font-weight: bold;
            color: #111;
        }

        .company-title h2 {
            margin: 0;
            font-size: 16px; /* Slightly larger */
            color: #444;
            font-weight: normal;
        }

        .report-details {
            display: table-cell;
            width: 40%; /* Adjust width as needed */
            vertical-align: top;
            text-align: right; /* Align details to the right */
        }

        .report-details p {
            margin: 0 0 3px 0;
            font-size: 9px; /* Keep details font size small for compactness */
            color: #555;
        }
        .report-details p strong {
            color: #333;
        }
        .report-meta { /* Added for better visual grouping of generated date */
            background-color: #f8f9fa;
            padding: 5px 8px;
            border-radius: 3px;
            margin-top: 5px;
            display: inline-block; /* To make it wrap content */
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 6px;
            text-align: left;
        }
        th {
            background-color: #f0f0f0;
            font-weight: bold;
            font-size: 10px;
        }
        td {
            font-size: 9px;
        }
        .text-right {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .footer {
            text-align: center;
            font-size: 8px;
            color: #888;
            margin-top: 20px;
            border-top: 1px solid #eee;
            padding-top: 10px;
        }
        .balance-dr {
            color: #d9534f; /* Red for Debit */
        }
        .balance-cr {
            color: #5cb85c; /* Green for Credit */
        }
        .opening-balance-row td {
            font-weight: bold;
            background-color: #f9f9f9;
        }
        
        /* New styles for totals section */
        .totals-section {
            margin-top: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .totals-table {
            width: 100%;
            margin-bottom: 0;
        }
        
        .totals-table th {
            background-color: #f5f5f5;
            font-size: 10px;
            text-align: left;
        }
        
        .totals-table td {
            font-size: 10px;
            padding: 8px;
        }
        
        .totals-table .value-cell {
            text-align: right;
            font-weight: bold;
        }
        
        .watermark {
            position: fixed;
            bottom: 40%;
            left: 0;
            width: 100%;
            text-align: center;
            opacity: 0.06;
            transform: rotate(-45deg);
            font-size: 100px;
            font-weight: bold;
            z-index: -1;
        }
        
        @media print {
            body {
                margin: 0; /* Remove margin for actual printing */
                font-size: 9pt; /* Adjust font size for print */
            }
            .header-container { /* Ensure print styles apply to new header */
                border-bottom: 2px solid #333;
                margin-bottom: 15px; /* Adjust as needed for print */
                padding-bottom: 5px;
            }
            .company-title h1 { font-size: 18pt; } /* Adjust print font sizes */
            .company-title h2 { font-size: 14pt; }
            .report-details p { font-size: 8pt; }
            .report-meta p { font-size: 8pt; }

            th { font-size: 9pt; }
            td { font-size: 8pt; }
            .no-print { display: none; } /* Hide elements not for printing */
            
            .totals-table th, .totals-table td { font-size: 9pt; }
            .footer { font-size: 7pt; }
            
            /* Ensure page breaks don't occur within rows */
            tr { page-break-inside: avoid; }
            
            /* Add page numbers */
            @page {
                margin: 1cm;
                @bottom-right {
                    content: "Page " counter(page) " of " counter(pages);
                }
            }
        }
    </style>
</head>
<body>
    <!-- Optional Watermark -->
    @if(isset($watermark) && $watermark)
    <div class="watermark">{{ $companyName }}</div>
    @endif
    
    <div class="container">
        <div class="header-container">
            <div class="header-main">
                <div class="company-title">
                    <h1>{{ $companyName }}</h1>
                    <h2>Ledger Report</h2>
                </div>
                <div class="report-details">
                    <p><strong>Account:</strong> {{ $accountName }}</p>
                    <div class="report-meta">
                        <p><strong>Period:</strong> {{ $fromDate }} to {{ $tillDate }}</p>
                        <!-- <p>Report Generated: {{ $reportDate }}</p> -->
                    </div>
                </div>
            </div>
        </div>

        <table>
            <thead>
                <tr>
                    <th>TID</th>
                    <th>Type</th>
                    <th>Description</th>
                    <th>Inv#</th>
                    <th>Chq No</th>
                    <th class="text-right">Debit</th>
                    <th class="text-right">Credit</th>
                    <th class="text-right">Balance</th>
                </tr>
            </thead>
            <tbody>
                @forelse($ledgerEntries as $entry)
                    @if(isset($entry->is_date_header) && $entry->is_date_header)
                        <tr style="background-color: #e2e8f0; font-weight: bold;">
                        <td colspan="8">{{ \Carbon\Carbon::parse($entry->date)->format('d-M-Y (l)') }}</td>
                        </tr>
                    @else
                        <tr>
                            <td>{{ $entry->TID }}</td>
                            <td style="text-transform: capitalize; text-align: center;">{{ $entry->payment_type_name }}</td>
                            <td>
                                @if($withPartyName)
                                    {{ $entry->description }}
                                @else
                                    {{ preg_replace('/\b(?:to|from|by|for)\s+\w+(?:\s+\w+)*(?=\s+(?:for|by|invoice|payment|receipt|bill|ref))/i', '', $entry->description) }}
                                @endif
                            </td>
                            <td>{{ $entry->inv_no }}</td>
                            <td>{{ $entry->chq_no }}</td>
                            <td class="text-right">{{ $entry->debit > 0 ? number_format($entry->debit, 2) : '-' }}</td>
                            <td class="text-right">{{ $entry->credit > 0 ? number_format($entry->credit, 2) : '-' }}</td>
                            <td class="text-right {{ $entry->balance >= 0 ? 'balance-dr' : 'balance-cr' }}">
                                {{ number_format(abs($entry->balance), 2) }} {{ $entry->balance >= 0 ? 'Dr' : 'Cr' }}
                            </td>
                        </tr>
                    @endif
                @empty
                    <tr>
                        <td colspan="8" class="text-center">No transactions found for the selected period.</td>
                    </tr>
                @endforelse
            </tbody>
        </table>

        <!-- Totals Section -->
        <div class="totals-section">
            <table class="totals-table">
                <tr>
                    <th width="15%">Total Debit:</th>
                    <td width="55%" style="text-align: left;" class="value-cell {{ isset($totals) && $totals['totalDr'] > 0 ? 'balance-dr' : '' }}">
                        {{ isset($totals) ? number_format($totals['totalDr'], 2) : '0.00' }} Dr
                    </td>
                    <th width="15%">Balance:</th>
                    <td width="15%" class="value-cell {{ isset($totals) && $totals['balance'] >= 0 ? 'balance-dr' : 'balance-cr' }}">
                        {{ isset($totals) ? number_format(abs($totals['balance']), 2) : '0.00' }} 
                        {{ isset($totals) && $totals['balance'] >= 0 ? 'Dr' : 'Cr' }}
                    </td>
                </tr>
                <tr>
                    <th>Total Credit:</th>
                    <td style="text-align: left;" class="value-cell">
                        {{ isset($totals) ? number_format($totals['totalCr'], 2) : '0.00' }} Cr
                    </td>
                    <th>CHQ Inward:</th>
                    <td class="value-cell {{ isset($totals) && $totals['chequeInwardTotal'] > 0 ? 'balance-dr' : 'balance-cr' }}">
                        {{ isset($totals) ? number_format(abs($totals['chequeInwardTotal']), 2) : '0.00' }}
                        {{ isset($totals) && $totals['chequeInwardTotal'] < 0 ? ' Cr' : ' Dr' }}
                    </td>
                </tr>
                <tr>
                    <th>CHQ(IW) Count:</th>
                    <td style="text-align: left;" class="value-cell">
                        {{ isset($totals) ? $totals['chequeInwardCount'] : '0' }}
                    </td>
                    <th>CHQ Outward:</th>
                    <td class="value-cell {{ isset($totals) && $totals['chequeOutwardTotal'] > 0 ? 'balance-dr' : 'balance-cr' }}">
                        {{ isset($totals) ? number_format($totals['chequeOutwardTotal'], 2) : '0.00' }}
                        {{ isset($totals) && $totals['chequeOutwardTotal'] > 0 ? ' Dr' : ' Cr' }}
                    </td>
                </tr>
                <tr>
                    <th>CHQ(OW) Count:</th>
                    <td style="text-align: left;" class="value-cell">
                        {{ isset($totals) ? $totals['chequeOutwardCount'] : '0' }}
                    </td>
                    <th>Final Total:</th>
                    <td class="value-cell {{ isset($totals) && $totals['finalTotal'] > 0 ? 'balance-dr' : 'balance-cr' }}">
                        {{ isset($totals) ? number_format(abs($totals['finalTotal']), 2) : '0.00' }}
                        {{ isset($totals) && $totals['finalTotal'] >= 0 ? ' Dr' : ' Cr' }}
                    </td>
                </tr>
            </table>
        </div>

        <div class="footer">
            <p class="no-print"><button onclick="window.print()">Print this page</button></p>
        </div>
    </div>
    
    <script>
        // Auto-print when the page loads in print mode
        window.onload = function() {
            // Small delay to ensure everything is rendered
            setTimeout(function() {
                window.print();
            }, 500);
        };
    </script>
</body>
</html>
