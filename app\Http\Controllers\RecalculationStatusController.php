<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class RecalculationStatusController extends Controller
{
    /**
     * Get recalculation status for a specific account
     */
    public function getAccountStatus(Request $request)
    {
        $request->validate([
            'account_id' => 'required|integer',
            'from_date' => 'nullable|date'
        ]);

        $query = DB::table('recalculation_logs')
            ->where('account_id', $request->account_id);

        if ($request->filled('from_date')) {
            $query->where('from_date', '>=', $request->from_date);
        }

        $logs = $query->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        $pendingCount = DB::table('recalculation_logs')
            ->where('account_id', $request->account_id)
            ->where('status', 'pending')
            ->count();

        $processingCount = DB::table('recalculation_logs')
            ->where('account_id', $request->account_id)
            ->where('status', 'processing')
            ->count();

        return response()->json([
            'account_id' => $request->account_id,
            'pending_jobs' => $pendingCount,
            'processing_jobs' => $processingCount,
            'is_busy' => ($pendingCount + $processingCount) > 0,
            'recent_logs' => $logs->map(function ($log) {
                return [
                    'id' => $log->id,
                    'from_date' => $log->from_date,
                    'trigger_type' => $log->trigger_type,
                    'description' => $log->description,
                    'status' => $log->status,
                    'error_message' => $log->error_message,
                    'created_at' => Carbon::parse($log->created_at)->format('Y-m-d H:i:s'),
                    'updated_at' => Carbon::parse($log->updated_at)->format('Y-m-d H:i:s'),
                ];
            })
        ]);
    }

    /**
     * Get overall system recalculation status
     */
    public function getSystemStatus()
    {
        $stats = DB::table('recalculation_logs')
            ->select(
                DB::raw('COUNT(*) as total_jobs'),
                DB::raw('SUM(CASE WHEN status = "pending" THEN 1 ELSE 0 END) as pending_jobs'),
                DB::raw('SUM(CASE WHEN status = "processing" THEN 1 ELSE 0 END) as processing_jobs'),
                DB::raw('SUM(CASE WHEN status = "completed" THEN 1 ELSE 0 END) as completed_jobs'),
                DB::raw('SUM(CASE WHEN status = "failed" THEN 1 ELSE 0 END) as failed_jobs')
            )
            ->where('created_at', '>=', now()->subDays(7)) // Last 7 days
            ->first();

        $recentFailures = DB::table('recalculation_logs')
            ->where('status', 'failed')
            ->where('created_at', '>=', now()->subHours(24))
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        return response()->json([
            'system_busy' => ($stats->pending_jobs + $stats->processing_jobs) > 0,
            'statistics' => [
                'total_jobs_7_days' => $stats->total_jobs,
                'pending_jobs' => $stats->pending_jobs,
                'processing_jobs' => $stats->processing_jobs,
                'completed_jobs' => $stats->completed_jobs,
                'failed_jobs' => $stats->failed_jobs,
                'success_rate' => $stats->total_jobs > 0 ? 
                    round(($stats->completed_jobs / $stats->total_jobs) * 100, 2) : 100
            ],
            'recent_failures' => $recentFailures->map(function ($log) {
                return [
                    'account_id' => $log->account_id,
                    'trigger_type' => $log->trigger_type,
                    'description' => $log->description,
                    'error_message' => $log->error_message,
                    'created_at' => Carbon::parse($log->created_at)->format('Y-m-d H:i:s'),
                ];
            })
        ]);
    }

    /**
     * Retry failed recalculation jobs
     */
    public function retryFailed(Request $request)
    {
        $request->validate([
            'account_id' => 'nullable|integer',
            'log_ids' => 'nullable|array',
            'log_ids.*' => 'integer'
        ]);

        $query = DB::table('recalculation_logs')
            ->where('status', 'failed');

        if ($request->filled('account_id')) {
            $query->where('account_id', $request->account_id);
        }

        if ($request->filled('log_ids')) {
            $query->whereIn('id', $request->log_ids);
        }

        $failedLogs = $query->get();

        $retriedCount = 0;
        foreach ($failedLogs as $log) {
            // Dispatch new job for failed recalculation
            \App\Jobs\ProcessInventoryRecalculationJob::dispatch(
                $log->account_id,
                $log->from_date,
                null // No specific trigger transaction for retries
            )->onQueue('inventory')->delay(now()->addSeconds(5));

            // Update the log status to pending
            DB::table('recalculation_logs')
                ->where('id', $log->id)
                ->update([
                    'status' => 'pending',
                    'error_message' => null,
                    'updated_at' => now()
                ]);

            $retriedCount++;
        }

        return response()->json([
            'message' => "Retried {$retriedCount} failed recalculation jobs.",
            'retried_count' => $retriedCount
        ]);
    }

    /**
     * Clear old completed logs
     */
    public function clearOldLogs(Request $request)
    {
        $request->validate([
            'days_old' => 'nullable|integer|min:1|max:365'
        ]);

        $daysOld = $request->input('days_old', 30); // Default 30 days

        $deletedCount = DB::table('recalculation_logs')
            ->where('status', 'completed')
            ->where('created_at', '<', now()->subDays($daysOld))
            ->delete();

        return response()->json([
            'message' => "Cleared {$deletedCount} old completed recalculation logs.",
            'deleted_count' => $deletedCount
        ]);
    }
}
