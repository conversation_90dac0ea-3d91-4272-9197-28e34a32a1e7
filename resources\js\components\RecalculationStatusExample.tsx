/**
 * DOCUMENTATION FILE - Usage Examples for RecalculationStatus Component
 * 
 * This file contains example implementations showing how to integrate
 * the RecalculationStatus component in different scenarios:
 * - Purchase/Sale edit forms
 * - Admin dashboards
 * - System monitoring
 * 
 * These are reference examples and not used in production.
 */

import React from 'react';
import RecalculationStatus from './RecalculationStatus';

// Example usage in a Purchase Edit form
const PurchaseEditForm: React.FC = () => {
    const [selectedAccountId, setSelectedAccountId] = React.useState<number | undefined>(5);

    return (
        <div className="space-y-4">
            <h2 className="text-lg font-semibold">Edit Purchase Transaction</h2>
            
            {/* Show recalculation status for the selected account */}
            <RecalculationStatus 
                accountId={selectedAccountId} 
                autoRefresh={true}
                refreshInterval={5000}
            />
            
            {/* Your purchase form fields would go here */}
            <div className="grid grid-cols-2 gap-4">
                <div>
                    <label className="block text-sm font-medium mb-1">Currency Account</label>
                    <select 
                        value={selectedAccountId || ''} 
                        onChange={(e) => setSelectedAccountId(Number(e.target.value))}
                        className="w-full border rounded px-3 py-2"
                    >
                        <option value="">Select Account</option>
                        <option value="5">USD Account</option>
                        <option value="6">EUR Account</option>
                        <option value="7">GBP Account</option>
                    </select>
                </div>
                
                <div>
                    <label className="block text-sm font-medium mb-1">Amount</label>
                    <input 
                        type="number" 
                        className="w-full border rounded px-3 py-2"
                        placeholder="Enter amount"
                    />
                </div>
            </div>
            
            {/* Form buttons */}
            <div className="flex gap-2">
                <button className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                    Update Purchase
                </button>
                <button className="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600">
                    Cancel
                </button>
            </div>
        </div>
    );
};

// Example usage for System Status (Admin Dashboard)
const AdminDashboard: React.FC = () => {
    return (
        <div className="space-y-6">
            <h1 className="text-2xl font-bold">Admin Dashboard</h1>
            
            {/* System-wide recalculation status */}
            <RecalculationStatus 
                showSystemStatus={true}
                autoRefresh={true}
                refreshInterval={10000} // Refresh every 10 seconds
            />
            
            {/* Other dashboard content */}
            <div className="grid grid-cols-3 gap-4">
                <div className="bg-white p-4 rounded-lg shadow">
                    <h3 className="font-medium">Total Transactions</h3>
                    <p className="text-2xl font-bold">1,234</p>
                </div>
                <div className="bg-white p-4 rounded-lg shadow">
                    <h3 className="font-medium">Active Accounts</h3>
                    <p className="text-2xl font-bold">56</p>
                </div>
                <div className="bg-white p-4 rounded-lg shadow">
                    <h3 className="font-medium">Pending Reviews</h3>
                    <p className="text-2xl font-bold">8</p>
                </div>
            </div>
        </div>
    );
};

export { PurchaseEditForm, AdminDashboard };
export default RecalculationStatus;
