# Project Details: Advanced Double Accounting System

## Overview
    This project is a comprehensive system designed to manage financial transactions and records for a company. It includes features like user authentication, account management, journal entry, transaction recording, currency exchange, and cheque management.

## Technologies Used
    Laravel 12 with react starter kit
    MySQL

## Core Features and Pages
    Responsive for all screen sizes
    1.  Login Page (done)
    2.  Logout Functionality (done)
    3.  Registration Page (as admin - Done)
    4.  Dashboard Page (dummyD<PERSON> will do later)
    5.  Todo List Feature (Integrate into Dashboard)
    6.  Profile Page (done)
    7.  Settings Page (done)
    8.  Create Account Page 
    9.  List Accounts Page
    10. Edit Account Page
    11. Currency Management Pages
    12. General Journal Entry Page
    13. Specific Journal Entry Pages (Bank Payment/Receive, Cash Payment/Receive)
    14. Multiple Journal Entry Page
    15. Edit Journal Entry Page
    16. PKR Purchase Page
    25. List PKR Purchases Page
    18. Edit PKR Purchase Page
    17. PKR Sale Page
    26. List PKR Sales Page
    19. Edit PKR Sale Page
    21. Cheque Entry Page
    22. Multiple Cheque Entry Page
    23. List Cheques Page
    24. Edit Cheque Page
    20. Currency Exchange Entry Pages (e.g., /exchange/dhm-entry, /exchange/tmn-single-entry)
    25. List Currency Exchange Entries Page
    26. Edit Currency Exchange Entry Page
    27. List PKR Transactions Page


## Project Folder Structure

```
AccSystem/
├── app/
│   ├── Http/
│   │   ├── Controllers/
│   │   │   ├── Accounts/
│   │   │   │   ├── AccountController.php
│   │   │   │   ├── AccountTypeController.php
│   │   │   │   └── CurrencyController.php
│   │   │   ├── Admin/
│   │   │   │   └── UserController.php
│   │   │   ├── Auth/
│   │   │   │   ├── ApiAuthController.php
│   │   │   │   ├── AuthenticatedSessionController.php
│   │   │   │   ├── ConfirmablePasswordController.php
│   │   │   │   ├── EmailVerificationNotificationController.php
│   │   │   │   ├── EmailVerificationPromptController.php
│   │   │   │   ├── NewPasswordController.php
│   │   │   │   ├── PasswordResetLinkController.php
│   │   │   │   ├── RegisteredUserController.php
│   │   │   │   └── VerifyEmailController.php
│   │   │   ├── ChequeEntry/
│   │   │   │   └── ChequeController.php
│   │   │   ├── Controller.php
│   │   │   ├── JournalEntry/
│   │   │   │   ├── JournalEntryController.php
│   │   │   │   └── PaymentTypeController.php
│   │   │   ├── Reports/
│   │   │   ├── Settings/
│   │   │   │   ├── PasswordController.php
│   │   │   │   └── ProfileController.php
│   │   │   ├── Todo/
│   │   │   │   └── TodoController.php
│   │   │   └── Transaction/
│   │   │       └── TransactionController.php
│   │   ├── Middleware/
│   │   │   ├── HandleAppearance.php
│   │   │   └── HandleInertiaRequests.php
│   │   └── Requests/
│   │       ├── Auth/
│   │       │   └── LoginRequest.php
│   │       ├── Settings/
│   │       │   └── ProfileUpdateRequest.php
│   │       └── Todo/
│   │           ├── StoreTodoRequest.php
│   │           └── UpdateTodoRequest.php
│   ├── Models/
│   │   ├── Account.php
│   │   ├── AccountType.php
│   │   ├── Cheque.php
│   │   ├── ChqRefBank.php
│   │   ├── Currency.php
│   │   ├── JournalEntry.php
│   │   ├── PaymentType.php
│   │   ├── Todo.php
│   │   └── User.php
│   ├── Policies/
│   │   └── TodoPolicy.php
│   └── Providers/
│       └── AppServiceProvider.php
├── artisan
├── bkacbox_mcp_settings.json
├── bootstrap/
│   ├── app.php
│   ├── cache/
│   └── providers.php
├── components.json
├── composer.json
├── composer.lock
├── config/
│   ├── app.php
│   ├── auth.php
│   ├── cache.php
│   ├── database.php
│   ├── filesystems.php
│   ├── inertia.php
│   ├── logging.php
│   ├── mail.php
│   ├── queue.php
│   ├── sanctum.php
│   ├── services.php
│   └── session.php
├── database/
│   ├── .gitignore
│   ├── database.sqlite
│   ├── factories/
│   │   └── UserFactory.php
│   ├── migrations/
│   │   ├── 0001_01_01_000000_create_users_table.php
│   │   ├── 0001_01_01_000001_create_cache_table.php
│   │   ├── 0001_01_01_000002_create_jobs_table.php
│   │   ├── 2025_04_26_183917_create_personal_access_tokens_table.php
│   │   ├── 2025_05_01_000000_create_todos_table.php
│   │   ├── 2025_05_02_100000_create_accounting_core_tables.php
│   │   ├── 2025_05_03_000000_create_journal_entries_and_payment_types_tables.php
│   │   ├── 2025_05_24_103530_create_chq_ref_banks_table.php
│   │   └── 2025_06_01_000000_create_cheque_entries_table.php
│   ├── schema.sql
│   └── seeders/
│       ├── AccountSeeder.php
│       ├── AccountTypeSeeder.php
│       ├── DatabaseSeeder.php
│       ├── PaymentTypeSeeder.php
│       └── UserSeeder.php
├── docs/
│   ├── api/
│   ├── comprehensive_laravel12_guide.md
│   ├── index.html
│   ├── Laravel12FeatureResearch.md
│   ├── laravel12_financial_app_implementation.md
│   ├── mysql_optimization_techniques.md
│   ├── performance/
│   │   ├── accounting-database-structure.md
│   │   ├── accounting-optimizations.md
│   │   ├── accounting-relationships.md
│   │   ├── async-and-broadcasting.md
│   │   ├── database-optimization.md
│   │   ├── laravel-tips-and-tricks.md
│   │   └── laravel12-features.md
│   ├── script.js
│   ├── style.css
│   └── website_plan.md
├── eslint.config.js
├── Memory.md
├── node_modules/
├── package-lock.json
├── package.json
├── phpunit.xml
├── ProjectDetails.md
├── projectProgress.md
├── public/
│   ├── .htaccess
│   ├── build/
│   ├── favicon.ico
│   ├── hot
│   ├── index.php
│   ├── logo.svg
│   └── robots.txt
├── README.md
├── resources/
├── routes/
│   ├── api.php
│   ├── auth.php
│   ├── console.php
│   ├── settings.php
│   └── web.php
├── storage/
│   ├── app/
│   │   ├── .gitignore
│   │   ├── private/
│   │   │   └── .gitignore
│   │   └── public/
│   │       └── .gitignore
│   ├── framework/
│   │   ├── .gitignore
│   │   ├── cache/
│   │   ├── sessions/
│   │   │   └── .gitignore
│   │   ├── testing/
│   │   │   └── .gitignore
│   │   └── views/
│   └── logs/
├── tests/
│   ├── Feature/
│   │   ├── Auth/
│   │   │   ├── AuthenticationTest.php
│   │   │   ├── EmailVerificationTest.php
│   │   │   ├── PasswordConfirmationTest.php
│   │   │   ├── PasswordResetTest.php
│   │   │   └── RegistrationTest.php
│   │   ├── DashboardTest.php
│   │   ├── ExampleTest.php
│   │   └── Settings/
│   │       ├── PasswordUpdateTest.php
│   │       └── ProfileUpdateTest.php
│   ├── Pest.php
│   ├── TestCase.php
│   └── Unit/
│       └── ExampleTest.php
├── .env
├── .env.example
├── .git/
├── .gitattributes
├── .github/
├── .gitignore
├── Todo.md
├── tsconfig.json
├── vendor/
└── vite.config.ts

```

### 1. Login Page

*   **Page Elements:** Standard login form (email, password fields, submit button).
*   **Frontend Logic:** Basic form submission.
*   **Controller Function:** Handled by Laravel's built-in authentication or a custom `AuthController`.
*   **Functionality:** Authenticates the user based on provided credentials.
*   **Database Interaction:** Reads `User` model to verify credentials.

### 2. Logout Functionality

*   **Page Elements:** Typically a button or link in the navigation.
*   **Frontend Logic:** Simple link click or form submission.
*   **Controller Function:** Handled by Laravel's built-in authentication or a custom `AuthController`.
*   **Functionality:** Logs the current user out.
*   **Database Interaction:** Updates user session state.

### 3. Registration Page

*   **Page Elements:** User registration form (name, email, password, password confirmation fields, submit button).
*   **Frontend Logic:** Basic form submission and validation.
*   **Controller Function:** Handled by Laravel's built-in registration or a custom `AuthController`.
*   **Functionality:** Creates a new user account.
*   **Database Interaction:** Creates a new record in the `User` model.

### 4. Dashboard Page

*   **Page Elements:** Summary boxes (Daily Book, Orders, Cheques), quick links, Todo list section.
*   **Frontend Logic:** Integrates the Todo list partial (`partials._todo_list`). Uses AJAX for adding, completing, deleting, and reordering Todo items.
*   **Controller Function:** `DashboardController@index`.
*   **Functionality:** Fetches and displays summary counts (Daily Book entries, Orders, Cheques), financial totals (Balance Sheet/Income Statement), and the user's Todo list.
*   **Database Interaction:** Reads `JournalEntry`, `Transaction` (implied for orders), `Cheque`, `Account`, and `Todo` models to gather data for display.

### 5. Todo List Feature (Integrated into Dashboard)

*   **Page Elements:** Task list display area, Add Todo Modal (`partials._add_todo_modal.blade.php`).
*   **Frontend Logic:** Uses AJAX calls for all CRUD operations (add, toggle completion, delete) and reordering tasks. Displays relative time labels for task age.
*   **Controller Functions:** `TodoController` handles `index`, `store`, `update`, `destroy`, `toggle`, and `reorder` actions.
*   **Functionality:** Allows users to manage their personal task list.
*   **Database Interaction:** Performs Create, Read, Update, Delete (CRUD) operations on the `Todo` model.

### 6. Profile Page

*   **Page Elements:** Form with fields for user's name, email, password (current and new), and profile image upload.
*   **Frontend Logic:** Standard form submission.
*   **Controller Functions:** `ProfileController@index` (displays the form), `ProfileController@update` (handles form submission).
*   **Functionality:** Allows the logged-in user to update their personal details and password.
*   **Database Interaction:** Reads and updates the authenticated `User` model record.

### 7. Settings Page

*   **Page Elements:** Contains sections for Profile, Password, Appearance, and (for Admins) User Management. User Management includes a data table listing users with Create, Edit, and Delete actions handled via modals.
*   **Frontend Logic:** Standard form submission for Profile/Password/Appearance. User management uses optimistic UI updates for CRUD operations.
*   **Controller Functions:** `ProfileController` handles profile/password updates. `Admin\UserController` handles user management CRUD.
*   **Functionality:** Allows users to manage their profile/password/appearance. Admins can manage all users (create, edit role/details, delete).
*   **Database Interaction:** Reads/Updates `User` model records. Application settings potentially read from config/env.

### 8. List Accounts Page (`/accounts`) (Done)

*   **Page Elements:** Dropdown (using Select2) to filter by `Account Type`, a Data Table (using Yajra DataTables) to display accounts, dynamic columns based on selected type, and an Edit button for each account row.
*   **Frontend Logic:** Initializes Select2 for the dropdown. Uses Yajra DataTables to fetch and display account data via an AJAX call to `/accounts/search` whenever the Account Type selection changes.
*   **Controller Function:** `AccountController@index` loads the initial view. `AccountController@search` responds to the AJAX request, fetching accounts based on `account_type_id`, formatting data for DataTables, and including edit links.
*   **Functionality:** Provides a searchable and sortable list of all accounts, filterable by their type.
*   **Database Interaction:** Reads `Account` and `AccountType` models. The `search` method specifically filters `Account` records by `account_type_id`.

### 9. Create Account Page (`/accounts/create`) (Done)

*   **Page Elements:** A form containing fields for various account details. Includes a dropdown (using Select2) for selecting the `Account Type`. Specific form sections (like contact info, bank details, employee info) are shown or hidden dynamically.
*   **Frontend Logic:** JavaScript code manages the visibility of form sections based on the selected `Account Type`.
*   **Controller Function:** `AccountController@create` loads the view. `AccountController@store` handles the form submission.
*   **Functionality:** Provides the interface for adding new accounts. The `store` method validates the submitted data, applies specific logic for certain account types (e.g., 'currency', 'currency_account'), generates a unique `account_number`, and saves the new account record.
*   **Database Interaction:** Reads `AccountType` model (for the dropdown). Creates a new record in the `Account` model.

### 10. Edit Account Page (`/accounts/{account}/edit`) (Done)

*   **Page Elements:** Similar to the Create Account page, but the form fields are pre-populated with the data of the account being edited. Dynamic fields based on Account Type are also present.
*   **Frontend Logic:** Similar dynamic field visibility logic as the Create Account page.
*   **Controller Function:** `AccountController@edit` loads the view and passes the existing account data. `AccountController@update` handles the form submission.
*   **Functionality:** Allows users to modify the details of an existing account.
*   **Database Interaction:** Reads `Account` and `AccountType` models. Updates the corresponding existing `Account` record.

### 11. Currency Management Pages (`/accounts/currency/*`) (Done)

*   **Page Elements:** Forms and lists for creating and viewing currencies.
*   **Frontend Logic:** Standard form submission and potentially DataTables for listing.
*   **Controller:** `CurrencyController` handles CRUD operations.
*   **Functionality:** Basic management (Create, Read, Update, Delete) of foreign currencies used in the system.
*   **Database Interaction:** Performs CRUD operations on the `Currency` model.

### 12. General Journal Entry Page (`/journal/entry`)

*   **Page Elements:** Form fields for Date (using Datepicker), Payment Type (Select2), Credit Account (Select2), Debit Account (Select2), Description, Cheque Number (optional), and Amount.
*   **Frontend Logic:** Basic client-side validation (required fields, ensuring credit account is different from debit account, amount is numeric).
*   **Controller Function:** `JournalController@entry` loads the view. `JournalController@store` handles the form submission.
*   **Functionality:** Allows users to record a standard double-entry (one debit, one credit) journal transaction in PKR.
*   **Database Interaction:** Reads `Account` and `PaymentType` models (for dropdowns). Creates two corresponding `JournalEntry` records (one debit, one credit) linked by a `transaction_number`.

* ### Data Storage Process :
- Each journal entry creates two records in the database (debit and credit) with the same TID (Transaction ID)
- The store method in JournalEntryController handles the creation of these paired entries
- Key fields stored include:
  - TID (unique transaction identifier)
  - date (transaction date)
  - payment_type_id
  - account_id (either credit or debit account)
  - description/narration
  - chq_no (check number if applicable)
  - amount (positive for debit, negative for credit)
  - is_credit flag
  - created_by (user ID)

### 13. Specific Journal Entry Pages (Bank Payment/Receive, Cash Payment/Receive) (Done)

*   **Page Elements:** Similar to the General Journal Entry page, but often pre-fills or restricts one side of the entry (e.g., Debit Account is set to a Bank account for Bank Payment).
*   **Frontend Logic:** Basic form validation.
*   **Controller Function:** Specific methods in `JournalController` like `bankPayment`, `bankReceive`, `cashPayment`, `cashReceive` load the respective views. The `JournalController@store` method handles the submission (often reused).
*   **Functionality:** Provides streamlined interfaces for common transaction types, reducing data entry steps.
*   **Database Interaction:** Reads `Account` (often filtered by type, e.g., 'Bank' or 'Cash') and `PaymentType` models. Creates paired `JournalEntry` records.

### 14. Multiple Journal Entry Page (`/journal/multiple`) (Done)

*   **Page Elements:** Fields for Date and Transaction Number. A dynamic table area where users can add multiple rows, each specifying a Debit or Credit Account, Description, and Amount.
*   **Frontend Logic:** JavaScript allows adding and removing entry rows. Client-side validation ensures that the total debits equal the total credits for the transaction.
*   **Controller Function:** `JournalController@multiple` loads the view. `JournalController@store` handles the submission (likely adapted to handle multiple entries).
*   **Functionality:** Enables recording complex transactions involving multiple debits and/or credits under a single `transaction_number`.
*   **Database Interaction:** Reads `Account` and `PaymentType` models. Creates multiple `JournalEntry` records, all linked by the same `transaction_number`.

### 15. Edit Journal Entry Page (`/journal/{id}/edit`) (Done)

*   **Page Elements:** Similar to the original entry page (General, Specific, or Multiple), pre-filled with the data for the transaction being edited.
*   **Frontend Logic:** Basic form validation.
*   **Controller Function:** `JournalController@edit` loads the view with the transaction data. `JournalController@update` handles the form submission.
*   **Functionality:** Allows users to modify existing journal entries.
*   **Database Interaction:** Reads `JournalEntry`, `Account`, and `PaymentType` models. Updates the existing `JournalEntry` records associated with the transaction.

### 16. PKR Purchase Page (`/transaction/purchase`)

*   **Page Elements:** Form fields for Date, Supplier Account (Select2), Currency Account (Select2, if foreign currency involved), Currency Amount, Rate, PKR Amount, Description, Reference Number, Order ID, and Invoice Number (auto-fetched).
*   **Frontend Logic:** An AJAX call to `/transaction/get-number` fetches the next available invoice number. Basic form validation.
*   **Controller Function:** `TransactionController@purchase` loads the view. `TransactionController@store` handles the submission.
*   **Functionality:** Records a purchase transaction. Creates corresponding `JournalEntry` records (e.g., Debit Purchases/Inventory, Credit Supplier). Calculates and potentially stores an average rate (`avg_rate`) if foreign currency is involved. Generates descriptive narrations.
*   **Database Interaction:** Reads `Account` model. Creates `JournalEntry` records.

### 17. PKR Sale Page (`/transaction/sale`)

*   **Page Elements:** Form fields for Date, Customer Account (Select2), Currency Account (Select2, if applicable), Currency Amount, Rate, PKR Amount, Description, Reference Number, Order ID, and Invoice Number (auto-fetched).
*   **Frontend Logic:** An AJAX call to `/transaction/sale/get-number` fetches the next available invoice number. Basic form validation.
*   **Controller Function:** `TransactionController@sale` loads the view. `TransactionController@storeSale` handles the submission.
*   **Functionality:** Records a sale transaction. Creates corresponding `JournalEntry` records (e.g., Debit Customer/Accounts Receivable, Credit Sales Revenue). Calculates `avg_rate` if needed. Generates narrations.
*   **Database Interaction:** Reads `Account` model. Creates `JournalEntry` records.

### 18. Edit PKR Purchase Page (`/transaction/purchase/{id}/edit`)

*   **Page Elements:** Similar to the create purchase page, pre-filled with the existing transaction data.
*   **Frontend Logic:** Basic form validation.
*   **Controller Function:** `TransactionController@editPurchase` loads the view. `TransactionController@updatePurchase` handles the submission.
*   **Functionality:** Allows modification of existing purchase transactions and their associated journal entries.
*   **Database Interaction:** Reads `JournalEntry` and `Account` models. Updates `JournalEntry` records.

### 19. Edit PKR Sale Page (`/transaction/sale/{id}/edit`)

*   **Page Elements:** Similar to the create sale page, pre-filled with the existing transaction data.
*   **Frontend Logic:** Basic form validation.
*   **Controller Function:** `TransactionController@editSale` loads the view. `TransactionController@updateSale` handles the submission.
*   **Functionality:** Allows modification of existing sale transactions and their associated journal entries.
*   **Database Interaction:** Reads `JournalEntry` and `Account` models. Updates `JournalEntry` records.

### 20. Currency Exchange Entry Pages (e.g., `/exchange/dhm-entry`, `/exchange/tmn-single-entry`)

*   **Page Elements:** Forms similar to PKR Journal Entry pages (single, double, multiple), but tailored for a specific foreign currency (DHM, TMN, RYL, USD, AFG). Fields include Date, Account (Select2 - filtered for the specific currency), Description, and Amount (in the foreign currency).
*   **Frontend Logic:** Basic form validation.
*   **Controller Function:** Methods within `ExchangeController` like `@dhmEntry`, `@tmnSingleEntry` load the views. Methods like `@dhmStore`, `@tmnStoreSingle` handle submissions.
*   **Functionality:** Records transactions directly in a specified foreign currency, separate from PKR entries.
*   **Database Interaction:** Reads `Account` model (filtered by the relevant currency). Creates records in the `ExchangeEntry` model, storing the `currency_code`.

### 21. Cheque Entry Page (`/cheque/entry`)

*   **Page Elements:** Form with fields for Cheque Type (Inward/Outward), Entry Date, Cheque Date, Posting Date (required for Inward), From Account (Select2), To Account (Select2), Amount, Bank (Select2 - likely filtered for 'chq_ref_bank' type accounts), and Cheque Number.
*   **Frontend Logic:** Basic form validation.
*   **Controller Function:** `ChequeController@entry` loads the view. `ChequeController@store` handles the submission.
*   **Functionality:** Records the details of a single post-dated cheque received or issued.
*   **Database Interaction:** Reads `Account` model. Creates a single record in the `Cheque` model.

### 22. Multiple Cheque Entry Page (`/cheque/multiple-entry`)

*   **Page Elements:** Similar to the single Cheque Entry page, but allows adding multiple cheque rows dynamically.
*   **Frontend Logic:** JavaScript to add/remove cheque entry rows. Basic validation per row.
*   **Controller Function:** `ChequeController@multipleEntry` loads the view. `ChequeController@storeMultiple` handles the submission.
*   **Functionality:** Allows efficient entry of multiple post-dated cheques at once.
*   **Database Interaction:** Reads `Account` model. Creates multiple records in the `Cheque` model.

### 23. List Cheques Page (`/cheque/list`)

*   **Page Elements:** A Data Table (likely Yajra) displaying recorded cheques. Filters for Cheque Type (Inward/Outward) and Status (Pending, Cleared, Bounced, Cancelled). Action buttons for each cheque (Edit, Delete, Change Status).
*   **Frontend Logic:** Uses DataTables for displaying, sorting, and filtering cheque data. AJAX is likely used to load the data and potentially handle status changes or bulk actions.
*   **Controller Function:** `ChequeController@list` loads the view and provides data for the DataTable (possibly via a separate data endpoint).
*   **Functionality:** Provides a comprehensive overview and management interface for all recorded cheques.
*   **Database Interaction:** Reads `Cheque` and related `Account` models.

### 24. Edit Cheque Page (`/cheque/edit/{cheque}`)

*   **Page Elements:** Similar to the Cheque Entry page, but pre-filled with the data of the cheque being edited.
*   **Frontend Logic:** Basic form validation.
*   **Controller Function:** `ChequeController@edit` loads the view with the cheque data. `ChequeController@update` handles the form submission.
*   **Functionality:** Allows modification of cheque details, typically only possible while the cheque is still in 'Pending' status.
*   **Database Interaction:** Reads `Cheque` and `Account` models. Updates the specific `Cheque` record.

### 25. Cheque Status Update Feature (via List Page or `/cheque/status/{cheque}`)

*   **Page Elements:** Usually implemented as a modal dialog or a dropdown menu on the List Cheques page, allowing selection of a new status (Cleared, Bounced, Cancelled).
*   **Frontend Logic:** An AJAX call is triggered to submit the status change request to the server.
*   **Controller Function:** `ChequeController@updateStatus`.
*   **Functionality:** Updates the status of a specific cheque. **Crucially, this action triggers the automatic creation of the corresponding `JournalEntry` records.** For example, clearing an inward cheque debits the Bank and credits Accounts Receivable. Clearing an outward cheque debits Accounts Payable and credits the Bank. Bounced cheques trigger reversal entries. The controller generates a transaction number and appropriate narrations for these journal entries.
*   **Database Interaction:** Reads `Cheque` and `Account` models. Updates the `status` field of the `Cheque` record. Creates new `JournalEntry` records. Updates the `Cheque` record with the `journal_entry_id` of the created transaction.

### 26. Daily Book Report (PKR) (`/reports/daily-book`)

*   **Page Elements:** A date selector (using Datepicker). A Data Table (Yajra) displaying columns like Transaction ID (TID), Account Name, Narration, Debit Amount, Credit Amount. Edit/Delete links for each entry. A Print button. Footer row showing total debits and credits for the day.
*   **Frontend Logic:** Initializes the Datepicker. When the date changes, DataTables makes an AJAX request to `/reports/daily-book-data` to load the transactions for the selected date.
*   **Controller Function:** `ReportController@dailyBook` loads the initial view. `ReportController@dailyBookData` responds to the AJAX request, fetching and formatting data for the table.
*   **Functionality:** Shows a chronological list of all PKR journal entries (`JournalEntry` records) posted on a specific date. Provides links to edit the original transactions.
*   **Database Interaction:** Reads `JournalEntry` records, joining with `Account`, `PaymentType`, and `User` models, filtered by the selected `date`.

### 27. Ledger Report (PKR) (`/reports/ledgers-pkr`)

*   **Page Elements:** An Account selector (Select2), Date range selectors (start and end dates using Datepickers). A table displaying transaction details (Date, Type, Narration, Debit, Credit, Running Balance). Display areas for Opening Balance and Closing Balance. Footer showing Total Debits and Credits for the period. Buttons for Print, PDF Export, and Excel Export.
*   **Frontend Logic:** Initializes Select2 and Datepickers. Submitting the form triggers the report generation on the server.
*   **Controller Function:** `ReportController@ledgersPkr` loads the view and generates the report data upon form submission. `ReportController@ledgersPdf` and `ReportController@ledgersExcel` handle the export requests using DomPDF and Maatwebsite/Laravel-Excel respectively.
*   **Functionality:** Displays the detailed transaction history and calculates the running balance for a specific account over a selected date range.
*   **Database Interaction:** Calculates the opening balance by summing `JournalEntry` amounts for the selected `account_id` before the start date. Reads `JournalEntry` records for the selected `account_id` within the specified date range.

### 28. Balance Sheet Report (PKR)

*   **Page Elements:** Date selector ('as of' date), report display area showing Assets, Liabilities, and Equity sections with account balances, Print/PDF/Excel export buttons.
*   **Frontend Logic:** Date selection triggers report generation via form submission.
*   **Controller Function:** `ReportController@balanceSheet` (or similar).
*   **Functionality:** Generates a standard Balance Sheet financial statement by summing the balances of accounts categorized under Assets, Liabilities, and Equity types as of the selected date.
*   **Database Interaction:** Reads `Account`, `AccountType`, and `JournalEntry` models. Aggregates `JournalEntry` amounts based on `account_id` and `account_type` up to the specified date to calculate final balances for each relevant account.

### 29. Income Statement Report (PKR)

*   **Page Elements:** Date range selectors, report display area showing Revenue and Expense sections with account totals, calculation of Net Income/Loss, Print/PDF/Excel export buttons.
*   **Frontend Logic:** Date range selection triggers report generation via form submission.
*   **Controller Function:** `ReportController@incomeStatement` (or similar).
*   **Functionality:** Generates a standard Income Statement (Profit and Loss) by calculating total revenues and total expenses over the selected period to determine Net Income or Loss.
*   **Database Interaction:** Reads `Account`, `AccountType`, and `JournalEntry` models. Aggregates `JournalEntry` amounts based on `account_id` and `account_type` (Revenue and Expense types) within the specified date range.

### 30. Currency Daily Book Report (e.g., `/exchange/dhm-daily-book`)

*   **Page Elements:** Date selector, Data Table (Yajra) showing currency transactions, Print/PDF buttons.
*   **Frontend Logic:** Date selection triggers an AJAX request (e.g., to `/exchange/dhm-daily-book-data`) to load data into the DataTable.
*   **Controller Function:** Methods in `ReportExchangeController` like `@dhmDailyBook` (loads view) and `@dhmDailyBookData` (provides data).
*   **Functionality:** Shows all transactions recorded in a specific foreign currency (`ExchangeEntry` records) for a selected date.
*   **Database Interaction:** Reads `ExchangeEntry` records filtered by the specific `currency_code` (e.g., 'DHM') and the selected `date`.

### 31. Currency Ledger Report (e.g., `/exchange/ryl-ledgers`)

*   **Page Elements:** Account selector (Select2, filtered for accounts associated with the specific currency), Date range selectors, Table displaying transactions (Date, Type, Narration, Debit, Credit, Balance in foreign currency), Opening/Closing Balances, Print/PDF buttons.
*   **Frontend Logic:** Account and date range selection triggers report generation via form submission.
*   **Controller Function:** Methods in `ReportExchangeController` like `@rylLedgers` (loads view/generates report) and potentially `@rylLedgersData`.
*   **Functionality:** Displays the transaction history and running balance for a specific account conducted in a specific foreign currency.
*   **Database Interaction:** Calculates opening balance by summing `ExchangeEntry` amounts for the selected `account_id` and `currency_code` before the start date. Reads `ExchangeEntry` records filtered by `account_id`, `currency_code`, and the date range.

### 32. Currency Balance Sheet Report (e.g., `/exchange/usd-balance-sheet`)

*   **Page Elements:** Date selector ('as of' date), report display area (Assets, Liabilities, Equity in the specific currency), Print/PDF buttons.
*   **Frontend Logic:** Date selection triggers report generation.
*   **Controller Function:** Methods in `ReportExchangeController` like `@usdBalanceSheet` and potentially `@usdBalanceSheetData`.
*   **Functionality:** Generates a Balance Sheet using only the balances derived from transactions recorded in the specified foreign currency.
*   **Database Interaction:** Reads `Account`, `AccountType`, and `ExchangeEntry` models. Aggregates `ExchangeEntry` amounts based on `account_id`, `account_type`, and `currency_code` up to the specified date.

## UI/UX Standard: Table Skeleton Loading
- Table headings always appear instantly and reflect the selected account type.
- Skeleton loaders show only in the table body cells while data is loading, not over the entire table.
- Skeleton cell style uses className="px-4 py-3" for table cells and a Skeleton with h-5 w-full rounded.
- This design is now standard for all data tables in the project.

## Database Models & Key Relationships

*   **`User`:** Stores application user data.
    *   *Links to:* `Todo`, `JournalEntry`, `ExchangeEntry`, `Cheque`, `Account`, `Currency` (usually via a `created_by` or `user_id` foreign key).
*   **`AccountType`:** Defines categories for accounts (e.g., Asset, Liability, Equity, Revenue, Expense, Bank, Customer, Supplier).
    *   *Links to:* `Account` (one type has many accounts).
*   **`Currency`:** Defines foreign currencies (e.g., USD, DHM, RYL).
    *   *Links to:* `User` (creator), `Account` (an account can be associated with a currency).
*   **`Account`:** Represents entries in the Chart of Accounts (e.g., Cash, Bank ABC, Customer X, Sales).
    *   *Links to:* `AccountType`, `Currency` (optional), `User` (creator). Is referenced by `JournalEntry`, `ExchangeEntry`, `Cheque`.
*   **`PaymentType`:** Defines the nature of a transaction (e.g., Journal Voucher, Bank Payment, Sale Invoice).
    *   *Links to:* `JournalEntry`, `ExchangeEntry`.
*   **`JournalEntry`:** Represents a single debit or credit line for a **PKR** transaction.
    *   Key Fields: `transaction_number` (groups related debits/credits), `account_id`, `amount` (positive for debit, negative for credit, or separate debit/credit columns), `date`.
    *   *Links to:* `Account`, `PaymentType`, `User` (creator), `Cheque` (optional, linked when cheque status changes).
*   **`ExchangeEntry`:** Represents a single debit or credit line for a **Foreign Currency** transaction.
    *   Key Fields: `transaction_number`, `account_id`, `currency_code`, `amount` (in foreign currency), `date`.
    *   *Links to:* `Account`, `PaymentType`, `User` (creator).
*   **`Cheque`:** Stores details of post-dated cheques.
    *   Key Fields: `type` (inward/outward), `status` (pending, cleared, bounced, cancelled), `from_account_id`, `to_account_id`, `bank_id`, `cheque_no`, `cheque_date`, `posting_date`, `journal_entry_id` (populated after status change).
    *   *Links to:* `Account` (from, to, bank), `JournalEntry` (optional), `User` (creator).
*   **`Todo`:** Stores items for the user's task list.
    *   *Links to:* `User`.


## PRD: Journal Entries (Single & Multiple)

### Overview
Implement Journal Entries module with two main pages:
- **Single Entry**: For standard double-entry (one debit, one credit) journal transactions.
- **Multiple Entries**: For complex transactions involving multiple debits and/or credits under a single transaction number.

### Payment Types
- Cust-to-Cust
- JV Payment
- Bank Payment
- Bank Receipt
- Cash Payment
- Cash Receipt
- Purchase
- Sale
- Checked

### Entry Page Elements
- **Date**: DatePicker
- **Payment Type**: ComboBox (select from Payment Types above)
- **Credit Account**: ComboBox (searchable)
- **Debit Account**: ComboBox (searchable)
- **Description**: Text input
- **CHQ NO**: Text input
- **Amount**: Numeric input with accounting like showing commas

### Single Entry Page
- Shows all above elements as a form.
- Only one entry (one debit, one credit) per submission.
- On submit, validates and posts a single journal entry (double-entry logic enforced).

#### Dynamic Description Sentences
- For each Payment Type, the system will generate and save a custom sentence in the description field for both credit and debit entries.
- The sentence template will use the selected accounts and user-provided description.
- **Example (Cust-to-Cust):**
  - Credit Entry: "online paid in {Description} deposit by {Debit Acc}"
  - Debit Entry: "online deposit in {Description} to {Credit Acc.}"
- Each payment type will have its own sentence structure for both credit and debit entries.
- The backend should handle this logic when saving journal entries.


### Multiple Entry Page
- Table format with columns:
  - Credit Account
  - Debit Account
  - Description
  - CHQ NO
  - Amount
- User can add multiple rows (entries) before submitting.
- On submit, validates that total debits equal total credits.
- Posts all entries in a single transaction.

### Requirements

#### 1. Frontend
- Add sidebar navigation:
  - 'Journal Entries' (Single Entry)
  - 'Journal Multiple Entry' (Multiple Entries)
- Use shadcn/ui components for forms, tables, and modals.
- Use skeleton loaders for table bodies (per project UI/UX standard).
- Form validation (required fields, numeric checks, account selection logic, debit/credit balance for multiple).
- Optimistic UI updates for entry creation.
- Use ComboBox for all select/search fields.
- Responsive design for all screen sizes.

#### 2. Backend (Laravel)
- Use Eloquent models: JournalEntry, Account, PaymentType, User.
- Controller: JournalController with methods for single and multiple entry (entry, store, multiple, storeMultiple).
- Validation: Use FormRequest classes for input validation.
- Authorization: Only allow users with correct roles to create/edit entries.
- Use database transactions to ensure atomicity of multi-row journal entries.
- Optimize queries (eager loading, chunking if needed).
- Use Laravel events/jobs for async processing if needed.
- Return clear JSON responses for API endpoints.

#### 3. Database & SQL
- Ensure proper indexing on transaction_number, account_id, date fields.
- Use efficient queries for listing/filtering entries.
- Test with large datasets for performance.

#### 4. Testing
- Write feature and unit tests for all endpoints and UI workflows.
- Test validation, authorization, and edge cases (e.g., unbalanced entries).

#### 5. Documentation
- Update OurProject.md and code comments as features are implemented.
- Document API endpoints and frontend usage.

#### 6. Best Practices
- Follow latest Laravel and React best practices (context7, shadcn/ui, Eloquent, FormRequest, etc.).
- Ensure code is clean, DRY, and maintainable.
- Use optimistic UI and async jobs where appropriate for best UX.

## Project Progress

- Started development on the Registration Page to implement user roles (Admin, Editor, 
Viewer).
- Todo List Feature: Done
- Create Account Page: Done
- List Accounts Page: Done
- Edit Account Page: Done
- Account Management: Done
- All sensitive pages and API endpoints (Daily Book, Journal Entry, User Management) are now protected by both frontend and backend role checks (Admin/Editor only for Journal/Daily Book, Admin only for User Management).
- Custom 403 (Unauthorized) and 404 (Not Found) error pages are implemented for both web and API (JSON) responses, with branded UI and home redirect.
- Daily Book, Journal Entry (single/multiple), and User Management pages are fully functional, with correct role-based access and error handling.
- Table skeleton loading is standardized across all data tables.
- Registration, login, logout, and profile/settings are complete and secure.
- All API endpoints use correct authentication headers and are grouped under `auth:sanctum`.
- Route registration order and shadowing issues resolved for custom API routes.
- Optimistic UI and error handling are implemented for user and account management.
- Documentation and code comments updated as features are implemented.

| Feature/Module                | Status         |
|-------------------------------|---------------|
| Login                         | Done          |
| Logout                        | Done          |
| Registration (Admin)          | Done          |
| Dashboard                     | Dummy data    |
| Todo List                     | Done          |
| Profile                       | Done          |
| Settings                      | Done          |
| Create Account                | Done          |
| List Accounts                 | Done          |
| Edit Account                  | Done          |
| Account Management            | Done          |
| Journal Entries               | Done          |
| Currency Management           | Not done      |
| PKR Purchase/Sale             | Not done      |
| Cheque Management             | Not done      |
| Reports (Daily Book, Ledger)  | In Progress   |
| User Roles                    | Done          |

### Security & Error Handling Improvements
- **Double Protection:** All sensitive pages and API endpoints are protected by both frontend (React role checks) and backend (Laravel controller/middleware role checks).
- **Custom Error Pages:** 403 and 404 error pages are implemented for both web and API responses, with branded UI and home redirect.
- **Role-Based Access:** Only Admin and Editor roles can access Daily Book, Journal Entry, and User Management pages. User Management is Admin-only.
- **API Route Fixes:** Custom API routes are defined before `apiResource` and use correct headers. All are under `auth:sanctum`.
- **Frontend Error Handling:** Skeleton loaders, error messages, and unauthorized pages are shown as appropriate.