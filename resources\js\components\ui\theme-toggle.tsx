import * as React from "react";
import { <PERSON>, Sun } from "lucide-react";
import { useAppearance } from "@/hooks/use-appearance";

export function ThemeToggle() {
  const { appearance, updateAppearance } = useAppearance();

  // Determine the current theme
  const isDark = appearance === "dark" || (appearance === "system" && typeof window !== "undefined" && window.matchMedia("(prefers-color-scheme: dark)").matches);

  const toggleTheme = () => {
    if (appearance === "dark") {
      updateAppearance("light");
    } else if (appearance === "light") {
      updateAppearance("dark");
    } else {
      // If system, toggle based on actual system preference
      updateAppearance(isDark ? "light" : "dark");
    }
  };

  return (
    <button
      aria-label="Toggle theme"
      className="inline-flex items-center justify-center rounded-md px-2 py-1 text-sm transition-colors hover:bg-muted/50 focus:outline-none focus:ring-none focus:ring-ring"
      onClick={toggleTheme}
      type="button"
    >
      {isDark ? (
        <Sun className="h-5 w-5 text-yellow-400" />
      ) : (
        <Moon className="h-5 w-5 text-gray-700" />
      )}
    </button>
  );
}
