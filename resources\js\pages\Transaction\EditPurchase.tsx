import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON>, Head } from '@inertiajs/react';
import type { Errors } from '@inertiajs/core';
import axios from 'axios';
import { NumericFormat, type NumberFormatValues } from 'react-number-format';
import { toast } from 'sonner';

// Shadcn/UI Components
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import AppLayout from '@/layouts/app-layout';
import { BreadcrumbItem } from '@/types';
import { ComboBox } from '@/components/ui/combobox';

// Icons
import { CalendarIcon, Loader2, Save } from 'lucide-react';
import RecalculationStatus from '@/components/RecalculationStatus';

const formatNumber = (value: number | string, options?: Intl.NumberFormatOptions) => {
    const num = typeof value === 'string' ? parseFloat(value.replace(/,/g, '')) : value;
    if (isNaN(num)) return '';
    return num.toLocaleString(undefined, options);
};

// Helper function to parse formatted number strings by removing commas
const parseFormattedNumber = (value: string | number): number => {
    if (typeof value === 'number') return value;
    return parseFloat(value.replace(/,/g, '')) || 0;
};

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Purchase List', href: '/purchase-list' },
    { title: `Edit Purchase Entry`, href: `/purchase-entry/edit/` }
];

interface Account {
    id: number;
    name: string;
    currency_code?: string;
    formula?: 'm' | 'd';
}

interface TransactionDetailsResponse {
    transaction_number: string;
    currency_balance: number;
    pkr_balance: number;
    average_rate: number;
    formula_type: 'm' | 'd';
}

// This is the shape of data fetched for an existing purchase entry
interface PurchaseEntryData {
    date: string;
    selectDr: number;
    selectCr: number | null;
    currency_amount: number;
    rate: number;
    pkr_amount: number;
    description: string;
    refno: string;
    orderid: string; // P-NO
    original_purchase_rate?: number; // For display in alert
    // is_debit is not directly part of the main JE, would be determined by BANAM logic if needed for edit
}

// Define the shape of the form data explicitly for clarity and type safety
interface EditPurchaseFormData {
    date: string;
    selectDr: string;
    selectCr: string;
    currency_amount: string; // Keep as string for input formatting
    rate: string;            // Keep as string for input formatting
    pkr_amount: string;      // Keep as string for input display (read-only)
    description: string;
    refno: string;
    orderid: string;
    [key: string]: any; // Add index signature
}

const initialFormState: EditPurchaseFormData = {
    date: '',
    selectDr: '',
    selectCr: '',
    currency_amount: '',
    rate: '',
    pkr_amount: '',
    description: '',
    refno: '',
    orderid: '',
};

interface EditPurchasePageProps {
    purchaseId: string;
    breadcrumbs: BreadcrumbItem[];
}

export default function EditPurchasePage({ purchaseId, breadcrumbs }: EditPurchasePageProps) {
    const { data, setData, put, processing, errors, clearErrors, setError } = useForm<EditPurchaseFormData>({
        ...initialFormState,
    });

    const [currencyAccounts, setCurrencyAccounts] = useState<Account[]>([]);
    const [customerAccounts, setCustomerAccounts] = useState<Account[]>([]);
    const [selectedCurrencyDetails, setSelectedCurrencyDetails] = useState<TransactionDetailsResponse | null>(null);
    const [originalPurchaseRate, setOriginalPurchaseRate] = useState<number | null>(null);
    const [loadingPageData, setLoadingPageData] = useState(true);
    const [datePickerOpen, setDatePickerOpen] = useState(false);

    useEffect(() => {
        const fetchInitialData = async () => {
            setLoadingPageData(true);
            try {
                const [currAccRes, custAccRes, entryRes] = await Promise.all([
                    axios.get('/api/accounts', { params: { account_type_id: 5 } }),
                    axios.get('/api/accounts', { params: { exclude_account_type_ids: '4,5,7' } }),
                    axios.get<PurchaseEntryData>(`/api/purchase-entries/${purchaseId}`)
                ]);

                setCurrencyAccounts(currAccRes.data);
                setCustomerAccounts(custAccRes.data);
                const entryData = entryRes.data;

                setData({
                    ...initialFormState,
                    date: entryData.date,
                    selectDr: String(entryData.selectDr),
                    selectCr: String(entryData.selectCr || ''),
                    currency_amount: formatNumber(entryData.currency_amount),
                    rate: formatNumber(entryData.rate, { minimumFractionDigits: 2, maximumFractionDigits: 2 }),
                    pkr_amount: formatNumber(entryData.pkr_amount, { minimumFractionDigits: 2, maximumFractionDigits: 2 }),
                    description: entryData.description,
                    refno: entryData.refno,
                    orderid: entryData.orderid,
                });

                setOriginalPurchaseRate(entryData.original_purchase_rate || entryData.rate);

                if (entryData.selectDr) {
                    const detailsRes = await axios.get<TransactionDetailsResponse>('/api/purchase-entries/get-details', {
                        params: { currency_account_id: entryData.selectDr, for_edit: true }
                    });
                    setSelectedCurrencyDetails(detailsRes.data);
                }

            } catch (err) {
                console.error("Failed to fetch initial data for edit page", err);
                toast.error("Failed to load purchase entry data.");
            } finally {
                setLoadingPageData(false);
            }
        };
        fetchInitialData();
    }, [purchaseId]);

    const handleCurrencyAccountChange = async (accountId: string) => {
        setData('selectDr', accountId);
        if (accountId) {
            try {
                const response = await axios.get<TransactionDetailsResponse>('/api/purchase-entries/get-details', {
                    params: { currency_account_id: accountId, for_edit: true }
                });
                setSelectedCurrencyDetails(response.data);
            } catch (error) {
                console.error("Failed to fetch currency details", error);
                setSelectedCurrencyDetails(null);
                toast.error("Failed to fetch new currency account details.");
            }
        } else {
            setSelectedCurrencyDetails(null);
        }
    };

    const handleCustomerAccountChange = (value: string) => {
        setData('selectCr', value);
    };

    useEffect(() => {
        if (data.currency_amount && data.rate && selectedCurrencyDetails?.formula_type) {
            const currAmount = parseFormattedNumber(data.currency_amount);
            const exchangeRate = parseFormattedNumber(data.rate);

            if (!isNaN(currAmount) && !isNaN(exchangeRate) && exchangeRate > 0) {
                const calculatedPkrFloat = selectedCurrencyDetails.formula_type === 'd'
                    ? currAmount / exchangeRate
                    : currAmount * exchangeRate;
                
                const pkrForState = parseFloat(calculatedPkrFloat.toFixed(2));
                setData('pkr_amount', formatNumber(pkrForState, { minimumFractionDigits: 2, maximumFractionDigits: 2 }));
            } else {
                setData('pkr_amount', '');
            }
        } else if (!data.currency_amount || !data.rate) {
             setData('pkr_amount', '');
        }
    }, [data.currency_amount, data.rate, selectedCurrencyDetails, setData]);

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        clearErrors();
        
        // Manually prepare submission data with proper parsing of numeric values
        const submissionData = {
            date: data.date,
            selectDr: data.selectDr ? Number(data.selectDr) : null,
            selectCr: data.selectCr ? Number(data.selectCr) : null,
            currency_amount: parseFormattedNumber(data.currency_amount),
            rate: parseFormattedNumber(data.rate),
            pkr_amount: parseFormattedNumber(data.pkr_amount),
            description: data.description,
            refno: data.refno,
            orderid: data.orderid,
        };
        
        // Directly send AJAX request instead of using Inertia's put method to avoid formatting issues
        axios.put(`/api/purchase-entries/${purchaseId}`, submissionData)
            .then(response => {
                toast.success('Purchase entry updated successfully!');
                // Redirect to the purchase list page after successful update
                window.location.href = '/purchase-list';
            })
            .catch(error => {
                console.error("Update errors:", error.response?.data);
                const serverErrors = error.response?.data?.errors || {};
                const firstErrorKey = Object.keys(serverErrors)[0];
                const firstErrorMessage = firstErrorKey 
                    ? (typeof serverErrors[firstErrorKey] === 'string' 
                        ? serverErrors[firstErrorKey] 
                        : serverErrors[firstErrorKey][0])
                    : error.response?.data?.message || error.message || 'An unknown error occurred';
                    
                toast.error('Update failed!', {
                    description: firstErrorMessage,
                });
                
                // Update form errors if they exist
                if (error.response?.data?.errors) {
                    // Convert errors to Inertia format if needed
                    const inertiaErrors: Record<string, string> = {};
                    Object.entries(error.response.data.errors).forEach(([key, value]) => {
                        inertiaErrors[key] = Array.isArray(value) ? value[0] : value as string;
                    });
                    // Set form errors
                    Object.entries(inertiaErrors).forEach(([key, value]) => {
                        setError(key, value);
                    });
                }
            });
    };

    const handleNumericValueChange = (field: keyof EditPurchaseFormData, values: NumberFormatValues) => {
        setData(field, values.value); 
    };

    if (loadingPageData) {
        return (
            <AppLayout breadcrumbs={breadcrumbs}>
                 <div className="container mx-auto p-4 flex justify-center items-center h-screen">
                    <Loader2 className="h-12 w-12 animate-spin text-primary" />
                    <p className="ml-4 text-lg">Loading purchase details...</p>
                </div>
            </AppLayout>
        );
    }

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Edit Purchase Entry`} />
            <Card className="max-w-3xl mx-auto mt-6 mb-6">
                <CardHeader>
                    <CardTitle>Edit Purchase Entry ({data.orderid})</CardTitle>
                </CardHeader>
                <form onSubmit={handleSubmit}>
                    <CardContent className="space-y-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <Label htmlFor="date">Date</Label>
                                <Popover open={datePickerOpen} onOpenChange={setDatePickerOpen}>
                                    <PopoverTrigger asChild>
                                        <Button
                                            variant={"outline"}
                                            className={`w-full justify-start text-left font-normal ${
                                                !data.date && "text-muted-foreground"
                                            }`}
                                        >
                                            <CalendarIcon className="mr-2 h-4 w-4" />
                                            {data.date ? new Date(data.date).toLocaleDateString() : <span>Pick a date</span>}
                                        </Button>
                                    </PopoverTrigger>
                                    <PopoverContent className="w-auto p-0">
                                        <Calendar
                                            mode="single"
                                            selected={data.date ? new Date(data.date) : undefined}
                                            onSelect={(selectedDate) => {
                                                if (selectedDate) {
                                                    // Fix timezone issue by using the date parts directly
                                                    const year = selectedDate.getFullYear();
                                                    const month = String(selectedDate.getMonth() + 1).padStart(2, '0');
                                                    const day = String(selectedDate.getDate()).padStart(2, '0');
                                                    setData('date', `${year}-${month}-${day}`);
                                                } else {
                                                    setData('date', '');
                                                }
                                                setDatePickerOpen(false);
                                            }}
                                            initialFocus
                                        />
                                    </PopoverContent>
                                </Popover>
                                {errors.date && <p className="text-sm text-red-600 mt-1">{errors.date}</p>}
                            </div>
                            <div>
                                <Label htmlFor="currencyAccount">Currency Account (DR)</Label>
                                <ComboBox 
                                    value={data.selectDr}
                                    onChange={handleCurrencyAccountChange}
                                    options={currencyAccounts.map(acc => ({
                                        label: `${acc.name} ${acc.currency_code ? `(${acc.currency_code})` : ''}`,
                                        value: String(acc.id)
                                    }))}
                                    placeholder="Select currency account..."
                                />
                                {errors.selectDr && <p className="text-sm text-red-600 mt-1">{errors.selectDr}</p>}
                            </div>
                        </div>

                        {selectedCurrencyDetails && (
                            <Alert className="mt-4 bg-sky-50 dark:bg-sky-900 border-sky-200 dark:border-sky-700 text-sky-800 dark:text-sky-100">
                                <AlertTitle className="font-semibold">
                                    Currency (Current Stock Info)
                                </AlertTitle>
                                <AlertDescription className="grid grid-cols-2 md:grid-cols-2 gap-x-4 gap-y-1 text-sm mt-2">
                                    <span>Balance: {formatNumber(selectedCurrencyDetails.currency_balance, {maximumFractionDigits:2})}</span>
                                    <span>PKR Value: {formatNumber(selectedCurrencyDetails.pkr_balance, {maximumFractionDigits:2})}</span>
                                    <span>Avg. Cost: {formatNumber(selectedCurrencyDetails.average_rate, {minimumFractionDigits:2, maximumFractionDigits: 2})}</span>
                                    <span>Formula: {selectedCurrencyDetails.formula_type?.toUpperCase()}</span>
                                </AlertDescription>
                                {(originalPurchaseRate && originalPurchaseRate > 0) && (
                                    <AlertDescription className="mt-2 text-sm font-medium">
                                        Original Purchase Rate: {formatNumber(originalPurchaseRate, {minimumFractionDigits:2, maximumFractionDigits:2})}
                                    </AlertDescription>
                                )}
                            </Alert>
                        )}

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <Label htmlFor="referenceNumber">Manual Ref#</Label>
                                <Input id="referenceNumber" value={data.refno} onChange={e => setData('refno', e.target.value)} />
                                {errors.refno && <p className="text-sm text-red-600 mt-1">{errors.refno}</p>}
                            </div>
                            <div>
                                <Label htmlFor="customerAccount">Customer PKR Account (CR)</Label>
                                <ComboBox 
                                    value={data.selectCr}
                                    onChange={handleCustomerAccountChange}
                                    options={[{label: 'None', value: ''}, ...customerAccounts.map(acc => ({
                                        label: acc.name,
                                        value: String(acc.id) 
                                    }))]}
                                    placeholder="Select customer account..."
                                />
                                {errors.selectCr && <p className="text-sm text-red-600 mt-1">{errors.selectCr}</p>}
                            </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 items-end">
                            <div>
                                <Label htmlFor="currencyAmount">Amount (Currency)</Label>
                                <NumericFormat 
                                    id="currencyAmount"
                                    value={data.currency_amount} 
                                    onValueChange={(values) => handleNumericValueChange('currency_amount', values)}
                                    customInput={Input}
                                    thousandSeparator={true}
                                    allowNegative={false}
                                    decimalScale={0}
                                    placeholder="e.g., 1000"
                                    className="w-full"
                                />
                                {errors.currency_amount && <p className="text-sm text-red-600 mt-1">{errors.currency_amount}</p>}
                            </div>
                            <div>
                                <Label htmlFor="exchangeRate">Rate</Label>
                                <NumericFormat 
                                    id="exchangeRate" 
                                    value={data.rate} 
                                    onValueChange={(values) => handleNumericValueChange('rate', values)}
                                    customInput={Input}
                                    thousandSeparator={true}
                                    allowNegative={false}
                                    decimalScale={2}
                                    fixedDecimalScale
                                    placeholder="e.g., 275.50"
                                    className="w-full"
                                />
                                {errors.rate && <p className="text-sm text-red-600 mt-1">{errors.rate}</p>}
                            </div>
                            <div>
                                <Label htmlFor="pkrAmount">Amount (PKR)</Label>
                                <NumericFormat 
                                    id="pkrAmount"
                                    value={data.pkr_amount} 
                                    readOnly 
                                    customInput={Input} 
                                    thousandSeparator={true}
                                    decimalScale={2}
                                    fixedDecimalScale
                                    className="font-bold bg-gray-100 dark:bg-gray-700 w-full"
                                />
                                {errors.pkr_amount && <p className="text-sm text-red-600 mt-1">{errors.pkr_amount}</p>}
                            </div>
                        </div>
                        
                        <div>
                            <Label htmlFor="description">Description</Label>
                            <Input id="description" value={data.description} onChange={e => setData('description', e.target.value)} />
                            {errors.description && <p className="text-sm text-red-600 mt-1">{errors.description}</p>}
                        </div>

                    </CardContent>
                    <CardFooter>
                        <Button type="submit" className="w-full md:w-auto" disabled={processing || loadingPageData}>
                            {processing ? (
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            ) : (
                                <Save className="mr-2 h-4 w-4" />
                            )}
                            Update Entry
                        </Button>
                    </CardFooter>
                </form>
            </Card>
            <RecalculationStatus 
                accountId={Number(data.selectDr)} 
                autoRefresh={true} 
            />
        </AppLayout>
    );
}
