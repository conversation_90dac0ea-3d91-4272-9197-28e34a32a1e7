"use client"

import * as React from "react"
import { Check, ChevronsUpDown, X, Loader2 } from "lucide-react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

const comboboxVariants = cva(
  "justify-between",
  {
    variants: {
      size: {
        sm: "h-8 min-w-[150px] text-xs",
        md: "h-9 min-w-[200px] text-sm",
        lg: "h-11 min-w-[250px] text-base",
      },
    },
    defaultVariants: {
      size: "md",
    },
  }
)

const popoverVariants = cva(
  "p-0",
  {
    variants: {
      size: {
        sm: "w-[150px]",
        md: "w-[200px]",
        lg: "w-[250px]",
      },
    },
    defaultVariants: {
      size: "md",
    },
  }
)

export interface ComboBoxOption {
  value: string
  label: string
  description?: string
  icon?: React.ReactNode
  disabled?: boolean
}

export interface ComboBoxGroup {
  label: string
  options: ComboBoxOption[]
}

interface ComboBoxProps extends VariantProps<typeof comboboxVariants> {
  options?: ComboBoxOption[]
  groups?: ComboBoxGroup[]
  value: string
  onChange: (value: string) => void
  placeholder?: string
  searchPlaceholder?: string
  emptyMessage?: string
  className?: string
  disabled?: boolean
  loading?: boolean
  clearable?: boolean
  renderOption?: (option: ComboBoxOption) => React.ReactNode
  onSearch?: (query: string) => void
}

export function ComboBox({
  options = [],
  groups = [],
  value,
  onChange,
  placeholder = "Select...",
  searchPlaceholder = "Search...",
  emptyMessage = "No results found.",
  className,
  size,
  disabled = false,
  loading = false,
  clearable = false,
  renderOption,
  onSearch,
}: ComboBoxProps) {
  const [open, setOpen] = React.useState(false)
  const [searchQuery, setSearchQuery] = React.useState("")

  // Combine options and groups into a single array
  const allOptions = React.useMemo(() => {
    const directOptions = options || []
    const groupOptions = groups?.flatMap(group => group.options) || []
    return [...directOptions, ...groupOptions]
  }, [options, groups])

  const filteredData = React.useMemo(() => {
    if (!searchQuery) {
      return {
        options: options || [],
        groups: groups || []
      }
    }

    const lowerQuery = searchQuery.toLowerCase()
    const filterOption = (option: ComboBoxOption) =>
      option.label.toLowerCase().includes(lowerQuery) ||
      option.value.toLowerCase().includes(lowerQuery) ||
      option.description?.toLowerCase().includes(lowerQuery)

    const filteredOptions = (options || []).filter(filterOption)
    const filteredGroups = (groups || []).map(group => ({
      ...group,
      options: group.options.filter(filterOption)
    })).filter(group => group.options.length > 0)

    return {
      options: filteredOptions,
      groups: filteredGroups
    }
  }, [options, groups, searchQuery])

  const selectedOption = allOptions.find((option) => option.value === value)

  const handleSearch = (query: string) => {
    setSearchQuery(query)
    onSearch?.(query)
  }

  const handleSelect = (selectedValue: string) => {
    if (disabled) return

    const newValue = selectedValue === value ? "" : selectedValue
    onChange(newValue)
    setOpen(false)
    setSearchQuery("")
  }

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation()
    if (disabled) return
    onChange("")
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (disabled) return

    if (e.key === "Escape") {
      setOpen(false)
      setSearchQuery("")
    } else if (e.key === "Enter" && !open) {
      setOpen(true)
    }
  }

  const renderOptionContent = (option: ComboBoxOption) => {
    if (renderOption) {
      return renderOption(option)
    }

    return (
      <div className="flex items-center gap-2 flex-1">
        {option.icon && <span className="shrink-0">{option.icon}</span>}
        <div className="flex flex-col">
          <span>{option.label}</span>
          {option.description && (
            <span className="text-xs text-muted-foreground">{option.description}</span>
          )}
        </div>
      </div>
    )
  }

  return (
    <Popover open={open && !disabled} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          aria-haspopup="listbox"
          disabled={disabled}
          onKeyDown={handleKeyDown}
          className={cn(
            comboboxVariants({ size }),
            disabled && "cursor-not-allowed opacity-50",
            className
          )}
        >
          <div className="flex items-center gap-2 flex-1 min-w-0">
            {loading ? (
              <Loader2 className="h-4 w-4 animate-spin shrink-0" />
            ) : selectedOption?.icon ? (
              <span className="shrink-0">{selectedOption.icon}</span>
            ) : null}
            <span className="truncate">
              {selectedOption ? selectedOption.label : placeholder}
            </span>
          </div>

          <div className="flex items-center gap-1 shrink-0">
            {clearable && value && !disabled && !loading && (
              <button
                type="button"
                onClick={handleClear}
                className="h-4 w-4 rounded-sm opacity-50 hover:opacity-100 hover:bg-accent"
                tabIndex={-1}
              >
                <X className="h-3 w-3" />
              </button>
            )}
            <ChevronsUpDown className="h-4 w-4 opacity-50" />
          </div>
        </Button>
      </PopoverTrigger>

      <PopoverContent
        className={cn(popoverVariants({ size }))}
        align="start"
        sideOffset={4}
      >
        <Command shouldFilter={false}>
          <CommandInput
            placeholder={searchPlaceholder}
            value={searchQuery}
            onValueChange={handleSearch}
            disabled={loading}
          />
          <CommandList>
            {loading ? (
              <div className="flex items-center justify-center py-6">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span className="ml-2 text-sm text-muted-foreground">Loading...</span>
              </div>
            ) : (
              <>
                <CommandEmpty>{emptyMessage}</CommandEmpty>

                {/* Render direct options */}
                {filteredData.options.length > 0 && (
                  <CommandGroup>
                    {filteredData.options.map((option) => (
                      <CommandItem
                        key={option.value}
                        value={option.value}
                        disabled={option.disabled}
                        onSelect={handleSelect}
                        className={cn(
                          "flex items-center gap-2",
                          option.disabled && "opacity-50 cursor-not-allowed"
                        )}
                      >
                        <Check
                          className={cn(
                            "h-4 w-4 shrink-0",
                            value === option.value ? "opacity-100" : "opacity-0"
                          )}
                        />
                        {renderOptionContent(option)}
                      </CommandItem>
                    ))}
                  </CommandGroup>
                )}

                {/* Render grouped options */}
                {filteredData.groups.map((group) => (
                  <CommandGroup key={group.label} heading={group.label}>
                    {group.options.map((option) => (
                      <CommandItem
                        key={option.value}
                        value={option.value}
                        disabled={option.disabled}
                        onSelect={handleSelect}
                        className={cn(
                          "flex items-center gap-2",
                          option.disabled && "opacity-50 cursor-not-allowed"
                        )}
                      >
                        <Check
                          className={cn(
                            "h-4 w-4 shrink-0",
                            value === option.value ? "opacity-100" : "opacity-0"
                          )}
                        />
                        {renderOptionContent(option)}
                      </CommandItem>
                    ))}
                  </CommandGroup>
                ))}
              </>
            )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}
