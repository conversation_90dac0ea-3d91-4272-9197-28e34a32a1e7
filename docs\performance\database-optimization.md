# Laravel 12 Database Optimization Guide

## Table of Contents
1. [Database Structure Optimization](#database-structure)
2. [Eloquent Performance Tips](#eloquent-performance)
3. [Query Optimization](#query-optimization)
4. [Caching Strategies](#caching-strategies)
5. [Indexing Best Practices](#indexing)
6. [Advanced Optimization](#advanced-optimization)
7. [Accounting-Specific Optimizations](#accounting-specific)

## Database Structure
### Relationship Design
- Choose appropriate relationships:
  - Use `belongsTo` for direct one-to-one relationships
  - Use `belongsToMany` for many-to-many with pivot tables
  - Consider `hasOne` through for nested relationships
- Properly index foreign keys
- Use composite indexes for frequently queried combinations

### Table Design
- Use appropriate data types (e.g., `uuid` vs `bigInteger`)
- Implement soft deletes when necessary
- Consider partitioning for large tables
- Use JSON columns for flexible attributes
- Consider enum vs integer for status fields

## Eloquent Performance
### Eager Loading
```php
// Instead of:
$books = Book::all();
foreach ($books as $book) {
    echo $book->author->name;
}

// Use:
$books = Book::with('author')->get();

// Even better - select specific columns:
$books = Book::with(['author' => function($query) {
    $query->select('id', 'name');
}])->select('id', 'title', 'author_id')->get();
```

### Chunking Large Data Sets
```php
// Basic chunking
Book::chunk(1000, function ($books) {
    foreach ($books as $book) {
        // Process each book
    }
});

// Using cursor for memory efficiency
foreach (Book::cursor() as $book) {
    // Process one at a time
}

// Lazy collection for better memory management
Book::lazy()->each(function ($book) {
    // Process each book without loading all into memory
});
```

### Performance Tips
```php
// Use whereIn instead of multiple where clauses
Book::whereIn('category_id', [1, 2, 3])->get();

// Use select() to limit columns
Book::select(['id', 'title', 'author_id'])->get();

// Use pluck() for single columns
$titles = Book::pluck('title');

// Use joins instead of whereHas when possible
$books = Book::join('authors', 'books.author_id', '=', 'authors.id')
    ->where('authors.active', true)
    ->select('books.*')
    ->get();
```

## Query Optimization
### Query Builder Best Practices
```php
// Use whereIn for multiple conditions
User::whereIn('status', ['active', 'pending'])->get();

// Use whereBetween for range queries
Order::whereBetween('created_at', [$startDate, $endDate])->get();

// Use subqueries effectively
$users = User::addSelect(['last_login' => Login::select('created_at')
    ->whereColumn('user_id', 'users.id')
    ->latest()
    ->limit(1)
])->get();
```

### Raw Queries
```php
// Use raw queries for complex operations
DB::select(DB::raw('SELECT * FROM books WHERE MATCH(title) AGAINST(?)'), [$searchTerm]);

// Use DB::raw in select
Book::select(DB::raw('COUNT(*) as book_count, category_id'))
    ->groupBy('category_id')
    ->having('book_count', '>', 10)
    ->get();
```

## Caching Strategies
### Query Cache
```php
// Cache expensive queries
$users = Cache::remember('users', 3600, function () {
    return User::with('posts')->get();
});

// Cache with tags
Cache::tags(['books', 'authors'])
    ->remember('key', 3600, function () {
        return Book::with('author')->get();
    });
```

### Model Cache
```php
class Book extends Model
{
    public static function getByCategory($category)
    {
        return Cache::tags(['books'])
            ->remember("books.{$category}", 3600, function () use ($category) {
                return static::where('category', $category)
                    ->with('author')
                    ->get();
            });
    }
}
```

## Indexing
### Basic Indexes
```php
Schema::table('books', function (Blueprint $table) {
    // Single column index
    $table->index('title');
    
    // Composite index
    $table->index(['author_id', 'published_at']);
    
    // Unique index
    $table->unique('isbn');
});
```

### Full-Text Search Indexes
```php
Schema::table('books', function (Blueprint $table) {
    $table->fullText(['title', 'description']);
});

// Using full-text search
Book::whereFullText(['title', 'description'], $searchTerm)->get();
```

## Advanced Optimization
### Database Configuration
```php
// config/database.php
'mysql' => [
    'strict' => false, // Consider disabling strict mode for better performance
    'engine' => 'InnoDB',
    'prefix_indexes' => true,
    'options' => extension_loaded('pdo_mysql') ? array_filter([
        PDO::MYSQL_ATTR_SSL_CA => env('MYSQL_ATTR_SSL_CA'),
        PDO::ATTR_EMULATE_PREPARES => true, // Better performance for prepared statements
    ]) : [],
],
```

### Query Monitoring
```php
// Log slow queries
DB::listen(function ($query) {
    if ($query->time > 100) { // 100ms threshold
        Log::channel('queries')
            ->warning('Slow query detected', [
                'sql' => $query->sql,
                'bindings' => $query->bindings,
                'time' => $query->time
            ]);
    }
});
```

### Performance Testing
```php
// Using database transactions in tests
class BookTest extends TestCase
{
    use RefreshDatabase;

    public function test_book_creation_performance()
    {
        $startTime = microtime(true);
        
        Book::factory()->count(1000)->create();
        
        $endTime = microtime(true);
        $executionTime = ($endTime - $startTime);
        
        $this->assertLessThan(
            5.0, // 5 seconds threshold
            $executionTime,
            "Book creation took too long: {$executionTime} seconds"
        );
    }
}
```

## Accounting-Specific Optimizations

### Financial Data Integrity
```php
// Using decimal type for financial columns
Schema::create('transactions', function (Blueprint $table) {
    $table->id();
    $table->decimal('amount', 15, 4); // 15 digits with 4 decimal places
    $table->string('currency', 3);
    $table->foreignId('account_id')->constrained()->onDelete('restrict');
    $table->string('type'); // credit/debit
    $table->timestamps();
    
    // Ensure data integrity
    $table->index(['account_id', 'created_at']);
    $table->index(['type', 'created_at']);
});
```

### Efficient Balance Calculations
```php
class Account extends Model
{
    public function getBalanceAttribute()
    {
        return Cache::tags(["account-{$this->id}"])
            ->remember("balance-{$this->id}", 60, function () {
                return $this->transactions()
                    ->selectRaw('
                        SUM(CASE 
                            WHEN type = "credit" THEN amount 
                            ELSE -amount 
                        END) as balance
                    ')
                    ->value('balance') ?? 0;
            });
    }
    
    public function scopeWithBalance($query)
    {
        return $query->addSelect([
            'current_balance' => Transaction::selectRaw('
                SUM(CASE 
                    WHEN type = "credit" THEN amount 
                    ELSE -amount 
                END)
            ')
            ->whereColumn('account_id', 'accounts.id')
            ->limit(1)
        ]);
    }
}
```

### Partitioning for Historical Data
```php
// Migration for partitioned transactions
Schema::create('transactions', function (Blueprint $table) {
    $table->id();
    $table->timestamp('transaction_date');
    // ... other columns ...
    
    // Partition by transaction date
    DB::statement('
        ALTER TABLE transactions
        PARTITION BY RANGE (UNIX_TIMESTAMP(transaction_date)) (
            PARTITION p_2023_q1 VALUES LESS THAN (UNIX_TIMESTAMP("2023-04-01")),
            PARTITION p_2023_q2 VALUES LESS THAN (UNIX_TIMESTAMP("2023-07-01")),
            PARTITION p_2023_q3 VALUES LESS THAN (UNIX_TIMESTAMP("2023-10-01")),
            PARTITION p_2023_q4 VALUES LESS THAN (UNIX_TIMESTAMP("2024-01-01")),
            PARTITION p_future VALUES LESS THAN MAXVALUE
        )
    ');
});
```

### Optimized Financial Reporting
```php
class FinancialReport extends Model
{
    public function generateTrialBalance()
    {
        return DB::table('accounts')
            ->leftJoin('transactions', 'accounts.id', '=', 'transactions.account_id')
            ->select([
                'accounts.id',
                'accounts.name',
                DB::raw('
                    SUM(CASE 
                        WHEN transactions.type = "debit" THEN transactions.amount 
                        ELSE 0 
                    END) as total_debits
                '),
                DB::raw('
                    SUM(CASE 
                        WHEN transactions.type = "credit" THEN transactions.amount 
                        ELSE 0 
                    END) as total_credits
                ')
            ])
            ->groupBy('accounts.id', 'accounts.name')
            ->having('total_debits', '>', 0)
            ->orHaving('total_credits', '>', 0)
            ->orderBy('accounts.name');
    }
}
```

### Materialized Views for Reports
```php
class CreateAccountBalanceView extends Migration
{
    public function up()
    {
        DB::statement("
            CREATE MATERIALIZED VIEW account_balances AS
            SELECT 
                account_id,
                SUM(CASE 
                    WHEN type = 'credit' THEN amount 
                    ELSE -amount 
                END) as balance,
                COUNT(*) as transaction_count,
                MAX(created_at) as last_transaction_at
            FROM transactions
            GROUP BY account_id
        ");
        
        // Create index on the materialized view
        DB::statement('CREATE INDEX account_balances_account_id_idx ON account_balances(account_id)');
    }
}
```

### Efficient Journal Entry Processing
```php
class JournalEntry extends Model
{
    public function validateBalance()
    {
        return $this->lines()
            ->selectRaw('
                ABS(SUM(CASE 
                    WHEN type = "debit" THEN amount 
                    ELSE -amount 
                END)) as difference
            ')
            ->having('difference', '>', 0.0001) // Allow for small rounding differences
            ->exists();
    }
    
    public function scopeUnbalanced($query)
    {
        return $query->whereHas('lines', function ($q) {
            $q->select('journal_entry_id')
                ->groupBy('journal_entry_id')
                ->havingRaw('
                    ABS(SUM(CASE 
                        WHEN type = "debit" THEN amount 
                        ELSE -amount 
                    END)) > ?
                ', [0.0001]);
        });
    }
}
``` 