<?php
namespace App\Http\Controllers\Accounts;

use App\Models\Account;
use App\Models\AccountType;
use App\Models\ChqRefBank;
use App\Models\Currency;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;

class AccountController extends Controller
{
    // List chq_ref_bank accounts
    public function listChqRefBanks(Request $request)
    {
        $query = ChqRefBank::query();

        // Optionally filter by user_id if provided
        if ($request->has('user_id') && $request->input('user_id') !== null) {
            $query->where('user_id', $request->input('user_id'));
        }

        $chqRefBanks = $query->with('user')->get();

        return response()->json($chqRefBanks);
    }
    
    // Add this method to your controller
    public function show(Account $account)
    {
        return response()->json($account->load(['accountType', 'user', 'currency']));  
    }

    // List accounts, optionally filter by type or exclude types
    public function index(Request $request)
    {
        $query = Account::with(['accountType', 'user', 'currency']);

        if ($request->has('account_type_id')) {
            $query->where('account_type_id', $request->account_type_id);
        }

        if ($request->has('exclude_account_type_ids')) {
            $excludeIds = explode(',', $request->exclude_account_type_ids);
            // Sanitize to ensure they are numbers, though Eloquent might handle strings.
            $excludeIds = array_filter(array_map('intval', $excludeIds), fn($id) => $id > 0);
            if (!empty($excludeIds)) {
                $query->whereNotIn('account_type_id', $excludeIds);
            }
        }
        return response()->json($query->get());
    }

    // Create account (admin/editor only)
    public function store(Request $request)
    {
        $authError = $this->authorizeRole(['Admin', 'Editor'], 'create accounts');
        if ($authError) {
            return response()->json(['message' => $authError['message']], $authError['status']);
        }

        // Initial validation for type ID
        $request->validate(['account_type_id' => 'required|exists:account_types,id']);
        $accountTypeId = $request->input('account_type_id');
        $accountType = AccountType::find($accountTypeId);

        if (!$accountType) {
            return response()->json(['message' => 'Invalid account type specified.'], 400);
        }

        // Define base rules required for all account types
        $rules = [
            'account_type_id' => 'required|exists:account_types,id',
            'name' => ['required', 'string', 'max:255'],
            'description' => 'nullable|string|max:65535', // Changed to text
        ];

        // Adjust unique rule and add type-specific rules
        if ($accountType->name === 'chq_ref_bank') {
            $rules['name'][] = Rule::unique('chq_ref_banks', 'name');
            // No other specific fields for chq_ref_bank other than name, description
        } else {
            $rules['name'][] = Rule::unique('accounts', 'name');
            switch ($accountType->name) {
                case 'customer':
                    $rules['contact'] = 'nullable|string|max:100';
                    $rules['email'] = 'nullable|email|max:255';
                    break;
                case 'bank':
                    $rules['ac_holder'] = 'nullable|string|max:255';
                    $rules['ac_number'] = 'nullable|string|max:100';
                    $rules['contact'] = 'nullable|string|max:100';
                    $rules['address'] = 'nullable|string|max:255';
                    break;
                case 'currency_account':
                    $rules['currency_id'] = 'required|exists:currencies,id';
                    $rules['formula'] = 'required|in:m,d';
                    $rules['code'] = 'nullable|string|max:50';
                    break;
                case 'employee':
                    $rules['father_name'] = 'required|string|max:255';
                    $rules['cnic'] = 'required|string|max:30';
                    $rules['contact'] = 'required|string|max:100';
                    $rules['address'] = 'required|string|max:255';
                    $rules['salary'] = 'required|numeric';
                    $rules['joining_date'] = 'required|date';
                    break;
                // For types needing only name/description (expense, cash, income, other, etc.)
                // No additional rules needed beyond the base ones.
                // case 'expense':
                // case 'cash':
                // case 'income':
                // case 'other':
                // case 'partnership':
                // case 'personal':
                // case 'property':
                // case 'stock':
                //    break; // Base rules are sufficient
            }
        }

        // Validate the request based on the dynamic rules
        $data = $request->validate($rules);
        $data['user_id'] = $request->user()->id;

        if ($accountType->name === 'chq_ref_bank') {
            $chqRefBank = ChqRefBank::create($data);
            return response()->json($chqRefBank->load('user'), 201);
        } else {
            return DB::transaction(function () use ($data, $accountType) {
            // Create the main currency account
                $account = Account::create($data);
            
            // If this is a currency account, create a corresponding P&L account
                if ($accountType->name === 'currency_account' && !empty($data['code'])) {
                // Find the Income account type
                    $incomeType = AccountType::where('name', 'income')->first();
                
                    if ($incomeType) {
                        // Create the P&L account
                    $plAccountData = [
                            'account_type_id' => $incomeType->id,
                            'name' => $data['code'] . ' P&L Account',
                            'description' => 'Profit and Loss Account for ' . $data['name'],
                            'user_id' => $data['user_id'],
                            'currency_id' => $data['currency_id'] ?? null,
                    ];
                    
                    Account::create($plAccountData);
                }
            }
            
            return response()->json($account->load('currency'), 201);
        });
        }
    }



    // Update account (admin/editor only)
    public function update(Request $request, $id)
    {
        $authError = $this->authorizeRole(['Admin', 'Editor'], 'update entries');
        if ($authError) {
            return response()->json(['message' => $authError['message']], $authError['status']);
        }

        // Initial validation for type ID
        $request->validate(['account_type_id' => 'required|exists:account_types,id']);
        $accountTypeId = $request->input('account_type_id');
        $accountType = AccountType::find($accountTypeId);

        if (!$accountType) {
            return response()->json(['message' => 'Invalid account type specified.'], 400);
        }

        // Define base rules required for all account types during update
        $rules = [
            'account_type_id' => 'required|exists:account_types,id', // Keep to ensure type isn't changed to something invalid
            'name' => ['required', 'string', 'max:255'],
            'description' => 'nullable|string|max:65535', // Changed to text
        ];

        if ($accountType->name === 'chq_ref_bank') {
            $rules['name'][] = Rule::unique('chq_ref_banks', 'name')->ignore($id);
        } else {
            $rules['name'][] = Rule::unique('accounts', 'name')->ignore($id);
            // Add other type-specific rules for Account updates
            switch ($accountType->name) {
                case 'customer':
                    $rules['contact'] = 'nullable|string|max:100';
                    $rules['email'] = 'nullable|email|max:255';
                    break;
                case 'bank':
                    $rules['ac_holder'] = 'nullable|string|max:255';
                    $rules['ac_number'] = 'nullable|string|max:100';
                    $rules['contact'] = 'nullable|string|max:100';
                    $rules['address'] = 'nullable|string|max:255';
                    break;
                case 'currency_account':
                    $rules['currency_id'] = 'required|exists:currencies,id';
                    $rules['formula'] = 'required|in:m,d';
                    $rules['code'] = 'nullable|string|max:50';
                    break;
                case 'employee':
                    $rules['father_name'] = 'required|string|max:255';
                    $rules['cnic'] = 'required|string|max:30';
                    $rules['contact'] = 'required|string|max:100';
                    $rules['address'] = 'required|string|max:255';
                    $rules['salary'] = 'required|numeric';
                    $rules['joining_date'] = 'required|date';
                    break;
            }
        }

        $data = $request->validate($rules);

        if ($accountType->name === 'chq_ref_bank') {
            $chqRefBank = ChqRefBank::findOrFail($id);
            // user_id is not typically changed on update for chq_ref_bank unless specified
            if ($request->has('user_id') && Auth::user()->role === 'Admin') { // Example: Admin can change owner
                 $data['user_id'] = $request->input('user_id');
            } else {
                unset($data['user_id']); // Don't update user_id if not provided or not authorized
            }
            $chqRefBank->update($data);
            return response()->json($chqRefBank->load('user'));
        } else {
            $account = Account::findOrFail($id);
            // Ensure user_id is not accidentally nulled if not in $data for standard accounts
            // And only allow Admin to change user_id for example
            if ($request->has('user_id') && Auth::user()->role === 'Admin') {
                 $data['user_id'] = $request->input('user_id');
            } else {
                $data['user_id'] = $account->user_id; // Preserve original user_id
            }
            $account->update($data);
            return response()->json($account->load(['accountType', 'user', 'currency']));
        }
    }


    // Delete account (admin only)
    public function destroy(Request $request, $id)
    {
        $authError = $this->authorizeRole(['Admin'], 'delete accounts');
        if ($authError) {
            return response()->json(['message' => $authError['message']], $authError['status']);
        }

        // account_type_id must be passed in request query or body to determine which table to delete from
        $accountTypeId = $request->input('account_type_id');
        if (!$accountTypeId) {
            return response()->json(['message' => 'account_type_id is required to delete an entry.'], 400);
        }

        $accountType = AccountType::find($accountTypeId);
        if (!$accountType) {
            return response()->json(['message' => 'Invalid account type specified.'], 400);
        }

        if ($accountType->name === 'chq_ref_bank') {
            $chqRefBank = ChqRefBank::findOrFail($id);
            $chqRefBank->delete();
            return response()->json(['message' => 'CHQ Ref Bank entry deleted successfully.'], 200);
        } else {
            $account = Account::findOrFail($id);
            $account->delete();
            return response()->json(['message' => 'Account deleted successfully.'], 200);
        }
    }

    // Helper for role-based authorization
    protected function authorizeRole(array $roles, string $action = 'perform this action')
    {
        $user = Auth::user();
        if (!$user) {
            return ['message' => 'You must be logged in to ' . $action . '.', 'status' => 401]; // Unauthorized - Not logged in
        }
        if (!in_array($user->role, $roles)) {
            $requiredRoles = implode(' or ', $roles);
            $message = 'Unauthorized. Only users with the ' . $requiredRoles . ' role can ' . $action . '.';
            if ($user->role === 'Viewer') {
                $message = 'Unauthorized. Viewers cannot ' . $action . '.';
            }
            return ['message' => $message, 'status' => 403]; // Forbidden - Incorrect role
        }
        return null; // Authorized
    }
} 