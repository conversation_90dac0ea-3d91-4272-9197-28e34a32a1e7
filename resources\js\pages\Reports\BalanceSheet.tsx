import AppLayout from '@/layouts/app-layout';
import { Head } from '@inertiajs/react';
import { format } from 'date-fns';
import { CalendarIcon } from 'lucide-react';
import React, { useEffect, useState } from 'react';

import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { cn } from '@/lib/utils';
import axios from 'axios';
import { CheckIcon, ChevronsUpDown } from 'lucide-react';
import { NumericFormat } from 'react-number-format';

interface AccountBalance {
    account_id: number; // Add account_id for key
    account_name: string;
    balance: number;
}

interface AccountTypeGroup {
    account_type: string;
    accounts: AccountBalance[];
    total_balance: number;
}

interface BalanceSheetData {
    grouped_data: { [key: string]: AccountTypeGroup }; // Corrected structure to match backend
    overall_credit_total: number; // Added based on rendering logic
    overall_debit_total: number; // Added based on rendering logic
}

interface AccountTypeOption {
    value: string;
    label: string;
}

export default function BalanceSheet() {
    const [fromDate, setFromDate] = useState<Date | undefined>(new Date(new Date().getFullYear(), 0, 1)); // Default to start of current year
    const [tillDate, setTillDate] = useState<Date | undefined>(new Date()); // Default to current date
    const [balanceData, setBalanceData] = useState<BalanceSheetData | null>(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [accountTypes, setAccountTypes] = useState<AccountTypeOption[]>([]);
    const [selectedAccountType, setSelectedAccountType] = useState<string>('');
    const [accountTypeOpen, setAccountTypeOpen] = useState(false);
    const [balanceFilter, setBalanceFilter] = useState<'all' | 'debit' | 'credit'>('all');
    const [balanceFilterOpen, setBalanceFilterOpen] = useState(false);

    const balanceFilterOptions = [
        { value: 'all', label: 'All' },
        { value: 'debit', label: 'Debit' },
        { value: 'credit', label: 'Credit' },
    ];

    useEffect(() => {
        // Fetch account types on component mount
        axios
            .get<AccountTypeOption[]>('/api/account-types') // Assuming an API endpoint for account types
            .then((response) => {
                setAccountTypes(response.data);
            })
            .catch((error) => console.error('Error fetching account types:', error));

        // Fetch initial data on component mount (from beginning to till date)
        handleViewClick();
    }, []); // Empty dependency array means this runs once on mount

    const handleViewClick = () => {
        if (!fromDate || !tillDate) {
            alert('Please select both From Date and Till Date.');
            return;
        }

        setLoading(true);
        setError(null);
        const formattedFromDate = format(fromDate, 'yyyy-MM-dd');
        const formattedTillDate = format(tillDate, 'yyyy-MM-dd');

        const params = new URLSearchParams({
            fromDate: formattedFromDate,
            tillDate: formattedTillDate,
        });

        if (selectedAccountType) {
            params.append('accountType', selectedAccountType);
        }

        if (balanceFilter !== 'all') {
            params.append('balanceFilter', balanceFilter);
        }

        axios
            .get<BalanceSheetData>(`/api/reports/balance-sheet?${params.toString()}`)
            .then((response) => {
                setBalanceData(response.data);
            })
            .catch((err) => {
                console.error('Error fetching balance sheet data:', err);
                setError('Failed to fetch balance sheet data.');
                setBalanceData(null);
            })
            .finally(() => {
                setLoading(false);
            });
    };

    return (
        <AppLayout>
            <Head title="Balance Sheet" />

            <div className="container mx-auto px-6 py-6">
                <h2 className="mb-4 text-2xl font-bold">Balance Sheet</h2>

                <div className="mb-10 flex flex-wrap items-center gap-4 md:flex-nowrap">
                    {/* From Date DatePicker */}
                    <Popover>
                        <PopoverTrigger asChild>
                            <Button
                                variant={'outline'}
                                className={cn('w-[180px] justify-start text-left font-normal', !fromDate && 'text-muted-foreground')}
                            >
                                <CalendarIcon className="mr-2 h-4 w-4" />
                                {fromDate ? format(fromDate, 'PPP') : <span>From Date</span>}
                            </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                            <Calendar mode="single" selected={fromDate} onSelect={setFromDate} initialFocus />
                        </PopoverContent>
                    </Popover>

                    {/* Till Date DatePicker */}
                    <Popover>
                        <PopoverTrigger asChild>
                            <Button
                                variant={'outline'}
                                className={cn('w-[180px] justify-start text-left font-normal', !tillDate && 'text-muted-foreground')}
                            >
                                <CalendarIcon className="mr-2 h-4 w-4" />
                                {tillDate ? format(tillDate, 'PPP') : <span>Till Date</span>}
                            </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                            <Calendar mode="single" selected={tillDate} onSelect={setTillDate} initialFocus />
                        </PopoverContent>
                    </Popover>
                    {/* Account Type ComboBox */}
                    <Popover open={accountTypeOpen} onOpenChange={setAccountTypeOpen}>
                        <PopoverTrigger asChild>
                            <Button variant="outline" role="combobox" aria-expanded={accountTypeOpen} className="w-[200px] justify-between">
                                {selectedAccountType
                                    ? accountTypes.find((type) => type.value === selectedAccountType)?.label
                                    : 'Select Account Type...'}
                                <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                            </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-[200px] p-0">
                            <Command>
                                <CommandInput placeholder="Search account type..." />
                                <CommandEmpty>No account type found.</CommandEmpty>
                                <CommandGroup>
                                    {accountTypes.map((type) => (
                                        <CommandItem
                                            key={type.value}
                                            value={type.value}
                                            onSelect={(currentValue) => {
                                                setSelectedAccountType(currentValue === selectedAccountType ? '' : currentValue);
                                                setAccountTypeOpen(false);
                                            }}
                                        >
                                            <CheckIcon
                                                className={cn('mr-2 h-4 w-4', selectedAccountType === type.value ? 'opacity-100' : 'opacity-0')}
                                            />
                                            {type.label}
                                        </CommandItem>
                                    ))}
                                </CommandGroup>
                            </Command>
                        </PopoverContent>
                    </Popover>

                    {/* Debit/Credit ComboBox */}
                    <Popover open={balanceFilterOpen} onOpenChange={setBalanceFilterOpen}>
                        <PopoverTrigger asChild>
                            <Button variant="outline" role="combobox" aria-expanded={balanceFilterOpen} className="w-[150px] justify-between">
                                {balanceFilterOptions.find((option) => option.value === balanceFilter)?.label}
                                <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                            </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-[150px] p-0">
                            <Command>
                                <CommandGroup>
                                    {balanceFilterOptions.map((option) => (
                                        <CommandItem
                                            key={option.value}
                                            value={option.value}
                                            onSelect={(currentValue) => {
                                                setBalanceFilter(currentValue as 'all' | 'debit' | 'credit');
                                                setBalanceFilterOpen(false);
                                            }}
                                        >
                                            <CheckIcon className={cn('mr-2 h-4 w-4', balanceFilter === option.value ? 'opacity-100' : 'opacity-0')} />
                                            {option.label}
                                        </CommandItem>
                                    ))}
                                </CommandGroup>
                            </Command>
                        </PopoverContent>
                    </Popover>

                    {/* Buttons */}
                    <Button onClick={handleViewClick} disabled={loading}>
                        {loading ? 'Loading...' : 'Filter'}
                    </Button>
                    <Button variant="secondary">Print</Button>
                    <Button variant="secondary">PDF</Button>
                </div>

                {error && <div className="mb-4 text-red-500">{error}</div>}

                {/* Balance Sheet Table */}
                {loading && <p>Loading...</p>}
                {error && <p className="text-red-500">Error: {error}</p>}

                {loading && <p>Loading...</p>}
                {error && <p className="text-red-500">Error: {error}</p>}

                {balanceData &&
                    (() => {
                        let overallDebitTotal = 0;
                        let overallCreditTotal = 0;
                        Object.values(balanceData.grouped_data).forEach((group) => {
                            group.accounts.forEach((account) => {
                                if (account.balance > 0) {
                                    overallDebitTotal += Number(account.balance);
                                } else if (account.balance < 0) {
                                    overallCreditTotal += Math.abs(Number(account.balance));
                                }
                            });
                        });
                        const balanceOut = overallDebitTotal - overallCreditTotal;
                        return (
                            <div className="flex flex-col md:flex-row md:gap-8">
                                {/* Balance Sheet Table */}
                                <div className="w-full md:flex-grow">
                                    <Table>
                                        <TableHeader>
                                            <TableRow>
                                                <TableHead>Account Name</TableHead>
                                                <TableHead className="text-right">Debit</TableHead>
                                                <TableHead className="text-right">Credit</TableHead>
                                            </TableRow>
                                        </TableHeader>
                                        <TableBody>
                                            {/* Iterate over grouped_data */}
                                            {Object.entries(balanceData.grouped_data).map(([accountTypeKey, accountTypeGroup]) => (
                                                <React.Fragment key={accountTypeKey}>
                                                    <TableRow className="bg-gray-200 font-semibold dark:bg-stone-800">
                                                        <TableCell colSpan={3} className="text-center">
                                                            {accountTypeGroup.account_type.toUpperCase()}
                                                        </TableCell>
                                                    </TableRow>
                                                    {/* Iterate over accounts within this specific group */}
                                                    {accountTypeGroup.accounts.map((account) => (
                                                        <TableRow key={account.account_id}>
                                                            <TableCell>{account.account_name}</TableCell>
                                                            <TableCell className="text-right">
                                                                <NumericFormat
                                                                    value={account.balance > 0 ? account.balance : 0}
                                                                    displayType={'text'}
                                                                    thousandSeparator={true}
                                                                    decimalScale={0}
                                                                    fixedDecimalScale={true}
                                                                />
                                                            </TableCell>
                                                            <TableCell className="text-right">
                                                                <NumericFormat
                                                                    value={account.balance < 0 ? Math.abs(account.balance) : 0}
                                                                    displayType={'text'}
                                                                    thousandSeparator={true}
                                                                    decimalScale={0}
                                                                    fixedDecimalScale={true}
                                                                />
                                                            </TableCell>
                                                        </TableRow>
                                                    ))}
                                                    {/* Total balance for this group */}
                                                    <TableRow className="bg-rose-100 font-bold dark:bg-stone-900">
                                                        <TableCell className="text-center">Total</TableCell>
                                                        <TableCell className="text-right">
                                                            <NumericFormat
                                                                value={accountTypeGroup.accounts.reduce(
                                                                    (sum, acc) => sum + (acc.balance > 0 ? Number(acc.balance) : 0),
                                                                    0,
                                                                )}
                                                                displayType={'text'}
                                                                thousandSeparator={true}
                                                                decimalScale={0}
                                                                fixedDecimalScale={true}
                                                            />
                                                        </TableCell>
                                                        <TableCell className="text-right">
                                                            <NumericFormat
                                                                value={accountTypeGroup.accounts.reduce(
                                                                    (sum, acc) => sum + (acc.balance < 0 ? Math.abs(Number(acc.balance)) : 0),
                                                                    0,
                                                                )}
                                                                displayType={'text'}
                                                                thousandSeparator={true}
                                                                decimalScale={0}
                                                                fixedDecimalScale={true}
                                                            />
                                                        </TableCell>
                                                    </TableRow>
                                                </React.Fragment>
                                            ))}
                                        </TableBody>
                                    </Table>
                                </div>

                                {/* Total Balance Section */}
                                <div className="mt-8 w-full md:mt-0 md:w-1/4">
                                    <div className="py-2">
                                        <h3 className="mb-3 text-lg font-semibold">Total Balance</h3>
                                        <div className="mb-2 flex justify-between">
                                            <span>Total Debit:</span>
                                            <NumericFormat
                                                value={overallDebitTotal}
                                                displayType={'text'}
                                                thousandSeparator={true}
                                                decimalScale={0}
                                                fixedDecimalScale={true}
                                            />
                                        </div>
                                        <div className="mb-4 flex justify-between">
                                            <span>Total Credit:</span>
                                            <NumericFormat
                                                value={overallCreditTotal}
                                                displayType={'text'}
                                                thousandSeparator={true}
                                                decimalScale={0}
                                                fixedDecimalScale={true}
                                            />
                                        </div>
                                        <Button
                                            className={`w-full text-white ${balanceOut === 0 ? 'bg-green-500 hover:bg-green-600' : 'bg-red-500 hover:bg-red-600'}`}
                                        >
                                            Balance OUT:{' '}
                                            <NumericFormat
                                                value={balanceOut}
                                                displayType={'text'}
                                                thousandSeparator={true}
                                                decimalScale={0}
                                                fixedDecimalScale={true}
                                                className="ml-1"
                                            />
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        );
                    })()}
            </div>
        </AppLayout>
    );
}
