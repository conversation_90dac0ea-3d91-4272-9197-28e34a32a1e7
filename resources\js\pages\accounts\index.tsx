import React, { useEffect, useState } from 'react';
import { DataTable } from '@/components/ui/data-table';
import type { ColumnDef } from '@tanstack/react-table';
import AppLayout from '@/layouts/app-layout';
import { Button } from '@/components/ui/button';
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverTrigger, PopoverContent } from "@/components/ui/popover";
import { Calendar as CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { Dialog, DialogContent, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { ComboBox } from '@/components/ui/combobox';
import axios, { AxiosResponse } from 'axios';
import { toast } from 'sonner';
import { Pencil, Trash2 } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import { AlertDialog, AlertDialogContent, AlertDialogTitle, AlertDialogDescription, AlertDialogFooter, AlertDialogCancel, AlertDialogAction,
} from '@/components/ui/alert-dialog';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Accounts',
        href: '/accounts',
    },
];

// Define interfaces for better type safety
interface AccountType {
  id: number;
  name: string;
  label: string;
}

interface Currency {
  id: number;
  name: string;
  code: string;
}

interface Account {
  id: number;
  name?: string;
  contact?: string;
  email?: string;
  description?: string;
  ac_holder?: string;
  ac_number?: string;
  address?: string;
  salary?: string | number;
  joining_date?: string;
  currency?: string | Currency; // Allow both string and object
  formula?: string;
  code?: string;
  cnic?: string;
  user_id?: string | number;
  [key: string]: string | number | Currency | undefined | null;
}

// Account type columns config (keeps track of which *display* columns to show)
const accountTypeColumns: Record<string, string[]> = {
  customer: ['Name', 'Contact', 'Email', 'Description', 'ID', 'Action'],
  bank: ['Name', 'A/C Holder', 'A/C #', 'Contact', 'Address', 'ID', 'Action'],
  expense: ['Name', 'Description', 'ID', 'Action'],
  chq_ref_bank: ['Name', 'Description', 'ID', 'Action'],
  currency_account: [ 'Name', 'Currency', 'Formula', 'Code', 'ID', 'Action'],
  employee: ['Name', 'Father Name', 'CNIC', 'Contact', 'Address', 'Salary', 'Joining Date', 'ID', 'Action'],
  cash: ['Name', 'Description', 'ID', 'Action'],
  income: ['Name', 'Description', 'ID', 'Action'],
  other: ['Name', 'Description', 'ID', 'Action'],
  partnership: ['Name', 'Description', 'ID', 'Action'],
  personal: ['Name', 'Description', 'ID', 'Action'],
  property: ['Name', 'Description', 'ID', 'Action'],
  stock: ['Name', 'Description', 'ID', 'Action'],
};

// Mapping from Display Name to Backend Key (snake_case)
const columnKeyMapping: Record<string, string> = {
  'Name': 'name',
  'Contact': 'contact',
  'Email': 'email',
  'Description': 'description',
  'ID': 'id',
  'A/C Holder': 'ac_holder',
  'A/C #': 'ac_number',
  'Address': 'address',
  'Currency': 'currency',
  'Formula': 'formula',
  'Code': 'code',
  'Father Name': 'father_name',
  'CNIC': 'cnic',
  'Salary': 'salary',
  'Joining Date': 'joining_date',
  'Action': 'action' // Special case
};

const Page = () => {
  const [accountTypes, setAccountTypes] = useState<AccountType[]>([]);
  const [activeType, setActiveType] = useState<string>('');
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [currenciesData, setCurrenciesData] = useState<Currency[]>([]);
  const [modalOpen, setModalOpen] = useState(false);
  const [form, setForm] = useState<Partial<Account & Currency>>({});
  const [editingId, setEditingId] = useState<number | null>(null);
  const [loading, setLoading] = useState(false);
  const [loadingData, setLoadingData] = useState(false);
  const [currenciesDropdown, setCurrenciesDropdown] = useState<Currency[]>([]);
  const [currencyDropdownLoading, setCurrencyDropdownLoading] = useState(false);
  const [showCurrencyModal, setShowCurrencyModal] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [pendingDeleteRow, setPendingDeleteRow] = useState<Account | Currency | null>(null);
  const [joiningDate, setJoiningDate] = useState<Date | undefined>(
    form.joining_date && typeof form.joining_date === 'string' && !isNaN(Date.parse(form.joining_date))
      ? new Date(form.joining_date)
      : undefined
  );

  // Fetch account types
  useEffect(() => {
    axios.get<AccountType[]>('/api/account-types').then(res => {
      setAccountTypes(res.data);
      setActiveType(prev => prev || (res.data.length > 0 ? res.data[0].name : ''));
    });
  }, []);

  // Fetch data (accounts or currencies) when activeType changes
  useEffect(() => {
    if (!activeType) return;

    setLoadingData(true);
    if (activeType === 'currency') {
      axios.get<Currency[]>('/api/currencies')
        .then(res => {
            setCurrenciesData(res.data);
            setAccounts([]);
        })
        .catch(() => toast.error('Failed to fetch currencies'))
        .finally(() => setLoadingData(false));
    } else {
      // Special case for chq_ref_bank: fetch from chq_ref_banks table via API
      if (activeType === 'chq_ref_bank') {
        const currentType = accountTypes.find(t => t.name === activeType);
        if (currentType) {
          axios.get('/api/chq-ref-banks', { params: { user_id: null } }) // Adjust params as needed
            .then(res => {
              setAccounts(res.data);
              setCurrenciesData([]);
            })
            .catch(() => toast.error('Failed to fetch CHQ Ref Banks'))
            .finally(() => setLoadingData(false));
        } else {
          setLoadingData(false);
        }
      } else {
        const currentType = accountTypes.find(t => t.name === activeType);
        if (currentType) {
          axios.get<Account[]>('/api/accounts', { params: { account_type_id: currentType.id } })
            .then(res => {
                setAccounts(res.data);
                setCurrenciesData([]);
            })
            .catch(() => toast.error('Failed to fetch accounts for type: ' + activeType))
            .finally(() => setLoadingData(false));
        } else if (accountTypes.length > 0) {
            setLoadingData(false);
        }
      }
    }
  }, [activeType, accountTypes]);

  // Fetch currencies for dropdown (only when needed)
  useEffect(() => {
    if (modalOpen && activeType === 'currency_account') {
      setCurrencyDropdownLoading(true);
      axios.get<Currency[]>('/api/currencies')
        .then(res => setCurrenciesDropdown(res.data))
        .catch(() => toast.error('Failed to fetch currencies for dropdown'))
        .finally(() => setCurrencyDropdownLoading(false));
    }
  }, [modalOpen, activeType]);

  const comboBoxOptions = accountTypes.map((type) => ({ value: type.name, label: type.label }));

  const formulaOptions = [
    { value: 'm', label: 'Multiple' },
    { value: 'd', label: 'Divide' }
  ];

  const getColumns = (type: string): ColumnDef<Account | Currency>[] => {
    const commonActionColumn: ColumnDef<Account | Currency> = {
      id: 'action',
      header: 'Action',
      cell: ({ row }) => renderActionButtons(row.original),
    };

    // Explicitly handle the 'currency' type first
    if (type === 'currency') {
      const currencyColumns: ColumnDef<Currency>[] = [
        { header: 'Name', accessorKey: 'name' },
        { header: 'Code', accessorKey: 'code' },
        commonActionColumn as ColumnDef<Currency>,
      ];
      return currencyColumns as ColumnDef<Account | Currency>[];
    }

    // Handle account types
    const displayCols = accountTypeColumns[type] || [];
    const accountColumns: ColumnDef<Account | Currency>[] = displayCols
      .map((colName): ColumnDef<Account | Currency> | null => {
        const backendKey = columnKeyMapping[colName];

        if (!backendKey && colName !== 'Action') {
            console.warn(`No backend key found for column: ${colName}`);
            return null;
        }

        if (colName === 'Action') {
          return commonActionColumn;
        }

        // Always define a cell renderer for account columns to handle type safety
        const colDef: ColumnDef<Account | Currency> = {
          id: backendKey || colName.toLowerCase().replace(/ /g, '_'),
          header: colName,
          cell: ({ row }) => {
            const data = row.original;

            // Type guard: Check if it's a Currency object (it shouldn't be in this branch, but safer)
            if ('code' in data && !('account_type_id' in data)) {
                // This case should ideally not happen if logic is correct elsewhere
                return 'Type Error';
            }

            // We can now be more confident it's an Account
            const accountData = data as Account;
            const value = accountData[backendKey as keyof Account];

            // Specific render logic for certain keys
            if (backendKey === 'id') {
                return accountData.id;
            }

            if (backendKey === 'joining_date') {
              if (accountData.joining_date) {
                try {
                  // Ensure the date string is valid before formatting
                  const date = new Date(accountData.joining_date);
                  if (!isNaN(date.getTime())) {
                    return format(date, "dd-MMM-yyyy");
                  }
                  return accountData.joining_date; // Fallback if date is invalid
                // eslint-disable-next-line @typescript-eslint/no-unused-vars
                } catch (e) { 
                  return accountData.joining_date; // Fallback if formatting fails
                }
              }
              return '';
            }

            if (backendKey === 'currency') {
              const currency = accountData.currency;
              if (typeof currency === 'object' && currency !== null && 'name' in currency) {
                return currency.name;
              } else if (typeof currency === 'string' || typeof currency === 'number') {
                const currencyId = Number(currency);
                const matchedCurrency = currenciesData.find(c => c.id === currencyId);
                return matchedCurrency ? matchedCurrency.name : String(currency);
              }
              return '';
            }

            if (backendKey === 'formula') {
              const formulaValue = String(value);
              const option = formulaOptions.find(opt => opt.value === formulaValue);
              return option ? option.label : formulaValue; // Fallback to raw value if no match
            }

            // Default rendering for other keys
            return value !== undefined && value !== null ? String(value) : '';
          },
        };
        return colDef;
      })
      // Correct the type predicate for the filter
      .filter((col): col is ColumnDef<Account | Currency> => col !== null);

    return accountColumns;
  };

  const columns = getColumns(activeType);

  const currentData = activeType === 'currency' ? currenciesData : accounts;

  const renderActionButtons = (row: Account | Currency) => (
    <div className="flex gap-2">
      <button onClick={() => handleEdit(row)} className="p-1 rounded hover:bg-muted transition" aria-label="Edit" type="button">
        <Pencil className="w-4 h-4 text-primary" />
      </button>
      <button onClick={() => handleDeleteRequest(row)} className="p-1 rounded hover:bg-muted transition" aria-label="Delete" type="button">
        <Trash2 className="w-4 h-4 text-destructive" />
      </button>
    </div>
  );

  const handleAddNew = () => {
    setForm({});
    setJoiningDate(undefined);
    setEditingId(null);
    setModalOpen(true);
  };

  const handleEdit = (row: Account | Currency) => {
    setForm({});
    setJoiningDate(undefined);
    setEditingId(row.id);

    if (activeType === 'currency') {
        const currencyRow = row as Currency;
        setForm({ Name: currencyRow.name, Code: currencyRow.code });
    } else {
        const accountRow = row as Account;
        const formState: Partial<Account & Currency> = {};
        Object.entries(columnKeyMapping).forEach(([displayName, backendKey]) => {
            if (backendKey !== 'action' && backendKey in accountRow) {
                const value = accountRow[backendKey as keyof Account];
                formState[displayName as keyof typeof formState] = value;
            }
        });
        if (activeType === 'currency_account' && accountRow.currency) {
            formState.currency_id = (accountRow.currency as Currency).id;
            formState.formula = accountRow.formula;
        }
        if (accountRow.joining_date) {
            const parsedDate = new Date(accountRow.joining_date);
            if (!isNaN(parsedDate.getTime())) {
                setJoiningDate(parsedDate);
            }
        }
        setForm(formState);
    }
    setModalOpen(true);
  };

  const handleDeleteRequest = (row: Account | Currency) => {
    setPendingDeleteRow(row);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (!pendingDeleteRow) return;

    const isCurrency = activeType === 'currency';
    const idToDelete = pendingDeleteRow.id;
    const originalAccounts = [...accounts];
    const originalCurrencies = [...currenciesData];

    if (isCurrency) {
        setCurrenciesData(prev => prev.filter(item => item.id !== idToDelete));
    } else {
        setAccounts(prev => prev.filter(item => item.id !== idToDelete));
    }
    setDeleteDialogOpen(false);
    setPendingDeleteRow(null);
    setLoading(true);

    try {
      if (isCurrency) {
        await axios.delete(`/api/currencies/${idToDelete}`);
        toast.success('Currency deleted successfully');
      } else {
        await axios.delete(`/api/accounts/${idToDelete}`, { data: { account_type_id: activeType ? accountTypes.find(t => t.name === activeType)?.id : null } });
        toast.success('Account deleted successfully');
      }
    } catch {
      toast.error('Failed to delete.');
      if (isCurrency) {
          setCurrenciesData(originalCurrencies);
      } else {
          setAccounts(originalAccounts);
      }
    }
    setLoading(false);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const { name, value } = e.target;
      setForm(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setLoading(true);

    const isCurrency = activeType === 'currency';
    const currentType = isCurrency ? null : accountTypes.find(t => t.name === activeType);
    const tempId = Date.now();

    if (isCurrency) {
        if (!form.Name || !form.Code || typeof form.Code !== 'string' || form.Code.length > 10) {
            toast.error('Currency requires Name and Code (max 10 chars).');
            setLoading(false); return;
        }
    } else {
        if (!currentType) {
            toast.error('Invalid account type selected.');
            setLoading(false); return;
        }
        if (!form.Name) {
             toast.error('Account Name is required.');
             setLoading(false); return;
        }
        if (currentType.name === 'currency_account' && (!form.currency_id || !form.formula)) {
            toast.error('Currency Account requires Currency and Formula.');
            setLoading(false); return;
        }
        if (currentType.name === 'employee' && (!form['Father Name'] || !form.CNIC || !form.Contact || !form.Address || !form.Salary || !joiningDate)) {
            toast.error('Employee requires Father Name, CNIC, Contact, Address, Salary, and Joining Date.');
            setLoading(false); return;
        }
    }

    const backendPayload: Record<string, unknown> = {};
    const optimisticDataPayload: Partial<Account & Currency> = { id: editingId ?? tempId };

    if (isCurrency) {
        const nameStr = String(form.Name ?? '');
        const codeStr = String(form.Code ?? '');
        backendPayload.name = nameStr;
        backendPayload.code = codeStr;
        optimisticDataPayload.name = nameStr;
        optimisticDataPayload.code = codeStr;
    } else if (currentType) {
        backendPayload.account_type_id = currentType.id;
        (optimisticDataPayload as Partial<Account>).account_type_id = currentType.id;

        Object.entries(columnKeyMapping).forEach(([displayName, backendKey]) => {
            const formValue = form[displayName as keyof typeof form];
            if (backendKey !== 'action' && backendKey !== 'id' && backendKey !== 'currency' && formValue !== undefined) {
                backendPayload[backendKey] = formValue;
                (optimisticDataPayload as Record<string, unknown>)[backendKey] = formValue;
            }
        });
        if (joiningDate) {
            const formattedDate = format(joiningDate, 'yyyy-MM-dd');
            backendPayload.joining_date = formattedDate;
            (optimisticDataPayload as Partial<Account>).joining_date = formattedDate;
        }
        if (currentType.name === 'currency_account') {
            backendPayload.currency_id = form.currency_id || null;
            backendPayload.formula = form.formula || null;
            const selectedCurrency = currenciesDropdown.find(c => c.id === form.currency_id);
            (optimisticDataPayload as Partial<Account>).currency = selectedCurrency;
            if (form.formula) {
                 (optimisticDataPayload as Partial<Account>).formula = form.formula;
            }
        }
        Object.keys(backendPayload).forEach(key => {
            if (backendPayload[key] === undefined || backendPayload[key] === null || backendPayload[key] === '') {
                delete backendPayload[key];
            }
        });
        if (!editingId) {
           (optimisticDataPayload as Partial<Account>).created_at = new Date().toISOString();
        }
    }

    const originalAccounts = [...accounts];
    const originalCurrencies = [...currenciesData];

    if (editingId) {
        if (isCurrency) {
            const optimisticCurrency = { ...originalCurrencies.find(c => c.id === editingId), ...optimisticDataPayload } as Currency;
            setCurrenciesData(prev => prev.map(item => (item.id === editingId ? optimisticCurrency : item)));
        } else {
            const optimisticAccount = { ...originalAccounts.find(a => a.id === editingId), ...optimisticDataPayload } as Account;
            setAccounts(prev => prev.map(item => (item.id === editingId ? optimisticAccount : item)));
        }
    } else {
        if (isCurrency) {
            setCurrenciesData(prev => [optimisticDataPayload as Currency, ...prev]);
        } else {
            setAccounts(prev => [optimisticDataPayload as Account, ...prev]);
        }
    }
    setModalOpen(false);

    try {
      let response: AxiosResponse<Account | Currency>;
      if (isCurrency) {
        if (editingId) {
          response = await axios.put<Currency>(`/api/currencies/${editingId}`, backendPayload);
          toast.success('Currency updated successfully');
          setCurrenciesData(prev => prev.map(item => (item.id === editingId ? response.data as Currency : item)));
        } else {
          response = await axios.post<Currency>('/api/currencies', backendPayload);
          toast.success('Currency created successfully');
          setCurrenciesData(prev => prev.map(item => (item.id === tempId ? response.data as Currency : item)));
        }
      } else {
          if (editingId) {
            response = await axios.put<Account>(`/api/accounts/${editingId}`, backendPayload);
            toast.success('Account updated successfully');
            setAccounts(prev => prev.map(item => (item.id === editingId ? response.data as Account : item)));
          } else {
            response = await axios.post<Account>('/api/accounts', backendPayload);
            toast.success('Account created successfully');
            setAccounts(prev => prev.map(item => (item.id === tempId ? response.data as Account : item)));
          }
      }
      setEditingId(null);
      setForm({});
      setJoiningDate(undefined);

    } catch (apiError) {
       toast.error(getErrorMessage(apiError, editingId ? 'update' : 'create'));
       if (isCurrency) {
           setCurrenciesData(originalCurrencies);
       } else {
           setAccounts(originalAccounts);
       }
       if (editingId) {
           setModalOpen(true);
       }
    }
    setLoading(false);
  };

  const getErrorMessage = (error: unknown, action: string): string => {
      let message = `Failed to ${action}.`;
      if (axios.isAxiosError(error) && error.response) {
          if (error.response.status === 422 && error.response.data.errors) {
              const validationErrors = error.response.data.errors;
              const firstErrorKey = Object.keys(validationErrors)[0];
              message = validationErrors[firstErrorKey][0];
          } else if (error.response.data && error.response.data.message) {
              message = error.response.data.message;
          }
      }
      return message
  };

  const renderFormFields = () => {
    const isCurrency = activeType === 'currency';
    const isEmployee = activeType === 'employee';
    const isCurrencyAccount = activeType === 'currency_account';
    const isChqRefBank = activeType === 'chq_ref_bank';
    let fieldsToRender: string[] = [];

    if (isCurrency) {
      fieldsToRender = ['Name', 'Code'];
    } else if (isChqRefBank) {
      fieldsToRender = ['Name', 'Description'];
    } else {
      fieldsToRender = (accountTypeColumns[activeType] || []).filter(col => col !== 'Action' && col !== 'ID' && col !== 'Currency' && col !== 'Formula' && col !== 'Joining Date');
    }

    const formKeysSource = form;

    return (
      <div className="grid gap-2 grid-cols-2">
        {fieldsToRender.map(fieldDisplayName => (
          <div key={fieldDisplayName}>
            <label htmlFor={fieldDisplayName}>{fieldDisplayName}</label>
            <Input
              id={fieldDisplayName}
              name={fieldDisplayName}
              value={String(formKeysSource[fieldDisplayName as keyof typeof formKeysSource] ?? '')}
              onChange={handleChange}
              placeholder={fieldDisplayName}
              type={fieldDisplayName === 'Email' ? 'email' : fieldDisplayName === 'Salary' ? 'number' : 'text'}
              required={fieldDisplayName === 'Name' || (isCurrency && fieldDisplayName === 'Code') || (isEmployee && ['Father Name', 'CNIC', 'Contact', 'Address', 'Salary'].includes(fieldDisplayName))}
            />
          </div>
        ))}

        {isCurrencyAccount && (
          <>
            <div key="currency_id">
              <label htmlFor="currency_id">Currency</label>
              <ComboBox
                options={currenciesDropdown.map(c => ({ value: String(c.id), label: `${c.name} (${c.code})` }))}
                value={String(formKeysSource.currency_id || '')}
                onChange={val => setForm(f => ({ ...f, currency_id: Number(val) || null }))}
                placeholder={currencyDropdownLoading ? 'Loading...' : 'Select currency'}
                className="min-w-[200px]"
              />
            </div>
            <div key="formula">
              <label htmlFor="formula">Formula</label>
              <ComboBox
                options={formulaOptions}
                value={formKeysSource.formula || ''}
                onChange={val => setForm(f => ({ ...f, formula: val }))}
                placeholder="Select formula" className="min-w-[200px]"
              />
            </div>
             <div className="flex justify-end mt-2 col-span-2">
                <Button type="button" variant="outline" onClick={() => setShowCurrencyModal(true)}>+ Add Currency</Button>
             </div>
          </>
        )}

        {isEmployee && (
          <div key="joining_date">
            <label htmlFor="Joining Date">Joining Date</label>
            <Popover>
                <PopoverTrigger asChild>
                    <Button variant="outline" className="w-full justify-start text-left font-normal" id="Joining Date">
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {joiningDate ? format(joiningDate, "yyyy-MM-dd") : <span>Pick a date</span>}
                    </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                    <Calendar mode="single" selected={joiningDate} onSelect={date => setJoiningDate(date)} initialFocus />
                </PopoverContent>
            </Popover>
          </div>
        )}
      </div>
    );
  };

  // Table Skeleton Rows for Loading State
  function TableSkeletonRows({ columns, count = 6, rounded = 'rounded' }: { columns: ColumnDef<Account | Currency>[]; count?: number; rounded?: string }) {
    return (
      <>
        {Array.from({ length: count }).map((_, rowIdx) => (
          <tr key={rowIdx}>
            {columns.map((col, colIdx: number) => (
              <td key={col.id ?? colIdx} className="px-4 py-3">
                <Skeleton className={`h-5 w-full ${rounded}`} />
              </td>
            ))}
          </tr>
        ))}
      </>
    );
  }

  // Currency modal for quick add
  const CurrencyModal = ({ open, onOpenChange, onCurrencyAdded }: {
      open: boolean;
      onOpenChange: (open: boolean) => void;
      onCurrencyAdded: () => void;
  }) => {
      const [currencyForm, setCurrencyForm] = useState({ Name: '', Code: ''});
      const [currencySaving, setCurrencySaving] = useState(false);

      const handleCurrencyChange = (e: React.ChangeEvent<HTMLInputElement>) => {
          const { name, value } = e.target;
          setCurrencyForm(prev => ({ ...prev, [name]: value }));
      };

      const handleCurrencySubmit = async (e: React.FormEvent<HTMLFormElement>) => {
          e.preventDefault();
          if (!currencyForm.Name || !currencyForm.Code) return;
          setCurrencySaving(true);
          try {
              await axios.post('/api/currencies', { name: currencyForm.Name, code: currencyForm.Code });
              toast.success('Currency added!');
              setCurrencyForm({ Name: '', Code: '' });
              onOpenChange(false);
              onCurrencyAdded();
          } catch (err) {
              toast.error(getErrorMessage(err, 'add currency'));
          }
          setCurrencySaving(false);
      };

      return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent>
                <DialogTitle>Add Currency</DialogTitle>
                <DialogDescription>Quickly add a new currency.</DialogDescription>
                <form onSubmit={handleCurrencySubmit}>
                    <div className="grid gap-2 md:grid-cols-2 mt-4">
                        <div>
                            <label htmlFor="CurrencyModalName">Name</label>
                            <Input id="CurrencyModalName" name="Name" value={currencyForm.Name} onChange={handleCurrencyChange} required />
                        </div>
                        <div>
                            <label htmlFor="CurrencyModalCode">Code</label>
                            <Input id="CurrencyModalCode" name="Code" value={currencyForm.Code} onChange={handleCurrencyChange} required maxLength={10} />
                        </div>
                    </div>
                    <div className="flex justify-end gap-2 mt-4">
                       <Button type="button" variant="outline" onClick={() => onOpenChange(false)} disabled={currencySaving}>Cancel</Button>
                       <Button type="submit" disabled={currencySaving}>{currencySaving ? 'Adding...' : 'Add Currency'}</Button>
                    </div>
                </form>
            </DialogContent>
        </Dialog>
      );
  }

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Accounts" />
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogTitle>Confirm Delete</AlertDialogTitle>
          <AlertDialogDescription>
            Are you sure you want to delete this item? This action cannot be undone.
          </AlertDialogDescription>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setDeleteDialogOpen(false)} className="bg-muted hover:bg-muted/10">Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete} disabled={loading} className="bg-destructive text-white hover:bg-destructive/70">
              {loading ? 'Deleting...' : 'Delete'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-6">Accounts</h1>
        <div className="flex flex-col md:flex-row md:items-center gap-4 mb-4">
          <span className="font-semibold">Account Type:</span>
          <ComboBox
            options={comboBoxOptions}
            value={activeType}
            onChange={setActiveType}
            className="min-w-[200px]"
          />
          <Button className="ml-auto w-fit" onClick={handleAddNew}>
            Create New {activeType === 'currency' ? 'Currency' : 'Account'}
          </Button>
        </div>

        {activeType && (
          loadingData ? (
            <div className="rounded-md border">
              <table className="min-w-full">
                <thead>
                  <tr>
                    {columns.map((col, idx) => (
                      <th key={col.id || idx} className="px-4 py-2">
                        <Skeleton className="h-6 w-24" />
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  <TableSkeletonRows columns={columns} count={6} />
                </tbody>
              </table>
            </div>
          ) : (
              <DataTable
                columns={columns}
                data={currentData}
                globalFilterPlaceholder={`Search ${activeType}...`}
              />
          )
        )}

        <Dialog open={modalOpen} onOpenChange={setModalOpen}>
          <DialogContent className="max-w-md w-full">
            <DialogTitle>{editingId ? 'Edit' : 'Add New'} {activeType} {activeType !== 'currency' ? 'Account' : ''}</DialogTitle>
            <DialogDescription>
              Fill in the details for the {activeType}{activeType !== 'currency' ? ' account' : ''}.
            </DialogDescription>
            <form onSubmit={handleSubmit} className="grid gap-4 py-4">
              {renderFormFields()}
              <div className="grid grid-cols-2 gap-4">
                <Button type="button" variant="outline" onClick={() => setModalOpen(false)} disabled={loading}>Cancel</Button>
                <Button type="submit" disabled={loading}>{loading ? 'Saving...' : 'Save'}</Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
        <CurrencyModal
            open={showCurrencyModal}
            onOpenChange={setShowCurrencyModal}
            onCurrencyAdded={() => {
                // Re-fetch currencies for dropdown after adding a new one
                setCurrencyDropdownLoading(true);
                axios.get<Currency[]>('/api/currencies')
                    .then(res => {
                        setCurrenciesDropdown(res.data);
                        // Also update the main currency list if viewing currencies
                        if (activeType === 'currency') {
                            setCurrenciesData(res.data);
                        }
                    })
                    .catch(() => toast.error('Failed to refresh currencies'))
                    .finally(() => setCurrencyDropdownLoading(false));
            }}
        />
      </div>
    </AppLayout>
  );
};

export default Page;
