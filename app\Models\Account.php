<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Account extends Model
{
    use HasFactory;

    protected $fillable = [
        'account_type_id', 'name', 'contact', 'email', 'description', 'user_id',
        'ac_holder', 'ac_number', 'address', 'salary', 'joining_date',
        'currency_id', 'formula', 'code', 'cnic', 'father_name'
    ];

    public function accountType()
    {
        return $this->belongsTo(AccountType::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function currency()
    {
        return $this->belongsTo(Currency::class);
    }
}
