import React, { useState, useEffect, useMemo } from 'react';
import { Inertia } from '@inertiajs/inertia';
import { Head, Link } from '@inertiajs/react';
import axios from 'axios';
import { format } from 'date-fns';
import {
    ColumnDef,
    flexRender,
    getCoreRowModel,
    getPaginationRowModel,
    useReactTable,
    SortingState,
    getSortedRowModel,
} from '@tanstack/react-table';

// Shadcn/UI Components
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ComboBox } from '@/components/ui/combobox';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

// Icons
import { CalendarIcon, FilterIcon, PrinterIcon, PencilIcon, Loader2, ArrowUpDown } from 'lucide-react';

import AppLayout from '@/layouts/app-layout';
import { BreadcrumbItem } from '@/types';
import { toast } from 'sonner';

// Helper for formatting numbers (consistent with Purchase.tsx)
const formatNumber = (value: number | string | null | undefined, options?: Intl.NumberFormatOptions) => {
    if (value === null || value === undefined) return '';
    const num = typeof value === 'string' ? parseFloat(value.replace(/,/g, '')) : value;
    if (isNaN(num)) return '';
    return num.toLocaleString(undefined, options);
};

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Purchase List', href: 'purchase-list' } // Assuming this will be the route
];

interface Account {
    id: number;
    name: string;
    currency_code?: string;
}

interface Purchase {
    id: number;
    date: string;
    ref_no: string | null;
    order_id: string; // P No
    customer_account_name: string; // From A/c
    currency_account_name: string; // To A/c
    currency_code: string;
    currency_amount: number;
    rate: number;
    pkr_amount: number;
    // Add other relevant fields from your purchase model
}

interface Filters {
    date_from: string;
    date_to: string;
    customer_account_id: string;
    currency_account_id: string;
    page: number; // For pagination
}

interface PaginatedPurchasesResponse {
    data: Purchase[];
    links: {
        first: string | null;
        last: string | null;
        prev: string | null;
        next: string | null;
    };
    current_page: number;
    from: number | null;
    last_page: number;
    path: string;
    per_page: number;
    to: number | null;
    total: number;
}

export default function PurchaseList() {
    const [purchases, setPurchases] = useState<Purchase[]>([]);
    const [accounts, setAccounts] = useState<Account[]>([]); // For Customer Accounts
    const [currencyAccounts, setCurrencyAccounts] = useState<Account[]>([]);
    const [filters, setFilters] = useState<Filters>({
        date_from: '',
        date_to: '',
        customer_account_id: '',
        currency_account_id: '',
        page: 1,
    });
    const [loading, setLoading] = useState(false);
    const [datePickerFromOpen, setDatePickerFromOpen] = useState(false);
    const [datePickerToOpen, setDatePickerToOpen] = useState(false);
    const [pagination, setPagination] = useState({
        pageIndex: 0, // TanStack Table uses 0-based indexing for pages
        pageSize: 15, // Default page size
    });
    const [pageCount, setPageCount] = useState(0);
    const [sorting, setSorting] = useState<SortingState>([]);

    // Fetch initial data (accounts and possibly initial purchases)
    useEffect(() => {
        // Fetch Customer Accounts
        axios.get('/api/accounts', { params: { exclude_account_type_ids: '4,5,7' } })
            .then(res => setAccounts(res.data))
            .catch(err => {
                console.error("Failed to fetch customer accounts", err);
                toast.error("Failed to load customer accounts.");
            });

        // Fetch Currency Accounts
        axios.get('/api/accounts', { params: { account_type_id: 5 } }) // ID for Currency Account Type
            .then(res => setCurrencyAccounts(res.data))
            .catch(err => {
                console.error("Failed to fetch currency accounts", err);
                toast.error("Failed to load currency accounts.");
            });
        
        // Initial fetch of purchases
        fetchPurchases();
    }, []);

    const fetchPurchases = (applyNewFilters = false) => {
        setLoading(true);
        const currentFilters = {
            ...filters,
            page: applyNewFilters ? 1 : pagination.pageIndex + 1, // API uses 1-based page
            per_page: pagination.pageSize,
            sort_by: sorting.length > 0 ? sorting[0].id : undefined,
            sort_direction: sorting.length > 0 ? (sorting[0].desc ? 'desc' : 'asc') : undefined,
        };
        
        // Remove empty filter values
        const activeFilters = Object.fromEntries(
            Object.entries(currentFilters).filter(([, value]) => value !== '' && value !== undefined)
        );

        axios.get<PaginatedPurchasesResponse>('/api/purchase-entries', { params: activeFilters })
            .then(res => {
                console.log("API Response data:", res.data);
                setPurchases(res.data.data);
                setPageCount(res.data.last_page);
                if (applyNewFilters) {
                     setPagination(prev => ({ ...prev, pageIndex: res.data.current_page -1 }));
                }
            })
            .catch(err => {
                console.error("Failed to fetch purchases", err);
                toast.error("Failed to load purchase data. Check API response and filters.");
            })
            .finally(() => setLoading(false));
    };

    const handleFilterInputChange = (field: keyof Omit<Filters, 'page'>, value: string) => {
        setFilters(prev => ({ ...prev, [field]: value, page: 1 })); // Reset to page 1 on filter change
    };
    
    const handleDateChange = (field: 'date_from' | 'date_to', selectedDate: Date | undefined) => {
        if (selectedDate) {
            handleFilterInputChange(field, format(selectedDate, 'yyyy-MM-dd'));
        } else {
            handleFilterInputChange(field, '');
        }
        if (field === 'date_from') setDatePickerFromOpen(false);
        if (field === 'date_to') setDatePickerToOpen(false);
    };

    const applyFilters = () => {
        // pageIndex is 0-based, API is 1-based
        setPagination(prev => ({ ...prev, pageIndex: 0})); // Reset to first page
        fetchPurchases(true); // Pass true to indicate new filters are applied
    };

    const handlePrint = () => {
        const printFilters: Omit<Filters, 'page'> & { page?: number } = { ...filters };
        delete printFilters.page; // Don't need page for print view usually
        const queryParams = new URLSearchParams(printFilters as any).toString();
        window.open(`/print/purchase-list?${queryParams}`, '_blank');
        toast.info("Generating print view...");
    };

    const columns = useMemo<ColumnDef<Purchase>[]>(() => [
        {
            accessorKey: 'date',
            header: ({ column }) => (
                <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
                    Date <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
            ),
            cell: ({ row }) => format(new Date(row.original.date + 'T00:00:00'), "dd-MMM-yy"),
        },
        {
            accessorKey: 'ref_no',
            header: 'Ref #',
            cell: ({ row }) => row.original.ref_no || '-',
        },
        {
            accessorKey: 'order_id',
            header: ({ column }) => (
                <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
                    P No <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
            ),
        },
        {
            accessorKey: 'customer_account_name',
            header: 'From Customer',
        },
        {
            accessorKey: 'currency_account_name',
            header: 'To Currency',
        },
        {
            accessorKey: 'currency_amount',
            header: ({ column }) => (
                <div className="text-right">
                    <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
                        Currency Amt <ArrowUpDown className="ml-2 h-4 w-4" />
                    </Button>
                </div>
            ),
            cell: ({ row }) => (
                <div className="text-right">
                    {formatNumber(row.original.currency_amount, { minimumFractionDigits: 0, maximumFractionDigits: 0 })} {row.original.currency_code}
                </div>
            ),
        },
        {
            accessorKey: 'rate',
             header: ({ column }) => (
                <div className="text-right">
                    <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
                        Rate <ArrowUpDown className="ml-2 h-4 w-4" />
                    </Button>
                </div>
            ),
            cell: ({ row }) => (
                <div className="text-right">
                    {formatNumber(row.original.rate, { minimumFractionDigits: 2, maximumFractionDigits: 4 })}
                </div>
            ),
        },
        {
            accessorKey: 'pkr_amount',
             header: ({ column }) => (
                <div className="text-right">
                    <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
                        PKR Amt <ArrowUpDown className="ml-2 h-4 w-4" />
                    </Button>
                </div>
            ),
            cell: ({ row }) => (
                <div className="text-right">
                    {formatNumber(row.original.pkr_amount, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                </div>
            ),
        },
        {
            id: 'actions',
            header: 'Action',
            cell: ({ row }) => (
                <Link href={`/purchase-entry/edit/${row.original.id}`}> 
                    <Button variant="ghost" size="icon" title="Edit Purchase">
                        <PencilIcon className="h-4 w-4" />
                    </Button>
                </Link>
            ),
        },
    ], []);

    const table = useReactTable({
        data: purchases,
        columns,
        getCoreRowModel: getCoreRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        manualPagination: true, // Server-side pagination
        pageCount,
        onPaginationChange: setPagination,
        state: {
            pagination,
            sorting,
        },
        onSortingChange: setSorting,
        getSortedRowModel: getSortedRowModel(),
        manualSorting: true, // Server-side sorting
    });

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Purchase List" />
            <div className="container mx-auto p-4">
                <Card>
                    <CardHeader>
                        <CardTitle>Purchase List & Filters</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-6">
                        {/* Filter Section */}
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4 items-end">
                            <div>
                                <Label htmlFor="date_from">Date From</Label>
                                <Popover open={datePickerFromOpen} onOpenChange={setDatePickerFromOpen}>
                                    <PopoverTrigger asChild>
                                        <Button
                                            variant={"outline"}
                                            className={`w-full justify-start text-left font-normal ${
                                                !filters.date_from && "text-muted-foreground"
                                            }`}
                                        >
                                            <CalendarIcon className="mr-2 h-4 w-4" />
                                            {filters.date_from ? format(new Date(filters.date_from + 'T00:00:00'), "PPP") : <span>Pick a date</span>}
                                        </Button>
                                    </PopoverTrigger>
                                    <PopoverContent className="w-auto p-0">
                                        <Calendar
                                            mode="single"
                                            selected={filters.date_from ? new Date(filters.date_from + 'T00:00:00') : undefined}
                                            onSelect={(date) => handleDateChange('date_from', date)}
                                            initialFocus
                                        />
                                    </PopoverContent>
                                </Popover>
                            </div>
                            <div>
                                <Label htmlFor="date_to">Date To</Label>
                                <Popover open={datePickerToOpen} onOpenChange={setDatePickerToOpen}>
                                    <PopoverTrigger asChild>
                                        <Button
                                            variant={"outline"}
                                            className={`w-full justify-start text-left font-normal ${
                                                !filters.date_to && "text-muted-foreground"
                                            }`}
                                        >
                                            <CalendarIcon className="mr-2 h-4 w-4" />
                                            {filters.date_to ? format(new Date(filters.date_to + 'T00:00:00'), "PPP") : <span>Pick a date</span>}
                                        </Button>
                                    </PopoverTrigger>
                                    <PopoverContent className="w-auto p-0">
                                        <Calendar
                                            mode="single"
                                            selected={filters.date_to ? new Date(filters.date_to + 'T00:00:00') : undefined}
                                            onSelect={(date) => handleDateChange('date_to', date)}
                                            initialFocus
                                        />
                                    </PopoverContent>
                                </Popover>
                            </div>
                             <div>
                                <Label htmlFor="customerAccount">Customer Account</Label>
                                <ComboBox
                                    value={filters.customer_account_id}
                                    onChange={(value) => handleFilterInputChange('customer_account_id', value)}
                                    options={[{ label: "All Customers", value: "" }, ...accounts.map(acc => ({ label: acc.name, value: String(acc.id) }))]}
                                    placeholder="Select Customer..."
                                />
                            </div>
                            <div>
                                <Label htmlFor="currencyAccount">Currency Account</Label>
                                <ComboBox
                                    value={filters.currency_account_id}
                                    onChange={(value) => handleFilterInputChange('currency_account_id', value)}
                                    options={[{ label: "All Currencies", value: "" }, ...currencyAccounts.map(acc => ({
                                        label: `${acc.name} ${acc.currency_code ? `(${acc.currency_code})` : ''}`,
                                        value: String(acc.id)
                                    }))]}
                                    placeholder="Select Currency..."
                                />
                            </div>
                            <div className="flex space-x-2 pt-6"> {/* Adjusted padding for alignment */}
                                <Button onClick={applyFilters} className="w-full" disabled={loading}>
                                    {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                                    <FilterIcon className="mr-2 h-4 w-4" /> Filter
                                </Button>
                                <Button onClick={handlePrint} variant="outline" className="w-full" disabled={loading}>
                                    <PrinterIcon className="mr-2 h-4 w-4" /> Print
                                </Button>
                            </div>
                        </div>

                        {/* Table Section */}
                        <div className="rounded-md border">
                            <Table>
                                <TableHeader>
                                    {table.getHeaderGroups().map(headerGroup => (
                                        <TableRow key={headerGroup.id}>
                                            {headerGroup.headers.map(header => (
                                                <TableHead key={header.id} style={{ width: header.getSize() !== 150 ? header.getSize() : undefined }}>
                                                    {header.isPlaceholder
                                                        ? null
                                                        : flexRender(
                                                            header.column.columnDef.header,
                                                            header.getContext()
                                                        )}
                                                </TableHead>
                                            ))}
                                        </TableRow>
                                    ))}
                                </TableHeader>
                                <TableBody>
                                    {table.getRowModel().rows?.length ? (
                                        table.getRowModel().rows.map(row => (
                                            <TableRow
                                                key={row.id}
                                                data-state={row.getIsSelected() && "selected"}
                                            >
                                                {row.getVisibleCells().map(cell => (
                                                    <TableCell key={cell.id}>
                                                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                                                    </TableCell>
                                                ))}
                                            </TableRow>
                                        ))
                                    ) : (
                                        <TableRow>
                                            <TableCell colSpan={columns.length} className="h-24 text-center">
                                                {loading ? (
                                                    <div className="flex flex-col items-center justify-center">
                                                         <Loader2 className="mx-auto h-8 w-8 animate-spin text-primary" />
                                                         <span>Loading purchases...</span>
                                                    </div>
                                                ): "No results found."}
                                            </TableCell>
                                        </TableRow>
                                    )}
                                </TableBody>
                            </Table>
                        </div>
                        {/* Pagination Controls */}
                        <div className="flex items-center justify-between space-x-2 py-4">
                            <div className="flex-1 text-sm text-muted-foreground">
                                Page {table.getState().pagination.pageIndex + 1} of {table.getPageCount()}
                            </div>
                            <div className="flex items-center space-x-2">
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => table.setPageIndex(0)}
                                    disabled={!table.getCanPreviousPage()}
                                >
                                    First
                                </Button>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => table.previousPage()}
                                    disabled={!table.getCanPreviousPage()}
                                >
                                    Previous
                                </Button>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => table.nextPage()}
                                    disabled={!table.getCanNextPage()}
                                >
                                    Next
                                </Button>
                                 <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => table.setPageIndex(table.getPageCount() - 1)}
                                    disabled={!table.getCanNextPage()}
                                >
                                    Last
                                </Button>
                            </div>
                            <div className="flex items-center space-x-2">
                                <span className="text-sm text-muted-foreground">Rows per page:</span>
                                <Select
                                    value={`${table.getState().pagination.pageSize}`}
                                    onValueChange={(value) => {
                                        table.setPageSize(Number(value));
                                    }}
                                >
                                    <SelectTrigger className="h-8 w-[70px]">
                                        <SelectValue placeholder={table.getState().pagination.pageSize} />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {[10, 15, 20, 30, 40, 50].map((pageSize) => (
                                            <SelectItem key={pageSize} value={`${pageSize}`}>
                                                {pageSize}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}