# Laravel Development Rules

## General Code Instructions

- **Code Comments:** Avoid adding comments above methods or code blocks unless they are non-obvious.Reserve comments for explaining the rationale behind complex code logic.

- **Code Changes:** Avoid commenting out code unless explicitly instructed.Assume the old code will remain in Git history.

- **Laravel 12 Features:** Familiarize yourself with Laravel 12's[official documentation](use Context7 MCP) to understand its new features and enhancements.

## General Laravel Instructions

- **File Generation:** Use`php artisan make`commands to generate files whenever possible.Do not manually create folders with`mkdir`unless necessary.

- **Pivot Table Migrations:** Ensure migrations for pivot tables follow alphabetical order(e.g.,`create_project_role_table`instead of`create_role_project_table`).

- **Laravel 12 Updates:** Utilize Laravel 12's new features like async caching,dynamic job queue prioritization,and native GraphQL support to improve application performance and scalability.

## Laravel 11+ Skeleton Structure

- **Service Providers:** Avoid creating new service providers unless absolutely necessary.If required,register them in`bootstrap/providers.php`instead of the deprecated`config/app.php`.

- **Event Listeners:** In Laravel 11+,listeners automatically listen to events if type-hinted correctly.No additional configuration is needed.

- **Console Scheduler:** Place scheduled commands in`routes/console.php`.The deprecated`app/Console/Kernel.php`file has been removed since Laravel 11.

- **Middleware:** Use middleware by class name in routes whenever possible.If middleware aliases are needed,register them in`bootstrap/app.php`instead of the deprecated`app/Http/Kernel.php`.

## Laravel-Specific Rules

- **Tailwind:** For new Blade templates,prefer Tailwind over Bootstrap unless explicitly instructed otherwise.Tailwind is pre-configured in Laravel 11+with Vite.

- **Faker:** In factories,use the`fake()helper instead of$this->faker`.

- **Views:** Generate new Blade files using`php artisan make:view`instead of manually creating folders or files with`mkdir`or`touch`.

- **Policies:** Policies are auto-discovered in Laravel,so no need to register them in service providers.

- **Laravel 12 Authentication:** Leverage the`secureValidate()`method to enforce stronger password.rules Utilize the new authentication starter kits (React,Vue,Livewire) for streamlined authentication development.

## Laravel 12-Specific Rules

- **Async Caching:** Implement asynchronous caching to reduce response times.

- **Custom GraphQL Schemas:** Define GraphQL schemas and resolvers directly within Laravel to improve API development efficiency.

- **Dynamic Job Queue Prioritization:** Use`dispatch(new ProcessOrder($order))->prioritize('high');`to dynamically prioritize jobs.

- **Enum Support:** Use the`enum`type in Eloquent models to store enumerated values and enhance data integrity.

## Filament Instructions

- If the project uses Filament,adhere to the structure and command flags of Filament 3.
