import React, { useState } from 'react'; // Added useState
import { Head } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { cn } from '@/lib/utils'; // Added cn

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'API Documentation',
        href: '/api-documentation',
    },
];

interface ApiSection {
    id: string;
    title: string;
    content: React.ReactNode;
}

// Placeholder sections - replace with actual API documentation content
const apiSections: ApiSection[] = [
    {
        id: 'introduction',
        title: 'Introduction',
        content: (
            <div>
                <p>Welcome to the API documentation. Here you will find information about available endpoints.</p>
                
                <h3 className="text-lg font-semibold mt-6 mb-2">Standard Error Responses</h3>
                <p className="mb-2">All error responses follow this format:</p>
                <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm mb-2">
                    <code>{`{
    "message": "Error description",
    "errors": {
        "field_name": ["Error message"]
    }
}`}
                    </code>
                </pre>
                <p className="mb-2">Common HTTP status codes:</p>
                <ul className="list-disc list-inside mb-2">
                    <li><code>400 Bad Request</code>: Invalid request format or parameters</li>
                    <li><code>401 Unauthorized</code>: Authentication required</li>
                    <li><code>403 Forbidden</code>: Insufficient permissions</li>
                    <li><code>404 Not Found</code>: Resource not found</li>
                    <li><code>422 Unprocessable Entity</code>: Validation errors</li>
                    <li><code>500 Internal Server Error</code>: Server error</li>
                </ul>

                <h3 className="text-lg font-semibold mt-6 mb-2">TypeScript Interfaces</h3>
                <p className="mb-2">Common interfaces used in API responses:</p>
                <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm mb-2">
                    <code>{`// Base response interface
export interface ApiResponse<T = any> {
    data: T;
    message?: string;
    meta?: {
        current_page?: number;
        from?: number;
        last_page?: number;
        per_page?: number;
        to?: number;
        total?: number;
    };
}

// Error response
export interface ApiError {
    message: string;
    errors?: Record<string, string[]>;
}

// Paginated response
export interface PaginatedResponse<T> {
    data: T[];
    links: {
        first: string | null;
        last: string | null;
        prev: string | null;
        next: string | null;
    };
    meta: {
        current_page: number;
        from: number;
        last_page: number;
        path: string;
        per_page: number;
        to: number;
        total: number;
    };
}

// Common entity with timestamps
export interface Timestamped {
    created_at: string;
    updated_at: string;
    deleted_at?: string | null;
}`}
                    </code>
                </pre>

                <h4 className="text-md font-semibold mt-4 mb-2">Currency Types</h4>
                <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm mb-2">
                    <code>{`export interface Currency extends Timestamped {
    id: number;
    name: string;
    code: string;
    symbol?: string;
    decimal_places?: number;
    is_active?: boolean;
}

export interface CreateCurrencyDto {
    name: string;
    code: string;
    symbol?: string;
    decimal_places?: number;
    is_active?: boolean;
}

export interface UpdateCurrencyDto extends Partial<CreateCurrencyDto> {}`}
                    </code>
                </pre>
            </div>
        ),
    },
    
    {
        id: 'authentication',
        title: 'Authentication',
        content: (
            <div>
                <h3 className="text-lg font-semibold mb-2">Overview</h3>
                <p className="mb-4">Authentication is handled using Laravel Sanctum for SPA authentication (cookie-based). Standard web routes are used for registration, login, and logout.</p>
                
                <div className="bg-yellow-50 dark:bg-yellow-900/20 border-l-4 border-yellow-400 p-4 mb-4">
                    <div className="flex">
                        <div className="flex-shrink-0">
                            <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                            </svg>
                        </div>
                        <div className="ml-3">
                            <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">Security Notice</h3>
                            <div className="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                                <p>Always use HTTPS for all API requests. Never expose your API tokens in client-side code or public repositories.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <h4 className="text-md font-semibold mt-4 mb-2">POST /register</h4>
                <p className="mb-2">Registers a new user. By default, users are assigned the 'Viewer' role. Admins can optionally assign 'Admin' or 'Editor' roles during creation.</p>
                <strong className="mb-1 block">Request Body:</strong>
                <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm mb-2">
                    <code>
{`{
    "name": "John Doe",
    "username": "johndoe",
    "email": "<EMAIL>",
    "password": "password",
    "password_confirmation": "password",
    "role": "Viewer"
}`}
                </code>
                </pre>
                <strong className="mb-1 block">Responses:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>302 Found</code>: Successful registration, redirects to dashboard.</li>
                    <li><code>422 Unprocessable Entity</code>: Validation errors (e.g., email taken, password mismatch).</li>
                    <li><code>403 Forbidden</code>: If public registration is disabled and user is not an Admin.</li>
                </ul>

                <h4 className="text-md font-semibold mt-4 mb-2">POST /login</h4>
                <p className="mb-2">Authenticates a user and creates a session.</p>
                <strong className="mb-1 block">Request Body:</strong>
                <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm mb-2">
                    <code>{`{
    "email": "<EMAIL>",
    "password": "password",
    "remember": false
}`}
                    </code>
                </pre>
                <strong className="mb-1 block">Responses:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>302 Found</code>: Successful login, redirects to intended page or dashboard.</li>
                    <li><code>422 Unprocessable Entity</code>: Invalid credentials or validation errors.</li>
                </ul>

                <h4 className="text-md font-semibold mt-4 mb-2">POST /logout</h4>
                <p className="mb-2">Logs out the currently authenticated user and destroys the session.</p>
                <strong className="mb-1 block">Responses:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>302 Found</code>: Successful logout, redirects to the home page ('/').</li>
                </ul>

                 <h4 className="text-md font-semibold mt-4 mb-2">GET /api/user</h4>
                <p className="mb-2">Retrieves the details of the currently authenticated user. Requires the user to be authenticated via Sanctum.</p>
                <strong className="mb-1 block">Authentication:</strong>
                <p className="mb-2">Requires a valid Sanctum session cookie.</p>
                <strong className="mb-1 block">Responses:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>200 OK</code>: Returns the authenticated user object.</li>
                    <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm mt-1 mb-2">
                        <code>{`{
    "id": 1,
    "name": "John Doe",
    "username": "johndoe",
    "email": "<EMAIL>",
    "email_verified_at": null,
    "role": "Viewer",
    "created_at": "2023-10-27T10:00:00.000000Z",
    "updated_at": "2023-10-27T10:00:00.000000Z"
}`}
                        </code>
                    </pre>
                    <li><code>401 Unauthorized</code>: If the user is not authenticated.</li>
                </ul>
            </div>
        ),
    },
    {
        id: 'endpoints',
        title: 'Endpoints',
        content: (
            <div>
                <h3 className="text-lg font-semibold mb-2">GET /api/users</h3>
                <p>Retrieve a list of users.</p>
                {/* Add more endpoint details */}
            </div>
        ),
    },
    {
        id: 'todos',
        title: 'Todos',
        content: (
            <div>
                <h3 className="text-lg font-semibold mb-2">Overview</h3>
                <p className="mb-4">Endpoints for managing user-specific todo items. All endpoints require authentication via Sanctum.</p>

                <h4 className="text-md font-semibold mt-4 mb-2">GET /api/todos</h4>
                <p className="mb-2">Retrieves the authenticated user's 15 most recent todo items, ordered by creation date (descending).</p>
                <strong className="mb-1 block">Authentication:</strong>
                <p className="mb-2">Requires a valid Sanctum session cookie.</p>
                <strong className="mb-1 block">Responses:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>200 OK</code>: Returns an array of todo objects.</li>
                    <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm mt-1 mb-2">
                        <code>{`[
    {
        "id": 1,
        "user_id": 1,
        "title": "Finish API documentation",
        "completed": false,
        "created_at": "2023-10-28T12:00:00.000000Z",
        "updated_at": "2023-10-28T12:00:00.000000Z"
    },
    // ... more todos
]`}
                        </code>
                    </pre>
                    <li><code>401 Unauthorized</code>: If the user is not authenticated.</li>
                </ul>

                <h4 className="text-md font-semibold mt-4 mb-2">POST /api/todos</h4>
                <p className="mb-2">Creates a new todo item for the authenticated user.</p>
                <strong className="mb-1 block">Authentication:</strong>
                <p className="mb-2">Requires a valid Sanctum session cookie.</p>
                <strong className="mb-1 block">Request Body:</strong>
                <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm mb-2">
                    <code>{`{
    "title": "Buy groceries"
}`}
                    </code>
                </pre>
                <strong className="mb-1 block">Responses:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>201 Created</code>: Returns the newly created todo object.</li>
                     <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm mt-1 mb-2">
                        <code>{`{
    "id": 2,
    "user_id": 1,
    "title": "Buy groceries",
    "completed": false,
    "created_at": "2023-10-28T13:00:00.000000Z",
    "updated_at": "2023-10-28T13:00:00.000000Z"
}`}
                        </code>
                    </pre>
                    <li><code>422 Unprocessable Entity</code>: Validation errors (e.g., title is missing or not a string).</li>
                    <li><code>401 Unauthorized</code>: If the user is not authenticated.</li>
                </ul>

                <h4 className="text-md font-semibold mt-4 mb-2">PUT /api/todos/{'{todo}'}</h4>
                <p className="mb-2">Updates an existing todo item. The user must own the todo item.</p>
                <strong className="mb-1 block">Authentication:</strong>
                <p className="mb-2">Requires a valid Sanctum session cookie.</p>
                <strong className="mb-1 block">URL Parameters:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>todo</code> (integer, required): The ID of the todo item to update.</li>
                </ul>
                <strong className="mb-1 block">Request Body:</strong>
                <p className="mb-1 text-sm">Can include either 'title' or 'completed' or both.</p>
                <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm mb-2">
                    <code>{`{
    "title": "Buy organic groceries", // Optional
    "completed": true // Optional
}`}
                    </code>
                </pre>
                <strong className="mb-1 block">Responses:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>200 OK</code>: Returns the updated todo object.</li>
                    <li><code>422 Unprocessable Entity</code>: Validation errors.</li>
                    <li><code>403 Forbidden</code>: If the user does not own the todo item.</li>
                    <li><code>404 Not Found</code>: If the todo item does not exist.</li>
                    <li><code>401 Unauthorized</code>: If the user is not authenticated.</li>
                </ul>

                <h4 className="text-md font-semibold mt-4 mb-2">DELETE /api/todos/{'{todo}'}</h4>
                <p className="mb-2">Deletes an existing todo item. The user must own the todo item.</p>
                <strong className="mb-1 block">Authentication:</strong>
                <p className="mb-2">Requires a valid Sanctum session cookie.</p>
                <strong className="mb-1 block">URL Parameters:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>todo</code> (integer, required): The ID of the todo item to delete.</li>
                </ul>
                <strong className="mb-1 block">Responses:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>204 No Content</code>: Successful deletion.</li>
                    <li><code>403 Forbidden</code>: If the user does not own the todo item.</li>
                    <li><code>404 Not Found</code>: If the todo item does not exist.</li>
                    <li><code>401 Unauthorized</code>: If the user is not authenticated.</li>
                </ul>
            </div>
        ),
    },
    {
        id: 'ledgers-pkr-data',
        title: 'Ledgers PKR Data',
        content: (
            <div>
                <h3 className="text-lg font-semibold mb-2">GET /api/ledgers-pkr/data</h3>
                <p className="mb-4">Retrieves ledger data for a specific account within a date range, including balance and absolute balance.</p>

                <strong className="mb-1 block">Authentication:</strong>
                <p className="mb-2">Requires a valid Sanctum session cookie (user must be logged in).</p>

                <strong className="mb-1 block">Query Parameters:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>account_id</code> (integer, required): The ID of the account for which to retrieve ledger data.</li>
                    <li><code>from_date</code> (string, required): The start date of the range in YYYY-MM-DD format.</li>
                    <li><code>to_date</code> (string, required): The end date of the range in YYYY-MM-DD format.</li>
                </ul>

                <h4 className="text-md font-semibold mt-4 mb-2">How to Send Request</h4>
                <p className="mb-2">Send a <code>GET</code> request to the endpoint, including the required parameters as URL query parameters.</p>
                <strong className="mb-1 block">Example Request URL:</strong>
                <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm mb-2">
                    <code>{`/api/ledgers-pkr/data?account_id=1&from_date=2023-01-01&to_date=2023-12-31`}</code>
                </pre>

                <h4 className="text-md font-semibold mt-4 mb-2">Responses</h4>
                <strong className="mb-1 block"><code>200 OK</code>: Successful response.</strong>
                <p className="mb-2">Returns an object containing an array of ledger entries for the specified account within the date range, along with the calculated total balance and absolute balance for that period.</p>
                <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm mt-1 mb-2">
                    <code>{`{
    "data": [
        {
            "id": 1,
            "transaction_date": "2023-10-28",
            "description": "Initial deposit",
            "debit": "1000.00",
            "credit": "0.00",
            "balance": "1000.00",
            "absolute_balance": "1000.00"
        },
        // ... more ledger entries
    ],
    "balance": "5000.00",
    "absolute_balance": "5000.00"
}`}    </code>
                </pre>

                <strong className="mb-1 block mt-4">Error Responses:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>400 Bad Request</code>: Invalid request format or parameters (e.g., missing required query parameters, incorrect date format).</li>
                    <li><code>401 Unauthorized</code>: If the user is not authenticated (missing or invalid Sanctum session cookie).</li>
                    <li><code>404 Not Found</code>: If the specified <code>account_id</code> does not exist.</li>
                    <li><code>500 Internal Server Error</code>: Server error occurred while processing the request.</li>
                </ul>
            </div>
        ),
    },
    {
        id: 'ledger-pdf-generation',
        title: 'Generate Ledger PDF',
        content: (
            <div>
                <h3 className="text-lg font-semibold mb-2">GET /reports/ledger-pkr/pdf</h3>
                <p className="mb-4">Generates a PDF report for a specific ledger account within a given date range. The PDF is streamed as the response.</p>

                <strong className="mb-1 block">Authentication:</strong>
                <p className="mb-2">Required (Sanctum). Ensures the user is logged in.</p>

                <strong className="mb-1 block">Authorization:</strong>
                <p className="mb-2">Accessible by users with 'Admin' or 'Editor' roles.</p>

                <strong className="mb-1 block">Query Parameters:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li>
                        <code>fromDate</code> (date string, format: YYYY-MM-DD, required): The start date for the ledger report.
                    </li>
                    <li>
                        <code>tillDate</code> (date string, format: YYYY-MM-DD, required): The end date for the ledger report. Must be after or equal to <code>fromDate</code>.
                    </li>
                    <li>
                        <code>accountId</code> (integer, required): The ID of the account for which the ledger is to be generated. Must be an existing account ID.
                    </li>
                    <li>
                        <code>withPartyName</code> (string: "true" or "false", required): Specifies whether to include party names in the transaction narrations within the PDF.
                    </li>
                </ul>

                <h4 className="text-md font-semibold mt-4 mb-2">How to Send Request</h4>
                <p className="mb-2">Send a <code>GET</code> request to the endpoint, including the required parameters as URL query parameters.</p>
                <strong className="mb-1 block">Example Request URL:</strong>
                <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm mb-2">
                    <code>{`/reports/ledger-pkr/pdf?fromDate=2023-01-01&tillDate=2023-12-31&accountId=1&withPartyName=true`}</code>
                </pre>

                <h4 className="text-md font-semibold mt-4 mb-2">Responses</h4>
                <strong className="mb-1 block"><code>200 OK</code>: Successful response.</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><strong>Content-Type:</strong> <code>application/pdf</code></li>
                    <li><strong>Body:</strong> The generated PDF file stream.</li>
                </ul>
                
                <strong className="mb-1 block mt-4">Error Responses:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li>
                        <code>403 Forbidden</code>: If the authenticated user does not have the 'Admin' or 'Editor' role.
                        <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm mt-1 mb-1">
                            <code>{`{ "message": "Unauthorized" }`}</code>
                        </pre>
                    </li>
                    <li>
                        <code>422 Unprocessable Entity</code>: If request validation fails.
                        <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm mt-1 mb-1">
                            <code>{`{ 
    "error": "Validation failed.", 
    "details": { "fromDate": ["The from date field is required."] } 
}`}</code>
                        </pre>
                    </li>
                    <li>
                        <code>500 Internal Server Error</code>: If an unexpected error occurs during PDF generation.
                        <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm mt-1 mb-1">
                            <code>{`{ 
    "error": "An error occurred while generating the PDF.", 
    "details": "Error message" 
}`}</code>
                        </pre>
                    </li>
                </ul>
            </div>
        ),
    },
    {
        id: 'balance-sheet-data',
        title: 'Balance Sheet Data',
        content: (
            <div>
                <h3 className="text-lg font-semibold mb-2">GET /api/reports/balance-sheet</h3>
                <p className="mb-4">Retrieves balance sheet data with filtering options.</p>

                <strong className="mb-1 block">Authentication:</strong>
                <p className="mb-2">Requires a valid Sanctum session cookie (user must be logged in).</p>

                <strong className="mb-1 block">Query Parameters:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>fromDate</code> (string, required): The start date of the range in YYYY-MM-DD format.</li>
                    <li><code>tillDate</code> (string, required): The end date of the range in YYYY-MM-DD format.</li>
                    <li><code>accountTypeIds</code> (array of integers, optional): An array of account type IDs to filter by.</li>
                    <li><code>balanceType</code> (string, optional): Filter by balance type ('debit' or 'credit').</li>
                </ul>

                <h4 className="text-md font-semibold mt-4 mb-2">How to Send Request</h4>
                <p className="mb-2">Send a <code>GET</code> request to the endpoint, including the required parameters as URL query parameters.</p>
                <strong className="mb-1 block">Example Request URL:</strong>
                <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm mb-2">
                    <code>{`/api/reports/balance-sheet?fromDate=2023-01-01&tillDate=2023-12-31&accountTypeIds[]=1&accountTypeIds[]=2&balanceType=debit`}</code>
                </pre>

                <h4 className="text-md font-semibold mt-4 mb-2">Responses</h4>
                <strong className="mb-1 block"><code>200 OK</code>: Successful response.</strong>
                <p className="mb-2">Returns an object containing grouped balance sheet data by account type and an overall total balance.</p>
                <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm mt-1 mb-2">
                    <code>{`{
    "grouped_data": [
        {
            "account_type": "Customer",
            "accounts": [
                {
                    "account_id": 1,
                    "account_name": "Customer A",
                    "balance": 5000.00
                }
            ],
            "total_balance": 5000.00
        }
        // ... more account types
    ],
    "overall_total": 15000.00
}`}</code>
                </pre>

                <strong className="mb-1 block mt-4">Error Responses:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>400 Bad Request</code>: Invalid request format or parameters.</li>
                    <li><code>401 Unauthorized</code>: If the user is not authenticated.</li>
                    <li><code>422 Unprocessable Entity</code>: Validation errors.</li>
                    <li><code>500 Internal Server Error</code>: Server error occurred.</li>
                </ul>
            </div>
        ),
    },
    {
        id: 'account-types',
        title: 'Account Types',
        content: (
            <div>
                <h3 className="text-lg font-semibold mb-2">Overview</h3>
                <p className="mb-4">Endpoint for retrieving available account types used in the system.</p>

                <h4 className="text-md font-semibold mt-4 mb-2">GET /api/account-types</h4>
                <p className="mb-2">Retrieves a list of all available account types.</p>
                <strong className="mb-1 block">Authentication:</strong>
                <p className="mb-2">Requires a valid Sanctum session cookie (user must be logged in).</p>
                <strong className="mb-1 block">Responses:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>200 OK</code>: Returns an array of account type objects.</li>
                    <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm mt-1 mb-2">
                        <code>{`[
    {
        "id": 1,
        "name": "customer",
        "label": "Customer",
        "created_at": null,
        "updated_at": null
    },
    {
        "id": 2,
        "name": "bank",
        "label": "Bank"
        // ... other fields
    },
    // ... more account types
]`}

                        </code>
                    </pre>
                    <li><code>401 Unauthorized</code>: If the user is not authenticated.</li>
                </ul>
            </div>
        ),
    },
    {
        id: 'currencies',
        title: 'Currencies',
        content: (
            <div>
                <h3 className="text-lg font-semibold mb-2">Overview</h3>
                <p className="mb-4">Endpoints for managing currencies in the system. Currencies are used for financial transactions and account management.</p>
                
                <div className="bg-blue-50 dark:bg-blue-900/20 border-l-4 border-blue-500 p-4 mb-4">
                    <div className="flex">
                        <div className="flex-shrink-0">
                            <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h2a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                            </svg>
                        </div>
                        <div className="ml-3">
                            <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200">Note</h3>
                            <div className="mt-2 text-sm text-blue-700 dark:text-blue-300">
                                <p>All endpoints require authentication unless otherwise specified. Some operations may be restricted based on user roles.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <h4 className="text-md font-semibold mt-4 mb-2">GET /api/currencies</h4>
                <p className="mb-2">Retrieves a list of all currencies in the system.</p>
                <strong className="mb-1 block">Authentication:</strong>
                <p className="mb-2">No authentication required.</p>
                <strong className="mb-1 block">Responses:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>200 OK</code>: Returns an array of currency objects.</li>
                    <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm mt-1 mb-2">
                        <code>{`[
    {
        "id": 1,
        "name": "US Dollar",
        "code": "USD",
        "created_at": "2023-10-28T10:00:00.000000Z",
        "updated_at": "2023-10-28T10:00:00.000000Z"
    }
]`}
                        </code>
                    </pre>
                </ul>

                <h4 className="text-md font-semibold mt-4 mb-2">POST /api/currencies</h4>
                <p className="mb-2">Creates a new currency. Requires Admin or Editor role.</p>
                <strong className="mb-1 block">Authentication:</strong>
                <p className="mb-2">Requires a valid Sanctum session cookie with Admin or Editor role.</p>
                <strong className="mb-1 block">Request Body:</strong>
                <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm mb-2">
                    <code>{`{
    "name": "Euro",
    "code": "EUR"
}`}
                    </code>
                </pre>
                <strong className="mb-1 block">Responses:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>201 Created</code>: Returns the newly created currency object.</li>
                    <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm mt-1 mb-2">
                        <code>{`{
    "id": 2,
    "name": "Euro",
    "code": "EUR",
    "created_at": "2023-10-28T11:00:00.000000Z",
    "updated_at": "2023-10-28T11:00:00.000000Z"
}`}
                        </code>
                    </pre>
                    <li><code>401 Unauthorized</code>: If user is not authenticated.</li>
                    <li><code>403 Forbidden</code>: If user doesn't have required role (Admin/Editor).</li>
                    <li><code>422 Unprocessable Entity</code>: Validation errors (e.g., code already exists).</li>
                </ul>

                <h4 className="text-md font-semibold mt-4 mb-2">PUT /api/currencies/{'{currency}'}</h4>
                <p className="mb-2">Updates an existing currency. Requires Admin or Editor role.</p>
                <strong className="mb-1 block">Authentication:</strong>
                <p className="mb-2">Requires a valid Sanctum session cookie with Admin or Editor role.</p>
                <strong className="mb-1 block">URL Parameters:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>currency</code> (integer, required): The ID of the currency to update.</li>
                </ul>
                <strong className="mb-1 block">Request Body:</strong>
                <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm mb-2">
                    <code>{`{
    "name": "US Dollar (Updated)",
    "code": "USD"
}`}
                    </code>
                </pre>
                <strong className="mb-1 block">Responses:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>200 OK</code>: Returns the updated currency object.</li>
                    <li><code>401 Unauthorized</code>: If user is not authenticated.</li>
                    <li><code>403 Forbidden</code>: If user doesn't have required role (Admin/Editor).</li>
                    <li><code>404 Not Found</code>: If currency with given ID doesn't exist.</li>
                    <li><code>422 Unprocessable Entity</code>: Validation errors.</li>
                </ul>

                <h4 className="text-md font-semibold mt-4 mb-2">DELETE /api/currencies/{'{currency}'}</h4>
                <p className="mb-2">Deletes a currency. Requires Admin role. Cannot delete currencies that are in use by accounts.</p>
                <strong className="mb-1 block">Authentication:</strong>
                <p className="mb-2">Requires a valid Sanctum session cookie with Admin role.</p>
                <strong className="mb-1 block">URL Parameters:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>currency</code> (integer, required): The ID of the currency to delete.</li>
                </ul>
                <strong className="mb-1 block">Responses:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>200 OK</code>: Success message.</li>
                    <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm mt-1 mb-2">
                        <code>{`{
    "message": "Currency deleted successfully."
}`}
                        </code>
                    </pre>
                    <li><code>401 Unauthorized</code>: If user is not authenticated.</li>
                    <li><code>403 Forbidden</code>: If user doesn't have Admin role.</li>
                    <li><code>404 Not Found</code>: If currency with given ID doesn't exist.</li>
                    <li><code>422 Unprocessable Entity</code>: If currency is in use by accounts.</li>
                </ul>
            </div>
        ),
    },
    {
        id: 'chq-ref-banks',
        title: 'CHQ Ref Banks',
        content: (
            <div>
                <h3 className="text-lg font-semibold mt-6 mb-2">Cheque Reference Banks (chq_ref_banks)</h3>
                <p className="mb-2">Endpoints for managing cheque reference banks. All endpoints require authentication via Sanctum.</p>

                <h4 className="text-md font-semibold mt-4 mb-2">GET /api/chq-ref-banks</h4>
                <p className="mb-2">Retrieves a list of cheque reference banks. Optional query parameter <code>user_id</code> can be used to filter by user.</p>
                <strong className="mb-1 block">Authentication:</strong>
                <p className="mb-2">Requires a valid Sanctum session cookie.</p>
                <strong className="mb-1 block">Query Parameters:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>user_id</code> (integer, optional): Filter cheque reference banks by user ID.</li>
                </ul>
                <strong className="mb-1 block">Responses:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>200 OK</code>: Returns an array of cheque reference bank objects.</li>
                    <li><code>401 Unauthorized</code>: If the user is not authenticated.</li>
                </ul>
            </div>
        )
    },
    {
        id: 'accounts',
        title: 'Accounts',
        content: (
            <div>
                <h3 className="text-lg font-semibold mb-2">Overview</h3>
                <p className="mb-4">Endpoints for managing various types of accounts (customers, banks, employees, etc.). All endpoints require authentication via Sanctum.</p>

                <h4 className="text-md font-semibold mt-4 mb-2">GET /api/accounts</h4>
                <p className="mb-2">Retrieves a list of accounts. Can be filtered by `account_type_id`.</p>
                <strong className="mb-1 block">Authentication:</strong>
                <p className="mb-2">Requires a valid Sanctum session cookie.</p>
                <strong className="mb-1 block">Query Parameters:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>account_type_id</code> (integer, optional): Filter accounts by a specific type ID.</li>
                </ul>
                <strong className="mb-1 block">Responses:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>200 OK</code>: Returns an array of account objects (structure varies by type). Includes `accountType`, `user`, and `currency` relations where applicable.</li>
                    {/* Add example response structure if helpful, noting variability */}
                    <li><code>401 Unauthorized</code>: If the user is not authenticated.</li>
                </ul>

                <h4 className="text-md font-semibold mt-4 mb-2">POST /api/accounts</h4>
                <p className="mb-2">Creates a new account. Requires Admin/Editor role. The required fields depend on the `account_type_id` provided.</p>
                <strong className="mb-1 block">Authentication:</strong>
                <p className="mb-2">Requires a valid Sanctum session cookie and Admin/Editor role.</p>
                <strong className="mb-1 block">Common Request Body Fields:</strong>
                <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm mb-2">
                    <code>{`{
    "account_type_id": 1, // Required, determines other fields
    "name": "Account Name", // Required
    "description": "Optional description" // Optional
}`}
                    </code>
                </pre>
                <strong className="mb-1 block">Type-Specific Fields:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><strong>Customer (ID: 1):</strong> `contact` (string, nullable), `email` (email, nullable)</li>
                    <li><strong>Bank (ID: 2):</strong> `ac_holder` (string, nullable), `ac_number` (string, nullable), `contact` (string, nullable), `address` (string, nullable)</li>
                    <li><strong>Currency Account (ID: 4):</strong> `currency_id` (integer, required), `formula` (enum: 'm' or 'd', required), `code` (string, nullable)</li>
                    <li><strong>Employee (ID: 5):</strong> `father_name` (string, required), `cnic` (string, required), `contact` (string, required), `address` (string, required), `salary` (numeric, required), `joining_date` (date, required)</li>
                    <li><strong>Expense, Cash, Income, Other, etc.:</strong> Usually only require `name` and optional `description`.</li>
                </ul>
                <strong className="mb-1 block">Responses:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>201 Created</code>: Returns the newly created account object (with relations).</li>
                    <li><code>422 Unprocessable Entity</code>: Validation errors based on account type.</li>
                    <li><code>400 Bad Request</code>: Invalid `account_type_id`.</li>
                    <li><code>401/403 Unauthorized</code>: If the user is not authenticated or lacks permission.</li>
                </ul>

                <h4 className="text-md font-semibold mt-4 mb-2">PUT /api/accounts/{'{account}'}</h4>
                <p className="mb-2">Updates an existing account. Requires Admin/Editor role. Fields depend on the `account_type_id`.</p>
                <strong className="mb-1 block">Authentication:</strong>
                <p className="mb-2">Requires a valid Sanctum session cookie and Admin/Editor role.</p>
                <strong className="mb-1 block">URL Parameters:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>account</code> (integer, required): The ID of the account to update.</li>
                </ul>
                <strong className="mb-1 block">Request Body:</strong>
                <p className="mb-1 text-sm">Include `account_type_id` and fields relevant to that type (similar to POST).</p>
                <strong className="mb-1 block">Responses:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>200 OK</code>: Returns the updated account object (with relations).</li>
                    <li><code>422 Unprocessable Entity</code>: Validation errors.</li>
                    <li><code>400 Bad Request</code>: Invalid `account_type_id`.</li>
                    <li><code>404 Not Found</code>: If the account does not exist.</li>
                    <li><code>401/403 Unauthorized</code>: If the user is not authenticated or lacks permission.</li>
                </ul>

                <h4 className="text-md font-semibold mt-4 mb-2">DELETE /api/accounts/{'{account}'}</h4>
                <p className="mb-2">Deletes an existing account. Requires Admin role.</p>
                <strong className="mb-1 block">Authentication:</strong>
                <p className="mb-2">Requires a valid Sanctum session cookie and Admin role.</p>
                <strong className="mb-1 block">URL Parameters:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>account</code> (integer, required): The ID of the account to delete.</li>
                </ul>
                <strong className="mb-1 block">Responses:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>200 OK</code>: Returns a success message.</li>
                    <li><code>404 Not Found</code>: If the account does not exist.</li>
                    <li><code>401/403 Unauthorized</code>: If the user is not authenticated or lacks permission.</li>
                </ul>
            </div>
        ),
    },
    {        id: 'payment-types',
        title: 'Payment Types',
        content: (
            <div>
                <h3 className="text-lg font-semibold mb-2">Overview</h3>
                <p className="mb-4">Endpoints for managing payment types (e.g., Cash, Bank Transfer). All endpoints require authentication via Sanctum.</p>

                <h4 className="text-md font-semibold mt-4 mb-2">GET /api/payment-types</h4>
                <p className="mb-2">Retrieves a list of all payment types.</p>
                <strong className="mb-1 block">Authentication:</strong>
                <p className="mb-2">Requires a valid Sanctum session cookie.</p>
                <strong className="mb-1 block">Responses:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>200 OK</code>: Returns an array of payment type objects.</li>
                    <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm mt-1 mb-2">
                        <code>{`[
    {
        "id": 1,
        "name": "Cash",
        "created_at": "2023-10-28T15:00:00.000000Z",
        "updated_at": "2023-10-28T15:00:00.000000Z"
    },
    // ... more payment types
]`}
                        </code>
                    </pre>
                    <li><code>401 Unauthorized</code>: If the user is not authenticated.</li>
                </ul>

                <h4 className="text-md font-semibold mt-4 mb-2">POST /api/payment-types</h4>
                <p className="mb-2">Creates a new payment type. Requires Admin/Editor role.</p>
                <strong className="mb-1 block">Authentication:</strong>
                <p className="mb-2">Requires a valid Sanctum session cookie and Admin/Editor role.</p>
                <strong className="mb-1 block">Request Body:</strong>
                <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm mb-2">
                    <code>{`{
    "name": "Credit Card"
}`}
                    </code>
                </pre>
                <strong className="mb-1 block">Responses:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>201 Created</code>: Returns the newly created payment type object.</li>
                    <li><code>422 Unprocessable Entity</code>: Validation errors (e.g., name missing or not unique).</li>
                    <li><code>401/403 Unauthorized</code>: If the user is not authenticated or lacks permission.</li>
                </ul>

                <h4 className="text-md font-semibold mt-4 mb-2">GET /api/payment-types/{'{paymentType}'}</h4>
                <p className="mb-2">Retrieves a specific payment type by ID.</p>
                <strong className="mb-1 block">Authentication:</strong>
                <p className="mb-2">Requires a valid Sanctum session cookie.</p>
                 <strong className="mb-1 block">URL Parameters:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>paymentType</code> (integer, required): The ID of the payment type to retrieve.</li>
                </ul>
                <strong className="mb-1 block">Responses:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>200 OK</code>: Returns the payment type object.</li>
                    <li><code>404 Not Found</code>: If the payment type does not exist.</li>
                    <li><code>401 Unauthorized</code>: If the user is not authenticated.</li>
                </ul>

                <h4 className="text-md font-semibold mt-4 mb-2">PUT /api/payment-types/{'{paymentType}'}</h4>
                <p className="mb-2">Updates an existing payment type. Requires Admin/Editor role.</p>
                <strong className="mb-1 block">Authentication:</strong>
                <p className="mb-2">Requires a valid Sanctum session cookie and Admin/Editor role.</p>
                <strong className="mb-1 block">URL Parameters:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>paymentType</code> (integer, required): The ID of the payment type to update.</li>
                </ul>
                <strong className="mb-1 block">Request Body:</strong>
                <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm mb-2">
                    <code>{`{
    "name": "Online Payment"
}`}
                    </code>
                </pre>
                <strong className="mb-1 block">Responses:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>200 OK</code>: Returns the updated payment type object.</li>
                    <li><code>422 Unprocessable Entity</code>: Validation errors.</li>
                    <li><code>404 Not Found</code>: If the payment type does not exist.</li>
                    <li><code>401/403 Unauthorized</code>: If the user is not authenticated or lacks permission.</li>
                </ul>

                <h4 className="text-md font-semibold mt-4 mb-2">DELETE /api/payment-types/{'{paymentType}'}</h4>
                <p className="mb-2">Deletes an existing payment type. Requires Admin role.</p>
                <strong className="mb-1 block">Authentication:</strong>
                <p className="mb-2">Requires a valid Sanctum session cookie and Admin role.</p>
                <strong className="mb-1 block">URL Parameters:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>paymentType</code> (integer, required): The ID of the payment type to delete.</li>
                </ul>
                <strong className="mb-1 block">Responses:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>204 No Content</code>: Successful deletion.</li>
                    <li><code>404 Not Found</code>: If the payment type does not exist.</li>
                    <li><code>401/403 Unauthorized</code>: If the user is not authenticated or lacks permission.</li>
                </ul>
            </div>
        ),
    },
    {
        id: 'journal-entries',
        title: 'Journal Entries',
        content: (
            <div>
                <h3 className="text-lg font-semibold mb-2">Overview</h3>
                <p className="mb-4">Endpoints for managing journal entries (double-entry bookkeeping). All endpoints require authentication via Sanctum.</p>

                <h4 className="text-md font-semibold mt-4 mb-2">GET /api/journal-entries</h4>
                <p className="mb-2">Retrieves a paginated list of journal entries (10 per page), ordered by latest first. Includes related data: `paymentType`, `creditAccount`, `debitAccount`, `createdBy`.</p>
                <strong className="mb-1 block">Authentication:</strong>
                <p className="mb-2">Requires a valid Sanctum session cookie.</p>
                <strong className="mb-1 block">Responses:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>200 OK</code>: Returns a paginated response object containing an array of journal entry objects.</li>
                    {/* Add example response structure if helpful */}
                    <li><code>401 Unauthorized</code>: If the user is not authenticated.</li>
                </ul>

                <h4 className="text-md font-semibold mt-4 mb-2">POST /api/journal-entries (Single Entry)</h4>
                <p className="mb-2">Creates a single new journal entry. Requires Admin/Editor role.</p>
                <strong className="mb-1 block">Authentication:</strong>
                <p className="mb-2">Requires a valid Sanctum session cookie and Admin/Editor role.</p>
                <strong className="mb-1 block">Request Body:</strong>
                <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm mb-2">
                    <code>{`{
    "date": "YYYY-MM-DD", // required
    "payment_type_id": 1, // required, ID of the payment type
    "credit_account_id": 5, // required, ID of the credit account
    "debit_account_id": 10, // required, ID of the debit account
    "description": "Office supplies purchase", // nullable
    "chq_no": null, // nullable
    "amount": 50.75 // required, numeric, min: 0
}`}
                    </code>
                </pre>
                <strong className="mb-1 block">Responses:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>201 Created</code>: Returns the newly created journal entry object.</li>
                    <li><code>422 Unprocessable Entity</code>: Validation errors.</li>
                    <li><code>401/403 Unauthorized</code>: If the user is not authenticated or lacks permission.</li>
                </ul>

                <h4 className="text-md font-semibold mt-4 mb-2">POST /api/journal-entries/multiple (Multiple Entries)</h4>
                <p className="mb-2">Creates multiple journal entries in a single transaction. Requires Admin/Editor role. The total debit amount must equal the total credit amount across all entries provided.</p>
                <strong className="mb-1 block">Authentication:</strong>
                <p className="mb-2">Requires a valid Sanctum session cookie and Admin/Editor role.</p>
                <strong className="mb-1 block">Request Body:</strong>
                <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm mb-2">
                    <code>{`{
    "date": "YYYY-MM-DD", // required
    "payment_type_id": 2, // required, ID of the payment type (e.g., Bank Transfer)
    "entries": [ // required, array of entry objects
        {
            "credit_account_id": 2, // required, ID of the credit account
            "debit_account_id": 15, // required, ID of the debit account
            "description": "Purchase of equipment", // nullable
            "chq_no": "12345", // nullable
            "amount": 1200.00 // required, numeric, min: 0
        },
        {
            "credit_account_id": 2, // required, ID of the credit account
            "debit_account_id": 16, // required, ID of the debit account
            "description": "Delivery fee", // nullable
            "chq_no": "12345", // nullable
            "amount": 50.00 // required, numeric, min: 0
        }
        // Add more entries as needed, ensuring debits = credits
    ]
}`}
                    </code>
                </pre>
                <strong className="mb-1 block">Responses:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>201 Created</code>: Returns a success message and the common `transaction_number`.</li>
                    <li><code>422 Unprocessable Entity</code>: Validation errors (e.g., missing fields, debit/credit mismatch).</li>
                    <li><code>401/403 Unauthorized</code>: If the user is not authenticated or lacks permission.</li>
                </ul>

                <h4 className="text-md font-semibold mt-4 mb-2">GET /api/journal-entries/{'{journalEntry}'}</h4>
                <p className="mb-2">Retrieves a specific journal entry by ID, including related data.</p>
                <strong className="mb-1 block">Authentication:</strong>
                <p className="mb-2">Requires a valid Sanctum session cookie.</p>
                 <strong className="mb-1 block">URL Parameters:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>journalEntry</code> (integer, required): The ID of the journal entry to retrieve.</li>
                </ul>
                <strong className="mb-1 block">Responses:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>200 OK</code>: Returns the journal entry object with relations.</li>
                    <li><code>404 Not Found</code>: If the entry does not exist.</li>
                    <li><code>401 Unauthorized</code>: If the user is not authenticated.</li>
                </ul>

                <h4 className="text-md font-semibold mt-4 mb-2">PUT /api/journal-entries/{'{journalEntry}'}</h4>
                <p className="mb-2">Updates an existing journal entry. Requires Admin/Editor role. Regenerates narration based on updated data.</p>
                <strong className="mb-1 block">Authentication:</strong>
                <p className="mb-2">Requires a valid Sanctum session cookie and Admin/Editor role.</p>
                <strong className="mb-1 block">URL Parameters:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>journalEntry</code> (integer, required): The ID of the journal entry to update.</li>
                </ul>
                <strong className="mb-1 block">Request Body:</strong>
                <p className="mb-1 text-sm">Include fields to update (similar structure to POST, but fields are optional).</p>
                <strong className="mb-1 block">Responses:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>200 OK</code>: Returns a success message.</li>
                    <li><code>422 Unprocessable Entity</code>: Validation errors.</li>
                    <li><code>404 Not Found</code>: If the entry does not exist.</li>
                    <li><code>401/403 Unauthorized</code>: If the user is not authenticated or lacks permission.</li>
                </ul>

                <h4 className="text-md font-semibold mt-4 mb-2">DELETE /api/journal-entries/{'{journalEntry}'}</h4>
                <p className="mb-2">Deletes an existing journal entry. Requires Admin role.</p>
                <strong className="mb-1 block">Authentication:</strong>
                <p className="mb-2">Requires a valid Sanctum session cookie and Admin role.</p>
                <strong className="mb-1 block">URL Parameters:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>journalEntry</code> (integer, required): The ID of the journal entry to delete.</li>
                </ul>
                <strong className="mb-1 block">Responses:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>204 No Content</code>: Successful deletion.</li>
                    <li><code>404 Not Found</code>: If the entry does not exist.</li>
                    <li><code>401/403 Unauthorized</code>: If the user is not authenticated or lacks permission.</li>
                </ul>
            </div>
        ),
    },
    {
        id: 'cheque-management',
        title: 'Cheque Management',
        content: (
            <div>
                <h3 className="text-lg font-semibold mt-6 mb-2">Cheque Management</h3>
                <p className="mb-2">Endpoints for managing cheque entries and their statuses. All endpoints require authentication via Sanctum and appropriate role permissions.</p>
    
                <div className="bg-yellow-50 dark:bg-yellow-900/20 border-l-4 border-yellow-400 p-4 mb-4">
                    <div className="flex">
                        <div className="flex-shrink-0">
                            <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                            </svg>
                        </div>
                        <div className="ml-3">
                            <p className="text-sm text-yellow-700 dark:text-yellow-300">
                                <strong>Important:</strong> Cheque entries must be unique based on the combination of <code>cheque_date</code>, <code>chq_ref_bank_id</code>, <code>chq_no</code>, and <code>amount</code>.
                                This constraint is enforced at both the application and database levels.
                            </p>
                        </div>
                    </div>
                </div>
    
                <h4 className="text-md font-semibold mt-4 mb-2">GET /api/cheque-entries</h4>
                <p className="mb-2">Retrieves a list of cheque entries with optional filtering.</p>
                <strong className="mb-1 block">Authentication:</strong>
                <p className="mb-2">Requires a valid Sanctum session cookie with Viewer, Editor, or Admin role.</p>
                <strong className="mb-1 block">Query Parameters:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>status</code> (string, optional): Filter by status (pending, ok, returned).</li>
                    <li><code>from_date</code> (date, optional): Filter cheques dated on or after this date.</li>
                    <li><code>to_date</code> (date, optional): Filter cheques dated on or before this date.</li>
                    <li><code>account_id</code> (integer, optional): Filter by account ID.</li>
                </ul>
                <strong className="mb-1 block">Responses:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>200 OK</code>: Returns an array of cheque entry objects.</li>
                    <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm mt-1 mb-2">
                        <code>{`[
        {
            "id": 1,
            "cheque_number": "CHQ12345",
            "amount": 1000.50,
            "status": "pending",
            "date": "2023-10-28",
            "to_account_id": 5,
            "from_account_id": 3,
            "chq_ref_bank_id": 2,
            "created_at": "2023-10-28T10:00:00.000000Z",
            "updated_at": "2023-10-28T10:00:00.000000Z"
        }
    ]`}
                        </code>
                    </pre>
                    <li><code>401 Unauthorized</code>: If the user is not authenticated.</li>
                </ul>
    
                <h4 className="text-md font-semibold mt-4 mb-2">POST /api/cheque-entries</h4>
                <p className="mb-2">Creates a new cheque entry. Requires Editor or Admin role.</p>
                <strong className="mb-1 block">Authentication:</strong>
                <p className="mb-2">Requires a valid Sanctum session cookie with Editor or Admin role.</p>
                <strong className="mb-1 block">Request Body:</strong>
                <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm mb-2">
                    <code>{`{
        "cheque_number": "CHQ12345",
        "amount": 1000.50,
        "date": "2023-10-28",
        "to_account_id": 5,
        "from_account_id": 3,
        "chq_ref_bank_id": 2,
    }`}
                    </code>
                </pre>
                <strong className="mb-1 block">Responses:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>201 Created</code>: Returns the newly created cheque entry.</li>
                    <li><code>401 Unauthorized</code>: If user is not authenticated.</li>
                    <li><code>403 Forbidden</code>: If user doesn't have required role.</li>
                    <li><code>422 Unprocessable Entity</code>: Validation errors.</li>
                </ul>

                <h4 className="text-md font-semibold mt-4 mb-2">POST /api/cheque-entries/multiple</h4>
                <p className="mb-2">Creates multiple cheque entries in a single request. Requires Editor or Admin role.</p>
                <strong className="mb-1 block">Authentication:</strong>
                <p className="mb-2">Requires a valid Sanctum session cookie with Editor or Admin role.</p>
                <strong className="mb-1 block">Request Body:</strong>
                <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm mb-2">
                    <code>{`{
        "entries": [
            {
                "entry_type": "inward",
                "entry_date": "2023-10-28",
                "cheque_date": "2023-10-28",
                "posting_date": "2023-10-28",
                "from_account_id": 1,
                "to_account_id": 2,
                "amount": 1000,
                "chq_ref_bank_id": 3,
                "chq_no": "12345",
            },
            {
                "entry_type": "outward",
                "entry_date": "2023-10-28",
                "cheque_date": "2023-10-29",
                "from_account_id": 2,
                "to_account_id": 1,
                "amount": 2000,
                "chq_ref_bank_id": 3,
                "chq_no": "12346",
            }
        ]
    }`}
                    </code>
                </pre>
                <strong className="mb-1 block">Request Body Fields:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>entries</code> (array, required): Array of cheque entry objects.</li>
                    <li><code>entry_type</code> (string, required): Type of entry (inward/outward).</li>
                    <li><code>entry_date</code> (date, required): Date of entry (YYYY-MM-DD).</li>
                    <li><code>cheque_date</code> (date, required): Date on the cheque (YYYY-MM-DD).</li>
                    <li><code>posting_date</code> (date, optional): Date of posting (YYYY-MM-DD).</li>
                    <li><code>from_account_id</code> (integer, required): ID of the source account.</li>
                    <li><code>to_account_id</code> (integer, required): ID of the destination account.</li>
                    <li><code>amount</code> (numeric, required): Cheque amount should be greater than 0.</li>
                    <li><code>chq_ref_bank_id</code> (integer, required): ID of the reference bank.</li>
                    <li><code>chq_no</code> (string, required): Cheque number.</li>
                </ul>
                <strong className="mb-1 block">Responses:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>201 Created</code>: Returns the created cheque entries.</li>
                    <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm mt-1 mb-2">
                        <code>{`{
        "message": "Cheque entries created successfully",
        "data": {
            "count": 2,
            "entries": [
                {
                    "id": 1,
                    "entry_type": "inward",
                    "entry_date": "2023-10-28",
                    "cheque_date": "2023-10-28",
                    "posting_date": "2023-10-28",
                    "from_account_id": 1,
                    "to_account_id": 2,
                    "amount": "1000",
                    "chq_ref_bank_id": 3,
                    "chq_no": "12345",
                    "status": "pending",
                    "created_at": "2023-10-28T10:00:00.000000Z",
                    "updated_at": "2023-10-28T10:00:00.000000Z"
                },
                {
                    "id": 2,
                    "entry_type": "outward",
                    "entry_date": "2023-10-28",
                    "cheque_date": "2023-10-29",
                    "from_account_id": 2,
                    "to_account_id": 1,
                    "amount": "2000",
                    "chq_ref_bank_id": 3,
                    "chq_no": "12346",
                    "status": "pending",
                    "created_at": "2023-10-28T10:00:00.000000Z",
                    "updated_at": "2023-10-28T10:00:00.000000Z"
                }
            ]
        }
    }`}
                        </code>
                    </pre>
                    <li><code>400 Bad Request</code>: If there are duplicate cheque entries in the request.</li>
                    <li><code>401 Unauthorized</code>: If user is not authenticated.</li>
                    <li><code>403 Forbidden</code>: If user doesn't have required role.</li>
                    <li><code>422 Unprocessable Entity</code>: Validation errors.</li>
                </ul>

                <h4 className="text-md font-semibold mt-4 mb-2">PUT /api/cheque-entries/{"{cheque}"}</h4>
                <p className="mb-2">Updates a cheque entry. Can be used to update status (pending → ok, pending → returned, ok → returned). Requires Editor or Admin role.</p>
                <strong className="mb-1 block">Authentication:</strong>
                <p className="mb-2">Requires a valid Sanctum session cookie with Editor or Admin role.</p>
                <strong className="mb-1 block">URL Parameters:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>cheque</code> (integer, required): The ID of the cheque entry to update.</li>
                </ul>
                <strong className="mb-1 block">Request Body:</strong>
                <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm mb-2">
                    <code>{`{
        "status": "ok",  // Can be 'pending', 'ok', or 'returned'
    }`}
                    </code>
                </pre>
                <strong className="mb-1 block">Status Transition Rules:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><strong>pending → ok</strong>: Creates journal entries for the cleared cheque.</li>
                    <li><strong>pending → returned</strong>: Only updates status, no journal entries.</li>
                    <li><strong>ok → returned</strong>: Creates reversal journal entries.</li>
                </ul>
                <strong className="mb-1 block">Responses:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>200 OK</code>: Returns the updated cheque entry.</li>
                    <li><code>400 Bad Request</code>: If status transition is invalid.</li>
                    <li><code>401 Unauthorized</code>: If user is not authenticated.</li>
                    <li><code>403 Forbidden</code>: If user doesn't have required role.</li>
                    <li><code>404 Not Found</code>: If cheque entry doesn't exist.</li>
                    <li><code>422 Unprocessable Entity</code>: Validation errors.</li>
                </ul>
    
                <h4 className="text-md font-semibold mt-4 mb-2">DELETE /api/cheque-entries/{"{cheque}"}</h4>
                <p className="mb-2">Deletes a cheque entry. Requires Admin role. Cannot delete cheques that have associated journal entries.</p>
                <strong className="mb-1 block">Authentication:</strong>
                <p className="mb-2">Requires a valid Sanctum session cookie with Admin role.</p>
                <strong className="mb-1 block">URL Parameters:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>cheque</code> (integer, required): The ID of the cheque entry to delete.</li>
                </ul>
                <strong className="mb-1 block">Responses:</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>200 OK</code>: Success message.</li>
                    <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm mt-1 mb-2">
                        <code>{`{
        "message": "Cheque entry deleted successfully."
    }`}
                        </code>
                    </pre>
                    <li><code>401 Unauthorized</code>: If user is not authenticated.</li>
                    <li><code>403 Forbidden</code>: If user doesn't have Admin role.</li>
                    <li><code>404 Not Found</code>: If cheque entry doesn't exist.</li>
                    <li><code>422 Unprocessable Entity</code>: If cheque has associated journal entries.</li>
                </ul>
            </div>
        )
    },
    {
        id: 'api-token-authentication',
        title: 'API Token Authentication (Sanctum)',
        content: (
            <div>
                <h3 className="text-lg font-semibold mb-2">Overview</h3>
                <p className="mb-4">
                    For API clients (like Postman, curl, mobile apps), authentication is handled using Laravel Sanctum API tokens. You can log in with either your email or username to receive a Bearer token, which must be included in the <code>Authorization</code> header for all protected API requests.
                </p>

                <h4 className="text-md font-semibold mt-4 mb-2">POST /api/login</h4>
                <p className="mb-2">Obtain an API token by providing your email or username and password.</p>
                <strong className="mb-1 block">Request Headers (required):</strong>
                <ul className="list-disc list-inside mb-2">
                    <li><code>Content-Type: application/json</code></li>
                    <li><code>Accept: application/json</code></li>
                </ul>
                <strong className="mb-1 block">Request Body:</strong>
                <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm mb-2">
                    <code>{`{
    "identifier": "your_email_or_username",
    "password": "your_password"
}`}</code>
                </pre>
                <strong className="mb-1 block">Example (curl):</strong>
                <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm mb-2">
                    <code>{`curl --location 'http://127.0.0.1:8000/api/login' \
--header 'Accept: application/json' \
--header 'Content-Type: application/json' \
--data-raw '{
    "identifier": "<EMAIL>",
    "password": "test1234"
}'`}</code>
                </pre>
                <strong className="mb-1 block">Example (Postman):</strong>
                <ul className="list-disc list-inside mb-2">
                    <li>Set method to <code>POST</code> and URL to <code>http://127.0.0.1:8000/api/login</code></li>
                    <li>In the <strong>Body</strong> tab, select <strong>raw</strong> and <strong>JSON</strong></li>
                    <li>Paste the JSON body as above</li>
                    <li>Ensure <strong>Headers</strong> include <code>Content-Type: application/json</code> and <code>Accept: application/json</code></li>
                </ul>
                <strong className="mb-1 block">Response:</strong>
                <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm mb-2">
                    <code>{`{
    "access_token": "YOUR_TOKEN_HERE",
    "token_type": "Bearer"
}`}</code>
                </pre>
                <p className="mb-2">Copy the <code>access_token</code> value for use in subsequent requests.</p>

                <h4 className="text-md font-semibold mt-4 mb-2">Using the Token</h4>
                <p className="mb-2">For all protected API endpoints, add this header:</p>
                <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm mb-2">
                    <code>{`Authorization: Bearer YOUR_TOKEN_HERE`}</code>
                </pre>
                <p className="mb-2">Example (curl):</p>
                <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm mb-2">
                    <code>{`curl --location 'http://127.0.0.1:8000/api/todos' \
--header 'Accept: application/json' \
--header 'Authorization: Bearer YOUR_TOKEN_HERE'`}</code>
                </pre>

                <h4 className="text-md font-semibold mt-4 mb-2">Common Mistakes & Troubleshooting</h4>
                <ul className="list-disc list-inside mb-2">
                    <li><strong>Wrong Content-Type:</strong> <span className="text-red-500">Do NOT use <code>text/plain</code></span>. Always use <code>Content-Type: application/json</code> for JSON requests.</li>
                    <li><strong>Missing Accept Header:</strong> Some endpoints require <code>Accept: application/json</code> to return JSON responses.</li>
                    <li><strong>Malformed JSON:</strong> Ensure your request body is valid JSON (no trailing commas, correct quotes).</li>
                    <li><strong>Sending as form-data or x-www-form-urlencoded:</strong> Use <strong>raw</strong> and <strong>JSON</strong> in Postman, not form-data.</li>
                    <li><strong>Sending cookies:</strong> You do <strong>not</strong> need to send cookies to obtain a token.</li>
                    <li><strong>HTML response instead of JSON:</strong> This means your headers or body are incorrect. Double-check <code>Content-Type</code> and <code>Accept</code> headers.</li>
                </ul>
                <h4 className="text-md font-semibold mt-4 mb-2">Notes</h4>
                <ul className="list-disc list-inside mb-2">
                    <li>You can use either your <strong>email</strong> or <strong>username</strong> as the <code>identifier</code> field.</li>
                    <li>All protected API endpoints require the Bearer token in the <code>Authorization</code> header.</li>
                    <li>If you receive a 401 or 403 error, check your token and permissions.</li>
                </ul>
                <h4 className="text-md font-semibold mt-6 mb-2">Step-by-Step: Using Postman for API Authentication</h4>
                <ol className="list-decimal list-inside mb-4 space-y-2">
                    <li>
                        <strong>Go to the <span className="text-blue-600">Headers</span> tab in Postman.</strong><br />
                        <ul className="list-disc list-inside ml-6">
                            <li>If you see <code>Content-Type: text/plain</code> (checked by default), <strong>uncheck it</strong> (you cannot change its value directly).</li>
                            <li>Add a new key: <code>Content-Type</code> with value <code>application/json</code>.</li>
                            <li>Add another key: <code>Accept</code> with value <code>application/json</code>.</li>
                        </ul>
                    </li>
                    <li>
                        <strong>Go to the <span className="text-blue-600">Body</span> tab.</strong><br />
                        <ul className="list-disc list-inside ml-6">
                            <li>Select <strong>raw</strong> and choose <strong>JSON</strong> from the dropdown.</li>
                            <li>Paste the following JSON, replacing with your actual credentials:</li>
                        </ul>
                        <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm mt-2 mb-2">
                            <code>{`{
    "identifier": "your_email_or_username",
    "password": "your_password"
}`}</code>
                        </pre>
                    </li>
                    <li>
                        <strong>Send the request.</strong><br />
                        <ul className="list-disc list-inside ml-6">
                            <li>You should receive a response like:</li>
                        </ul>
                        <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm mt-2 mb-2">
                            <code>{`{
    "access_token": "1|aiAKixxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
    "token_type": "Bearer"
}`}</code>
                        </pre>
                    </li>
                    <li>
                        <strong>Copy the <code>access_token</code> value.</strong>
                    </li>
                    <li>
                        <strong>Go to the <span className="text-blue-600">Auth</span> tab in Postman.</strong><br />
                        <ul className="list-disc list-inside ml-6">
                            <li>From the dropdown, select <strong>Bearer Token</strong>.</li>
                            <li>Paste your token into the <strong>Token</strong> field.</li>
                        </ul>
                    </li>
                    <li>
                        <strong>Now you can make authenticated requests to any protected API endpoint.</strong>
                    </li>
                </ol>
                <p className="mb-2">This process ensures your requests are properly authenticated and formatted for the API.</p>
            </div>
        ),
    },
    // Add more sections as needed
];

export default function ApiDocumentation() {
    const [activeSection, setActiveSection] = useState<string>(apiSections[0]?.id || '');

    const handleNavClick = (id: string) => {
        setActiveSection(id);
        // Optional: Scroll to the section if content is long
        const element = document.getElementById(id);
        element?.scrollIntoView({ behavior: 'smooth' });
    };

    const currentSection = apiSections.find(section => section.id === activeSection);

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="API Documentation" />

            <div className="flex flex-1 h-full">
                {/* Left Sidebar Navigation */}
                <aside className="w-64 border-r dark:border-gray-700 p-4 overflow-y-auto">
                    <nav className="space-y-2">
                        <h2 className="text-lg font-semibold mb-4">Sections</h2>
                        {apiSections.map((section) => (
                            <button
                                key={section.id}
                                onClick={() => handleNavClick(section.id)}
                                className={cn(
                                    'w-full text-left px-3 py-2 rounded-md text-sm font-medium',
                                    activeSection === section.id
                                        ? 'bg-primary text-primary-foreground'
                                        : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground'
                                )}
                            >
                                {section.title}
                            </button>
                        ))}
                    </nav>
                </aside>

                {/* Right Content Area */}
                <main className="flex-1 p-6 overflow-y-auto">
                    {currentSection ? (
                        <div id={currentSection.id}>
                            <h1 className="text-2xl font-bold mb-4">{currentSection.title}</h1>
                            <div>{currentSection.content}</div>
                        </div>
                    ) : (
                        <p>Select a section from the left to view details.</p>
                    )}
                </main>
            </div>
        </AppLayout>
    );
}