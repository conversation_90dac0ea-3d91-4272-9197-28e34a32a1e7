<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        // Payment Types Table
        Schema::create('payment_types', function (Blueprint $table) {
            $table->id();
            $table->string('name', 100)->unique();
            $table->string('label', 100);
            $table->timestamps();
        });

        // Journal Entries Table
        Schema::create('journal_entries', function (Blueprint $table) {
            $table->id();
            $table->string('TID', 50)->index();
            $table->date('date')->index();
            $table->foreignId('payment_type_id')->constrained('payment_types')->cascadeOnDelete();
            $table->foreignId('account_id')->constrained('accounts')->cascadeOnDelete();
            $table->boolean('is_credit')->default(false);
            $table->string('description', 255);
            $table->string('chq_no', 100)->nullable();
            $table->string('inv_no', 100)->nullable();
            $table->decimal('amount', 18, 2);
            $table->foreignId('created_by')->nullable()->constrained('users')->nullOnDelete();
            $table->timestamps();

            $table->index('account_id');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('journal_entries');
        Schema::dropIfExists('payment_types');
    }
}; 