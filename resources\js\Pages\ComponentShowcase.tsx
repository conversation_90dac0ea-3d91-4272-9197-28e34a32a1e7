import React, { useState } from 'react'
import { Head } from '@inertiajs/react'
import { Button } from '@/components/ui/button'
import { ComboBox } from '@/components/ui/combobox'
import { toast } from '@/components/ui/sonner'
import { 
  Save, 
  Download, 
  Upload, 
  Settings, 
  User, 
  Star, 
  Heart,
  ShoppingCart,
  Bell,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Clock,
  Search,
  Filter,
  Edit,
  Trash2,
  Plus,
  Minus,
  Check,
  X,
  AlertTriangle,
  Info,
  CheckCircle,
  XCircle
} from 'lucide-react'

export default function ComponentShowcase() {
  const [selectedValue, setSelectedValue] = useState('')
  const [selectedGroupValue, setSelectedGroupValue] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  // Sample options for ComboBox
  const simpleOptions = [
    { value: 'option1', label: 'Option 1' },
    { value: 'option2', label: 'Option 2' },
    { value: 'option3', label: 'Option 3' },
    { value: 'option4', label: 'Option 4', disabled: true },
  ]

  const richOptions = [
    { 
      value: 'user1', 
      label: '<PERSON>', 
      description: 'Software Engineer',
      icon: <User className="h-4 w-4" />
    },
    { 
      value: 'user2', 
      label: 'Jane Smith', 
      description: 'Product Manager',
      icon: <User className="h-4 w-4" />
    },
    { 
      value: 'user3', 
      label: 'Bob Johnson', 
      description: 'Designer',
      icon: <User className="h-4 w-4" />
    },
  ]

  const groupedOptions = [
    {
      label: 'Actions',
      options: [
        { value: 'save', label: 'Save', icon: <Save className="h-4 w-4" /> },
        { value: 'download', label: 'Download', icon: <Download className="h-4 w-4" /> },
        { value: 'upload', label: 'Upload', icon: <Upload className="h-4 w-4" /> },
      ]
    },
    {
      label: 'Settings',
      options: [
        { value: 'profile', label: 'Profile', icon: <User className="h-4 w-4" /> },
        { value: 'preferences', label: 'Preferences', icon: <Settings className="h-4 w-4" /> },
        { value: 'notifications', label: 'Notifications', icon: <Bell className="h-4 w-4" /> },
      ]
    }
  ]

  const handleLoadingDemo = async () => {
    setIsLoading(true)
    setTimeout(() => {
      setIsLoading(false)
      toast.success('Operation completed successfully!')
    }, 2000)
  }

  const showToastExamples = () => {
    // Basic toasts
    toast.message('This is a basic message')
    
    setTimeout(() => {
      toast.success('Success! Operation completed.')
    }, 500)
    
    setTimeout(() => {
      toast.error('Error! Something went wrong.')
    }, 1000)
    
    setTimeout(() => {
      toast.warning('Warning! Please check your input.')
    }, 1500)
    
    setTimeout(() => {
      toast.info('Info: New update available.')
    }, 2000)
  }

  const showRichToastExamples = () => {
    toast.success({
      title: 'Payment Successful',
      description: 'Your payment of $99.99 has been processed.',
      action: {
        label: 'View Receipt',
        onClick: () => toast.info('Opening receipt...')
      },
      cancel: {
        label: 'Dismiss',
        onClick: () => toast.dismiss()
      }
    })
  }

  return (
    <>
      <Head title="Component Showcase" />
      
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 p-8">
        <div className="max-w-7xl mx-auto space-y-12">
          {/* Header */}
          <div className="text-center space-y-4">
            <h1 className="text-4xl font-bold text-slate-900 dark:text-slate-100">
              Enhanced shadcn Components Showcase
            </h1>
            <p className="text-lg text-slate-600 dark:text-slate-400 max-w-3xl mx-auto">
              Explore our enhanced shadcn components with comprehensive variants, loading states, 
              and advanced features for modern web applications.
            </p>
          </div>

          {/* Button Component Showcase */}
          <section className="bg-white dark:bg-slate-800 rounded-xl p-8 shadow-lg">
            <h2 className="text-2xl font-semibold mb-6 text-slate-900 dark:text-slate-100">
              Enhanced Button Component
            </h2>
            
            {/* Size Variants */}
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium mb-4 text-slate-700 dark:text-slate-300">Size Variants</h3>
                <div className="flex flex-wrap items-center gap-4">
                  <Button size="xs">Extra Small</Button>
                  <Button size="sm">Small</Button>
                  <Button size="default">Default</Button>
                  <Button size="lg">Large</Button>
                  <Button size="xl">Extra Large</Button>
                </div>
              </div>

              {/* Semantic Variants */}
              <div>
                <h3 className="text-lg font-medium mb-4 text-slate-700 dark:text-slate-300">Semantic Variants</h3>
                <div className="flex flex-wrap items-center gap-4">
                  <Button variant="default">Default</Button>
                  <Button variant="success">Success</Button>
                  <Button variant="warning">Warning</Button>
                  <Button variant="info">Info</Button>
                  <Button variant="destructive">Destructive</Button>
                  <Button variant="outline">Outline</Button>
                  <Button variant="ghost">Ghost</Button>
                  <Button variant="link">Link</Button>
                </div>
              </div>

              {/* Gradient Variants */}
              <div>
                <h3 className="text-lg font-medium mb-4 text-slate-700 dark:text-slate-300">Gradient Variants</h3>
                <div className="flex flex-wrap items-center gap-4">
                  <Button variant="gradient">Purple-Pink</Button>
                  <Button variant="gradient-success">Green-Emerald</Button>
                  <Button variant="gradient-warning">Yellow-Orange</Button>
                  <Button variant="gradient-info">Blue-Cyan</Button>
                </div>
              </div>

              {/* Rounded Variants */}
              <div>
                <h3 className="text-lg font-medium mb-4 text-slate-700 dark:text-slate-300">Rounded Variants</h3>
                <div className="flex flex-wrap items-center gap-4">
                  <Button rounded="none">No Radius</Button>
                  <Button rounded="sm">Small</Button>
                  <Button rounded="default">Default</Button>
                  <Button rounded="lg">Large</Button>
                  <Button rounded="xl">Extra Large</Button>
                  <Button rounded="full">Full</Button>
                </div>
              </div>

              {/* Icon Buttons */}
              <div>
                <h3 className="text-lg font-medium mb-4 text-slate-700 dark:text-slate-300">Icon Buttons</h3>
                <div className="flex flex-wrap items-center gap-4">
                  <Button leftIcon={<Save />}>Save</Button>
                  <Button rightIcon={<Download />}>Download</Button>
                  <Button size="icon"><Settings /></Button>
                  <Button size="icon-sm"><Star /></Button>
                  <Button size="icon-lg"><Heart /></Button>
                  <Button size="icon-xl"><ShoppingCart /></Button>
                </div>
              </div>

              {/* Loading States */}
              <div>
                <h3 className="text-lg font-medium mb-4 text-slate-700 dark:text-slate-300">Loading States</h3>
                <div className="flex flex-wrap items-center gap-4">
                  <Button loading>Loading</Button>
                  <Button loading loadingText="Saving...">Save</Button>
                  <Button loading variant="success" loadingText="Processing...">Process</Button>
                  <Button 
                    loading={isLoading} 
                    onClick={handleLoadingDemo}
                    variant="gradient"
                  >
                    {isLoading ? 'Processing...' : 'Start Demo'}
                  </Button>
                </div>
              </div>
            </div>
          </section>

          {/* ComboBox Component Showcase */}
          <section className="bg-white dark:bg-slate-800 rounded-xl p-8 shadow-lg">
            <h2 className="text-2xl font-semibold mb-6 text-slate-900 dark:text-slate-100">
              Enhanced ComboBox Component
            </h2>

            <div className="space-y-6">
              {/* Size Variants */}
              <div>
                <h3 className="text-lg font-medium mb-4 text-slate-700 dark:text-slate-300">Size Variants</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">Small</label>
                    <ComboBox
                      size="sm"
                      options={simpleOptions}
                      value={selectedValue}
                      onChange={setSelectedValue}
                      placeholder="Select small..."
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">Medium (Default)</label>
                    <ComboBox
                      size="md"
                      options={simpleOptions}
                      value={selectedValue}
                      onChange={setSelectedValue}
                      placeholder="Select medium..."
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">Large</label>
                    <ComboBox
                      size="lg"
                      options={simpleOptions}
                      value={selectedValue}
                      onChange={setSelectedValue}
                      placeholder="Select large..."
                    />
                  </div>
                </div>
              </div>

              {/* Feature Variants */}
              <div>
                <h3 className="text-lg font-medium mb-4 text-slate-700 dark:text-slate-300">Feature Variants</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">Clearable</label>
                    <ComboBox
                      options={simpleOptions}
                      value={selectedValue}
                      onChange={setSelectedValue}
                      placeholder="Clearable..."
                      clearable
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">Disabled</label>
                    <ComboBox
                      options={simpleOptions}
                      value=""
                      onChange={() => {}}
                      placeholder="Disabled..."
                      disabled
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">Loading</label>
                    <ComboBox
                      options={[]}
                      value=""
                      onChange={() => {}}
                      placeholder="Loading..."
                      loading
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">Rich Options</label>
                    <ComboBox
                      options={richOptions}
                      value={selectedValue}
                      onChange={setSelectedValue}
                      placeholder="Select user..."
                      clearable
                    />
                  </div>
                </div>
              </div>

              {/* Grouped Options */}
              <div>
                <h3 className="text-lg font-medium mb-4 text-slate-700 dark:text-slate-300">Grouped Options</h3>
                <div className="max-w-md">
                  <ComboBox
                    groups={groupedOptions}
                    value={selectedGroupValue}
                    onChange={setSelectedGroupValue}
                    placeholder="Select action..."
                    clearable
                    size="lg"
                  />
                </div>
              </div>
            </div>
          </section>

          {/* Toast Component Showcase */}
          <section className="bg-white dark:bg-slate-800 rounded-xl p-8 shadow-lg">
            <h2 className="text-2xl font-semibold mb-6 text-slate-900 dark:text-slate-100">
              Enhanced Toast Component
            </h2>

            <div className="space-y-6">
              {/* Basic Toasts */}
              <div>
                <h3 className="text-lg font-medium mb-4 text-slate-700 dark:text-slate-300">Basic Toast Variants</h3>
                <div className="flex flex-wrap gap-4">
                  <Button onClick={() => toast.message('Basic message toast')}>
                    Basic Message
                  </Button>
                  <Button onClick={() => toast.success('Success toast!')} variant="success">
                    <CheckCircle className="h-4 w-4" />
                    Success
                  </Button>
                  <Button onClick={() => toast.error('Error toast!')} variant="destructive">
                    <XCircle className="h-4 w-4" />
                    Error
                  </Button>
                  <Button onClick={() => toast.warning('Warning toast!')} variant="warning">
                    <AlertTriangle className="h-4 w-4" />
                    Warning
                  </Button>
                  <Button onClick={() => toast.info('Info toast!')} variant="info">
                    <Info className="h-4 w-4" />
                    Info
                  </Button>
                </div>
              </div>

              {/* Advanced Toasts */}
              <div>
                <h3 className="text-lg font-medium mb-4 text-slate-700 dark:text-slate-300">Advanced Toast Features</h3>
                <div className="flex flex-wrap gap-4">
                  <Button onClick={showRichToastExamples} variant="gradient">
                    Rich Toast with Actions
                  </Button>
                  <Button onClick={() => toast.loading('Loading operation...')}>
                    Loading Toast
                  </Button>
                  <Button
                    onClick={() => {
                      const promise = new Promise((resolve) =>
                        setTimeout(() => resolve('Data loaded!'), 2000)
                      )
                      toast.promise(promise, {
                        loading: 'Loading data...',
                        success: 'Data loaded successfully!',
                        error: 'Failed to load data'
                      })
                    }}
                  >
                    Promise Toast
                  </Button>
                  <Button onClick={showToastExamples} variant="outline">
                    Show All Types
                  </Button>
                </div>
              </div>

              {/* Custom Toast */}
              <div>
                <h3 className="text-lg font-medium mb-4 text-slate-700 dark:text-slate-300">Custom Rich Content</h3>
                <Button
                  onClick={() => {
                    toast.custom(
                      <div className="flex items-center gap-3 p-4 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-lg">
                        <Star className="h-6 w-6" />
                        <div>
                          <div className="font-semibold">Custom Toast!</div>
                          <div className="text-sm opacity-90">This is a completely custom toast with gradient background</div>
                        </div>
                      </div>
                    )
                  }}
                  variant="gradient"
                >
                  Custom Rich Toast
                </Button>
              </div>
            </div>
          </section>

          {/* Usage Examples */}
          <section className="bg-white dark:bg-slate-800 rounded-xl p-8 shadow-lg">
            <h2 className="text-2xl font-semibold mb-6 text-slate-900 dark:text-slate-100">
              Usage Examples & Code Snippets
            </h2>

            <div className="space-y-6">
              <div className="bg-slate-50 dark:bg-slate-900 rounded-lg p-4">
                <h4 className="font-medium mb-2">Enhanced Button with Loading</h4>
                <pre className="text-sm text-slate-600 dark:text-slate-400 overflow-x-auto">
{`<Button
  variant="gradient-success"
  size="lg"
  loading={isSubmitting}
  loadingText="Saving..."
  leftIcon={<Save />}
>
  Save Changes
</Button>`}
                </pre>
              </div>

              <div className="bg-slate-50 dark:bg-slate-900 rounded-lg p-4">
                <h4 className="font-medium mb-2">Enhanced ComboBox with Rich Options</h4>
                <pre className="text-sm text-slate-600 dark:text-slate-400 overflow-x-auto">
{`<ComboBox
  size="lg"
  clearable
  options={[
    {
      value: "user1",
      label: "John Doe",
      description: "Software Engineer",
      icon: <User className="h-4 w-4" />
    }
  ]}
  value={selectedValue}
  onChange={setSelectedValue}
/>`}
                </pre>
              </div>

              <div className="bg-slate-50 dark:bg-slate-900 rounded-lg p-4">
                <h4 className="font-medium mb-2">Enhanced Toast with Actions</h4>
                <pre className="text-sm text-slate-600 dark:text-slate-400 overflow-x-auto">
{`toast.success({
  title: "Payment Successful",
  description: "Your payment has been processed.",
  action: {
    label: "View Receipt",
    onClick: () => navigate('/receipt')
  }
})`}
                </pre>
              </div>
            </div>
          </section>

          {/* Footer */}
          <div className="text-center py-8">
            <p className="text-slate-600 dark:text-slate-400">
              Enhanced shadcn components with comprehensive variants and advanced features.
            </p>
          </div>
        </div>
      </div>
    </>
  )
}
