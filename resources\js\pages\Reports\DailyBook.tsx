import { Button } from '@/components/ui/button';
import { DataTable } from '@/components/ui/data-table';
import { DatePicker } from '@/components/ui/date-picker';
import { Skeleton } from '@/components/ui/skeleton'; // Import Skeleton
import AppLayout from '@/layouts/app-layout';
import axios from '@/lib/axios';
import { ColumnDef } from '@tanstack/react-table';
import { isAxiosError } from 'axios';
import { format } from 'date-fns'; // Import format from date-fns
import { useState } from 'react';
import { Head } from '@inertiajs/react';

interface JournalEntry {
    id: number;
    date: string;
    TID: string;
    account_id: number;
    description: string;
    chq_no: string | null;
    inv_no: string | null; // Added inv_no
    amount: number;
    is_credit: boolean;
    created_by: number;
    payment_type_id: number;
    created_at: string;
    updated_at: string;
    payment_type: {
        id: number;
        name: string;
        label: string;
        slug: string;
    };
    account?: {
        id: number;
        name: string;
    };
    created_by_user?: {
        id: number;
        name: string;
        role?: string;
    };
}

const DailyBook = () => {
    const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date());
    const [journalEntries, setJournalEntries] = useState<JournalEntry[]>([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const columns: ColumnDef<JournalEntry>[] = [
        {
            accessorKey: 'TID',
            header: 'TID',
            cell: ({ row }) => {
                const entry = row.original;
                return loading ? <Skeleton className="h-4 w-16" /> : entry.TID;
            },
        },
        {
            accessorKey: 'account_name',
            header: 'Name',
            cell: ({ row }) => {
                const entry = row.original;
                return loading ? <Skeleton className="h-4 w-32" /> : entry.account?.name;
            },
        },
        {
            accessorKey: 'description',
            header: 'Narration',
            cell: ({ row }) => {
                const entry = row.original;
                return loading ? <Skeleton className="h-4 w-48" /> : entry.description;
            },
        },
        {
            accessorKey: 'credit',
            header: 'Credit',
            cell: ({ row }) => {
                const entry = row.original;
                return loading ? <Skeleton className="h-4 w-12" /> : entry.is_credit ? Math.abs(entry.amount) : '';
            },
        },
        {
            accessorKey: 'debit',
            header: 'Debit',
            cell: ({ row }) => {
                const entry = row.original;
                return loading ? <Skeleton className="h-4 w-12" /> : !entry.is_credit ? Math.abs(entry.amount) : '';
            },
        },
        {
            accessorKey: 'chq_no',
            header: 'CHQ#',
            cell: ({ row }) => {
                const entry = row.original;
                return loading ? <Skeleton className="h-4 w-16" /> : entry.chq_no;
            },
        },
        {
            accessorKey: 'inv_no',
            header: 'INV#',
            cell: ({ row }) => {
                const entry = row.original;
                return loading ? <Skeleton className="h-4 w-16" /> : entry.inv_no;
            },
        },
        {
            accessorKey: 'payment_type.name',
            header: 'PaymentType',
            cell: ({ row }) => {
                const entry = row.original;
                return loading ? <Skeleton className="h-4 w-24" /> : entry.payment_type?.label;
            },
        },
        {
            accessorKey: 'created_by_user.name',
            header: 'User',
            cell: ({ row }) => {
                const entry = row.original;
                return loading ? <Skeleton className="h-4 w-20" /> : entry.created_by_user?.name;
            },
        },
        {
            accessorKey: 'action',
            header: 'Action',
            cell: ({ row }) => {
                const entry = row.original;
                return loading ? (
                    <Skeleton className="h-4 w-16" />
                ) : (
                    <Button variant="outline" size="sm" onClick={() => (window.location.href = `/journal-entry/edit/${entry.id}`)}>
                        Edit
                    </Button>
                );
            },
        },
    ];

    const fetchJournalEntries = async () => {
        if (!selectedDate) {
            setError('Please select a date.');
            setJournalEntries([]);
            return;
        }

        setLoading(true);
        setError(null);
        try {
            const formattedDate = format(selectedDate, 'yyyy-MM-dd');
            const response = await axios.get(`/api/journal-entries/by-date?date=${formattedDate}`);
            setJournalEntries(response.data);
        } catch (err: unknown) {
            console.error('Error fetching journal entries:', err);
            if (isAxiosError(err) && err.response?.status === 403) {
                setError('Unauthorized: You do not have permission to view this page.');
            } else {
                setError('Failed to fetch journal entries.');
            }
            setJournalEntries([]);
        } finally {
            setLoading(false);
        }
    };

    // Determine what to show in the table body
    let tableData = journalEntries;
    let showEmptyRow = false;

    if (loading) {
        // Show skeletons as before
        tableData = Array(5).fill({});
    } else if (error) {
        tableData = [];
        showEmptyRow = true;
    }

    return (
        <AppLayout>
            <Head title="Daily Book Report" />
            <div className="container mx-auto px-6 py-6">
                <h1 className="mb-4 text-2xl font-bold">Daily Book Report</h1>

                <div className="mb-6 flex items-center space-x-4">
                    <DatePicker
                        className="w-auto" // Add class to control width
                        selected={selectedDate}
                        onSelect={setSelectedDate}
                    />
                    <Button onClick={fetchJournalEntries} disabled={loading}>
                        {loading ? 'Loading...' : 'View'}
                    </Button>
                    <Button onClick={() => window.print()}>Print</Button> {/* Placeholder Print button */}
                </div>

                <DataTable columns={columns} data={tableData} />
                {!loading && showEmptyRow}
            </div>
        </AppLayout>
    );
};

export default DailyBook;
