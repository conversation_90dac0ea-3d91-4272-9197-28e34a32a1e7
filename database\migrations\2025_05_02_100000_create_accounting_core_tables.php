<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Account Types Table (Reflects schema.sql)
        Schema::create('account_types', function (Blueprint $table) {
            $table->id(); // Equivalent to BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY
            $table->string('name', 100)->unique();
            $table->string('label', 100);
            $table->timestamps(); // Adds nullable created_at and updated_at
        });

        // Currencies Table (Reflects schema.sql)
        Schema::create('currencies', function (Blueprint $table) {
            $table->id();
            $table->string('name', 100);
            $table->string('code', 10)->unique();
            $table->timestamps();
        });

        // Accounts Table (Reflects schema.sql)
        Schema::create('accounts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('account_type_id')->constrained('account_types')->cascadeOnDelete();
            $table->string('name');
            $table->foreignId('user_id')->constrained('users')->cascadeOnDelete();
            $table->string('contact', 100)->nullable();
            $table->string('email')->nullable();
            $table->string('description')->nullable();
            $table->string('ac_holder')->nullable();
            $table->string('ac_number', 100)->nullable();
            $table->string('address')->nullable();
            $table->decimal('salary', 15, 2)->nullable();       // Nullable
            $table->date('joining_date')->nullable();           // Nullable
            $table->foreignId('currency_id')->nullable()->constrained('currencies')->nullOnDelete(); // Nullable FK
            $table->char('formula', 1)->nullable();             // Nullable
            $table->string('code', 50)->nullable();
            $table->string('cnic', 30)->nullable();            // Nullable
            $table->string('father_name')->nullable();    // Nullable
            $table->timestamps();

            // Add index for name searching
            $table->index('name');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop in reverse order of creation due to constraints
        Schema::dropIfExists('accounts');
        Schema::dropIfExists('currencies');
        Schema::dropIfExists('account_types');
    }
}; 