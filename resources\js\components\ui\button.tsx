import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"
import { Loader2 } from "lucide-react"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",
  {
    variants: {
      variant: {
        default:
          "bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",
        destructive:
          "bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",
        outline:
          "border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",
        secondary:
          "bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",
        ghost:
          "hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",
        link: "text-primary underline-offset-4 hover:underline",
        success:
          "bg-success text-success-foreground shadow-xs hover:bg-success/90 focus-visible:ring-success/20",
        warning:
          "bg-warning text-warning-foreground shadow-xs hover:bg-warning/90 focus-visible:ring-warning/20",
        info:
          "bg-info text-info-foreground shadow-xs hover:bg-info/90 focus-visible:ring-info/20",
        gradient:
          "bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-xs hover:from-purple-600 hover:to-pink-600",
        "gradient-success":
          "bg-gradient-to-r from-green-500 to-emerald-500 text-white shadow-xs hover:from-green-600 hover:to-emerald-600",
        "gradient-warning":
          "bg-gradient-to-r from-yellow-500 to-orange-500 text-white shadow-xs hover:from-yellow-600 hover:to-orange-600",
        "gradient-info":
          "bg-gradient-to-r from-blue-500 to-cyan-500 text-white shadow-xs hover:from-blue-600 hover:to-cyan-600",
      },
      size: {
        xs: "h-7 px-2 text-xs gap-1 has-[>svg]:px-1.5",
        sm: "h-8 px-3 text-sm gap-1.5 has-[>svg]:px-2.5",
        default: "h-9 px-4 py-2 text-sm has-[>svg]:px-3",
        lg: "h-10 px-6 text-base has-[>svg]:px-4",
        xl: "h-12 px-8 text-lg gap-3 has-[>svg]:px-6",
        icon: "size-9",
        "icon-sm": "size-8",
        "icon-lg": "size-10",
        "icon-xl": "size-12",
      },
      rounded: {
        default: "rounded-md",
        none: "rounded-none",
        sm: "rounded-sm",
        lg: "rounded-lg",
        xl: "rounded-xl",
        full: "rounded-full",
      },
      iconPosition: {
        left: "flex-row",
        right: "flex-row-reverse",
        only: "justify-center",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
      rounded: "default",
      iconPosition: "left",
    },
  }
)

interface ButtonProps
  extends React.ComponentProps<"button">,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
  loading?: boolean
  loadingText?: string
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({
    className,
    variant,
    size,
    rounded,
    iconPosition,
    asChild = false,
    loading = false,
    loadingText,
    leftIcon,
    rightIcon,
    children,
    disabled,
    ...props
  }, ref) => {
    const Comp = asChild ? Slot : "button"

    // Determine if this is an icon-only button
    const isIconOnly = size?.includes("icon") || (!children && (leftIcon || rightIcon))

    // Handle loading state
    const isDisabled = disabled || loading

    // Determine icon position based on props
    const effectiveIconPosition = isIconOnly ? "only" : iconPosition

    const renderContent = () => {
      if (loading) {
        return (
          <>
            <Loader2 className="animate-spin" />
            {loadingText && <span>{loadingText}</span>}
            {!loadingText && !isIconOnly && children}
          </>
        )
      }

      if (isIconOnly) {
        return leftIcon || rightIcon || children
      }

      // Handle icon + text combinations
      if (leftIcon && children) {
        return (
          <>
            {leftIcon}
            <span>{children}</span>
          </>
        )
      }

      if (rightIcon && children) {
        return (
          <>
            <span>{children}</span>
            {rightIcon}
          </>
        )
      }

      // Default: just children
      return children
    }

    return (
      <Comp
        ref={ref}
        data-slot="button"
        disabled={isDisabled}
        className={cn(
          buttonVariants({
            variant,
            size,
            rounded,
            iconPosition: effectiveIconPosition,
            className
          })
        )}
        {...props}
      >
        {renderContent()}
      </Comp>
    )
  }
)

Button.displayName = "Button"

export { Button, buttonVariants, type ButtonProps }
