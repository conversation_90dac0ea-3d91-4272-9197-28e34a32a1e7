import React, { useState } from 'react'
import { Head } from '@inertiajs/react'
import AppLayout from '@/layouts/app-layout'
import { Button } from '@/components/ui/button'
import { ComboBox } from '@/components/ui/combobox'
import { toast, Toaster } from '@/components/ui/sonner'
import { 
  Save, 
  Download, 
  Upload, 
  Settings, 
  User, 
  Star, 
  Heart,
  ShoppingCart,
  Bell,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Clock,
  Search,
  Filter,
  Edit,
  Trash2,
  Plus,
  Minus,
  Check,
  X,
  AlertTriangle,
  Info,
  CheckCircle,
  XCircle
} from 'lucide-react'

export default function ComponentShowcase() {
  const [selectedValue, setSelectedValue] = useState('')
  const [selectedGroupValue, setSelectedGroupValue] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  // Sample options for ComboBox
  const simpleOptions = [
    { value: 'option1', label: 'Option 1' },
    { value: 'option2', label: 'Option 2' },
    { value: 'option3', label: 'Option 3' },
    { value: 'option4', label: 'Option 4', disabled: true },
  ]

  const richOptions = [
    {
      value: 'user1',
      label: '<PERSON> Doe',
      description: 'Software Engineer • San Francisco, CA',
      icon: <User className="h-4 w-4" />
    },
    {
      value: 'user2',
      label: 'Jane Smith',
      description: 'Product Manager • New York, NY',
      icon: <User className="h-4 w-4" />
    },
    {
      value: 'user3',
      label: 'Bob Johnson',
      description: 'Designer • Austin, TX',
      icon: <User className="h-4 w-4" />
    },
    {
      value: 'user4',
      label: 'Alice Wilson',
      description: 'DevOps Engineer • Seattle, WA',
      icon: <User className="h-4 w-4" />
    },
  ]

  const locationOptions = [
    {
      value: 'ny',
      label: 'New York',
      description: 'New York, United States • EST',
      icon: <MapPin className="h-4 w-4" />
    },
    {
      value: 'london',
      label: 'London',
      description: 'London, United Kingdom • GMT',
      icon: <MapPin className="h-4 w-4" />
    },
    {
      value: 'tokyo',
      label: 'Tokyo',
      description: 'Tokyo, Japan • JST',
      icon: <MapPin className="h-4 w-4" />
    },
    {
      value: 'sydney',
      label: 'Sydney',
      description: 'Sydney, Australia • AEDT',
      icon: <MapPin className="h-4 w-4" />
    },
  ]

  const contactOptions = [
    {
      value: 'email1',
      label: '<EMAIL>',
      description: 'Primary work email',
      icon: <Mail className="h-4 w-4" />
    },
    {
      value: 'phone1',
      label: '+****************',
      description: 'Mobile phone',
      icon: <Phone className="h-4 w-4" />
    },
    {
      value: 'phone2',
      label: '+****************',
      description: 'Office phone',
      icon: <Phone className="h-4 w-4" />
    },
  ]

  const groupedOptions = [
    {
      label: 'Actions',
      options: [
        { value: 'save', label: 'Save', icon: <Save className="h-4 w-4" /> },
        { value: 'download', label: 'Download', icon: <Download className="h-4 w-4" /> },
        { value: 'upload', label: 'Upload', icon: <Upload className="h-4 w-4" /> },
      ]
    },
    {
      label: 'Settings',
      options: [
        { value: 'profile', label: 'Profile', icon: <User className="h-4 w-4" /> },
        { value: 'preferences', label: 'Preferences', icon: <Settings className="h-4 w-4" /> },
        { value: 'notifications', label: 'Notifications', icon: <Bell className="h-4 w-4" /> },
      ]
    }
  ]

  const handleLoadingDemo = async () => {
    setIsLoading(true)
    setTimeout(() => {
      setIsLoading(false)
      toast.success('Operation completed successfully!')
    }, 2000)
  }

  const showToastExamples = () => {
    // Basic toasts with staggered timing
    toast.message('📝 This is a basic message toast')

    setTimeout(() => {
      toast.success('✅ Success! Operation completed successfully.')
    }, 500)

    setTimeout(() => {
      toast.error('❌ Error! Something went wrong with the request.')
    }, 1000)

    setTimeout(() => {
      toast.warning('⚠️ Warning! Please check your input before proceeding.')
    }, 1500)

    setTimeout(() => {
      toast.info('ℹ️ Info: New update available for download.')
    }, 2000)
  }

  const showRichToastExamples = () => {
    toast.success({
      title: 'Payment Successful',
      description: 'Your payment of $99.99 has been processed successfully.',
      action: {
        label: 'View Receipt',
        onClick: () => toast.info('📄 Opening receipt in new window...')
      },
      cancel: {
        label: 'Dismiss',
        onClick: () => toast.dismiss()
      }
    })
  }

  const showLocationToast = () => {
    toast.info({
      title: 'Location Services',
      description: 'Your current location: New York, NY, USA',
      richContent: (
        <div className="flex items-center gap-3 p-2">
          <MapPin className="h-8 w-8 text-blue-500" />
          <div>
            <div className="font-semibold">📍 Location Detected</div>
            <div className="text-sm text-muted-foreground">Latitude: 40.7128° N</div>
            <div className="text-sm text-muted-foreground">Longitude: 74.0060° W</div>
            <div className="text-xs text-muted-foreground mt-1">Accuracy: ±10 meters</div>
          </div>
        </div>
      ),
      action: {
        label: 'Open Maps',
        onClick: () => toast.success('🗺️ Opening Google Maps...')
      }
    })
  }

  const showNotificationToast = () => {
    toast.custom(
      <div className="flex items-center gap-3 p-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg shadow-lg">
        <Bell className="h-6 w-6" />
        <div className="flex-1">
          <div className="font-semibold">📱 New Notification</div>
          <div className="text-sm opacity-90">You have 3 unread messages</div>
          <div className="text-xs opacity-75 mt-1">2 minutes ago</div>
        </div>
        <Button
          size="sm"
          variant="ghost"
          className="text-white hover:bg-white/20"
          onClick={() => toast.success('📬 Opening messages...')}
        >
          View
        </Button>
      </div>
    )
  }

  const showEmailToast = () => {
    toast.info({
      title: 'New Email Received',
      description: 'From: <EMAIL>',
      richContent: (
        <div className="space-y-3">
          <div className="flex items-center gap-3">
            <Mail className="h-5 w-5 text-blue-500" />
            <div>
              <div className="font-medium">📧 Project Update</div>
              <div className="text-sm text-muted-foreground"><EMAIL></div>
            </div>
          </div>
          <div className="text-sm bg-slate-50 dark:bg-slate-800 p-2 rounded">
            "The quarterly report is ready for review. Please check the attached documents..."
          </div>
          <div className="flex gap-2">
            <Button size="sm" onClick={() => toast.success('📖 Opening email...')}>
              Read
            </Button>
            <Button size="sm" variant="outline" onClick={() => toast.info('📁 Email archived')}>
              Archive
            </Button>
          </div>
        </div>
      )
    })
  }

  const showCalendarToast = () => {
    toast.warning({
      title: 'Meeting Reminder',
      description: 'Team standup in 15 minutes',
      richContent: (
        <div className="space-y-3">
          <div className="flex items-center gap-3">
            <Calendar className="h-5 w-5 text-orange-500" />
            <div>
              <div className="font-medium">📅 Daily Standup</div>
              <div className="text-sm text-muted-foreground">Conference Room A</div>
            </div>
          </div>
          <div className="flex items-center gap-2 text-sm">
            <Clock className="h-4 w-4" />
            <span>10:00 AM - 10:30 AM</span>
          </div>
          <div className="flex gap-2">
            <Button size="sm" onClick={() => toast.success('📞 Joining meeting...')}>
              Join Now
            </Button>
            <Button size="sm" variant="outline" onClick={() => toast.info('⏰ Reminder snoozed for 5 minutes')}>
              Snooze
            </Button>
          </div>
        </div>
      )
    })
  }

  const showProgressToast = () => {
    const toastId = toast.loading('📤 Uploading file... 0%')

    let progress = 0
    const interval = setInterval(() => {
      progress += 20

      if (progress <= 80) {
        toast.loading(`📤 Uploading file... ${progress}%`, { id: toastId })
      } else {
        clearInterval(interval)
        toast.success('✅ File uploaded successfully!', { id: toastId })
      }
    }, 500)
  }

  const showPromiseToast = () => {
    const fetchData = new Promise((resolve, reject) => {
      setTimeout(() => {
        if (Math.random() > 0.3) {
          resolve('Data loaded successfully!')
        } else {
          reject(new Error('Failed to load data'))
        }
      }, 3000)
    })

    toast.promise(fetchData, {
      loading: '🔄 Loading user data...',
      success: (data) => `✅ ${data}`,
      error: '❌ Failed to load data. Please try again.',
    })
  }

  return (
    <AppLayout breadcrumbs={[{ title: 'Component Showcase', href: '/component-showcase' }]}>
      <Head title="Component Showcase" />

      {/* Add Toaster for toast notifications */}
      <Toaster position="bottom-right" />

      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 p-8">
        <div className="max-w-7xl mx-auto space-y-12">
          {/* Header */}
          <div className="text-center space-y-4">
            <h1 className="text-4xl font-bold text-slate-900 dark:text-slate-100">
              Enhanced shadcn Components Showcase
            </h1>
            <p className="text-lg text-slate-600 dark:text-slate-400 max-w-3xl mx-auto">
              Explore our enhanced shadcn components with comprehensive variants, loading states, 
              and advanced features for modern web applications.
            </p>
          </div>

          {/* Button Component Showcase */}
          <section className="bg-white dark:bg-slate-800 rounded-xl p-8 shadow-lg">
            <h2 className="text-2xl font-semibold mb-6 text-slate-900 dark:text-slate-100">
              Enhanced Button Component
            </h2>
            
            {/* Size Variants */}
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium mb-4 text-slate-700 dark:text-slate-300">Size Variants</h3>
                <div className="flex flex-wrap items-center gap-4">
                  <Button size="xs">Extra Small</Button>
                  <Button size="sm">Small</Button>
                  <Button size="default">Default</Button>
                  <Button size="lg">Large</Button>
                  <Button size="xl">Extra Large</Button>
                </div>
              </div>

              {/* Semantic Variants */}
              <div>
                <h3 className="text-lg font-medium mb-4 text-slate-700 dark:text-slate-300">Semantic Variants</h3>
                <div className="flex flex-wrap items-center gap-4">
                  <Button variant="default">Default</Button>
                  <Button variant="success">Success</Button>
                  <Button variant="warning">Warning</Button>
                  <Button variant="info">Info</Button>
                  <Button variant="destructive">Destructive</Button>
                  <Button variant="outline">Outline</Button>
                  <Button variant="ghost">Ghost</Button>
                  <Button variant="link">Link</Button>
                </div>
              </div>

              {/* Gradient Variants */}
              <div>
                <h3 className="text-lg font-medium mb-4 text-slate-700 dark:text-slate-300">Gradient Variants</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <Button variant="gradient" leftIcon={<Star className="h-4 w-4" />}>Purple-Pink</Button>
                  <Button variant="gradient-success" leftIcon={<CheckCircle className="h-4 w-4" />}>Green-Emerald</Button>
                  <Button variant="gradient-warning" leftIcon={<AlertTriangle className="h-4 w-4" />}>Yellow-Orange</Button>
                  <Button variant="gradient-info" leftIcon={<Info className="h-4 w-4" />}>Blue-Cyan</Button>
                </div>
              </div>

              {/* Rounded Variants */}
              <div>
                <h3 className="text-lg font-medium mb-4 text-slate-700 dark:text-slate-300">Rounded Variants</h3>
                <div className="flex flex-wrap items-center gap-4">
                  <Button rounded="none">No Radius</Button>
                  <Button rounded="sm">Small</Button>
                  <Button rounded="default">Default</Button>
                  <Button rounded="lg">Large</Button>
                  <Button rounded="xl">Extra Large</Button>
                  <Button rounded="full">Full</Button>
                </div>
              </div>

              {/* Icon + Text Buttons */}
              <div>
                <h3 className="text-lg font-medium mb-4 text-slate-700 dark:text-slate-300">Icon + Text Buttons</h3>

                {/* Left Icon + Text */}
                <div className="mb-6">
                  <h4 className="text-md font-medium mb-3 text-slate-600 dark:text-slate-400">Left Icon + Text</h4>
                  <div className="flex flex-wrap items-center gap-4">
                    <Button leftIcon={<Save className="h-4 w-4" />}>Save Document</Button>
                    <Button leftIcon={<Download className="h-4 w-4" />} variant="success">Download File</Button>
                    <Button leftIcon={<Upload className="h-4 w-4" />} variant="info">Upload Image</Button>
                    <Button leftIcon={<Plus className="h-4 w-4" />} variant="outline">Add New</Button>
                    <Button leftIcon={<Edit className="h-4 w-4" />} variant="secondary">Edit Item</Button>
                    <Button leftIcon={<Trash2 className="h-4 w-4" />} variant="destructive">Delete</Button>
                  </div>
                </div>

                {/* Right Icon + Text */}
                <div className="mb-6">
                  <h4 className="text-md font-medium mb-3 text-slate-600 dark:text-slate-400">Text + Right Icon</h4>
                  <div className="flex flex-wrap items-center gap-4">
                    <Button rightIcon={<Download className="h-4 w-4" />}>Download Report</Button>
                    <Button rightIcon={<Settings className="h-4 w-4" />} variant="outline">Open Settings</Button>
                    <Button rightIcon={<Search className="h-4 w-4" />} variant="info">Search Now</Button>
                    <Button rightIcon={<Filter className="h-4 w-4" />} variant="secondary">Apply Filters</Button>
                    <Button rightIcon={<Bell className="h-4 w-4" />} variant="warning">View Notifications</Button>
                    <Button rightIcon={<User className="h-4 w-4" />} variant="success">View Profile</Button>
                  </div>
                </div>

                {/* Different Sizes with Icons */}
                <div className="mb-6">
                  <h4 className="text-md font-medium mb-3 text-slate-600 dark:text-slate-400">Different Sizes with Icons</h4>
                  <div className="flex flex-wrap items-center gap-4">
                    <Button leftIcon={<Save className="h-3 w-3" />} size="xs">Extra Small</Button>
                    <Button leftIcon={<Save className="h-4 w-4" />} size="sm">Small</Button>
                    <Button leftIcon={<Save className="h-4 w-4" />} size="default">Default</Button>
                    <Button leftIcon={<Save className="h-5 w-5" />} size="lg">Large</Button>
                    <Button leftIcon={<Save className="h-6 w-6" />} size="xl">Extra Large</Button>
                  </div>
                </div>

                {/* Icon-Only Buttons */}
                <div>
                  <h4 className="text-md font-medium mb-3 text-slate-600 dark:text-slate-400">Icon-Only Buttons</h4>
                  <div className="flex flex-wrap items-center gap-4">
                    <Button size="icon"><Settings className="h-4 w-4" /></Button>
                    <Button size="icon-sm" variant="success"><Check className="h-3 w-3" /></Button>
                    <Button size="icon" variant="destructive"><X className="h-4 w-4" /></Button>
                    <Button size="icon-lg" variant="info"><Star className="h-5 w-5" /></Button>
                    <Button size="icon-xl" variant="warning"><Heart className="h-6 w-6" /></Button>
                    <Button size="icon" variant="outline"><ShoppingCart className="h-4 w-4" /></Button>
                  </div>
                </div>
              </div>

              {/* Loading States */}
              <div>
                <h3 className="text-lg font-medium mb-4 text-slate-700 dark:text-slate-300">Loading States</h3>
                <div className="space-y-4">
                  {/* Basic Loading */}
                  <div>
                    <h4 className="text-md font-medium mb-3 text-slate-600 dark:text-slate-400">Basic Loading States</h4>
                    <div className="flex flex-wrap items-center gap-4">
                      <Button loading>Loading</Button>
                      <Button loading loadingText="Saving...">Save</Button>
                      <Button loading variant="success" loadingText="Processing...">Process</Button>
                      <Button loading variant="destructive" loadingText="Deleting...">Delete</Button>
                    </div>
                  </div>

                  {/* Loading with Icons */}
                  <div>
                    <h4 className="text-md font-medium mb-3 text-slate-600 dark:text-slate-400">Loading with Icons (Icons replaced by spinner)</h4>
                    <div className="flex flex-wrap items-center gap-4">
                      <Button loading leftIcon={<Save className="h-4 w-4" />} loadingText="Saving...">Save Document</Button>
                      <Button loading rightIcon={<Download className="h-4 w-4" />} variant="info" loadingText="Downloading...">Download File</Button>
                      <Button loading leftIcon={<Upload className="h-4 w-4" />} variant="success" loadingText="Uploading...">Upload Image</Button>
                      <Button loading rightIcon={<Settings className="h-4 w-4" />} variant="outline" loadingText="Configuring...">Configure</Button>
                    </div>
                  </div>

                  {/* Interactive Loading Demo */}
                  <div>
                    <h4 className="text-md font-medium mb-3 text-slate-600 dark:text-slate-400">Interactive Loading Demo</h4>
                    <div className="flex flex-wrap items-center gap-4">
                      <Button
                        loading={isLoading}
                        onClick={handleLoadingDemo}
                        variant="gradient"
                        leftIcon={<Star className="h-4 w-4" />}
                        loadingText="Processing..."
                      >
                        {isLoading ? 'Processing...' : 'Start Demo'}
                      </Button>
                      <Button
                        loading={isLoading}
                        onClick={handleLoadingDemo}
                        variant="gradient-success"
                        rightIcon={<CheckCircle className="h-4 w-4" />}
                        loadingText="Completing..."
                      >
                        {isLoading ? 'Working...' : 'Complete Task'}
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* ComboBox Component Showcase */}
          <section className="bg-white dark:bg-slate-800 rounded-xl p-8 shadow-lg">
            <h2 className="text-2xl font-semibold mb-6 text-slate-900 dark:text-slate-100">
              Enhanced ComboBox Component
            </h2>

            <div className="space-y-6">
              {/* Size Variants */}
              <div>
                <h3 className="text-lg font-medium mb-4 text-slate-700 dark:text-slate-300">Size Variants</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">Small</label>
                    <ComboBox
                      size="sm"
                      options={simpleOptions}
                      value={selectedValue}
                      onChange={setSelectedValue}
                      placeholder="Select small..."
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">Medium (Default)</label>
                    <ComboBox
                      size="md"
                      options={simpleOptions}
                      value={selectedValue}
                      onChange={setSelectedValue}
                      placeholder="Select medium..."
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">Large</label>
                    <ComboBox
                      size="lg"
                      options={simpleOptions}
                      value={selectedValue}
                      onChange={setSelectedValue}
                      placeholder="Select large..."
                    />
                  </div>
                </div>
              </div>

              {/* Feature Variants */}
              <div>
                <h3 className="text-lg font-medium mb-4 text-slate-700 dark:text-slate-300">Feature Variants</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">Clearable</label>
                    <ComboBox
                      options={simpleOptions}
                      value={selectedValue}
                      onChange={setSelectedValue}
                      placeholder="Clearable..."
                      clearable
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">Disabled</label>
                    <ComboBox
                      options={simpleOptions}
                      value=""
                      onChange={() => {}}
                      placeholder="Disabled..."
                      disabled
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">Loading</label>
                    <ComboBox
                      options={[]}
                      value=""
                      onChange={() => {}}
                      placeholder="Loading..."
                      loading
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">Rich Options</label>
                    <ComboBox
                      options={richOptions}
                      value={selectedValue}
                      onChange={setSelectedValue}
                      placeholder="Select user..."
                      clearable
                    />
                  </div>
                </div>
              </div>

              {/* Real-world Examples */}
              <div>
                <h3 className="text-lg font-medium mb-4 text-slate-700 dark:text-slate-300">Real-world Examples</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">🌍 Location Selector</label>
                    <ComboBox
                      options={locationOptions}
                      value={selectedValue}
                      onChange={setSelectedValue}
                      placeholder="Select location..."
                      clearable
                      size="lg"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">📞 Contact Method</label>
                    <ComboBox
                      options={contactOptions}
                      value={selectedValue}
                      onChange={setSelectedValue}
                      placeholder="Select contact..."
                      clearable
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">👥 Team Members</label>
                    <ComboBox
                      options={richOptions}
                      value={selectedValue}
                      onChange={setSelectedValue}
                      placeholder="Select team member..."
                      clearable
                      searchPlaceholder="Search by name or location..."
                    />
                  </div>
                </div>
              </div>

              {/* Grouped Options */}
              <div>
                <h3 className="text-lg font-medium mb-4 text-slate-700 dark:text-slate-300">Grouped Options</h3>
                <div className="max-w-md">
                  <ComboBox
                    groups={groupedOptions}
                    value={selectedGroupValue}
                    onChange={setSelectedGroupValue}
                    placeholder="Select action..."
                    clearable
                    size="lg"
                  />
                </div>
              </div>
            </div>
          </section>

          {/* Toast Component Showcase */}
          <section className="bg-white dark:bg-slate-800 rounded-xl p-8 shadow-lg">
            <h2 className="text-2xl font-semibold mb-6 text-slate-900 dark:text-slate-100">
              Enhanced Toast Component
            </h2>

            <div className="space-y-6">
              {/* Basic Toasts */}
              <div>
                <h3 className="text-lg font-medium mb-4 text-slate-700 dark:text-slate-300">Basic Toast Variants</h3>
                <p className="text-sm text-slate-600 dark:text-slate-400 mb-4">
                  Each toast type has distinct colors and styling for easy identification:
                </p>
                <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                  <Button onClick={() => toast.message('📝 Basic message toast')} variant="outline">
                    Basic Message
                  </Button>
                  <Button onClick={() => toast.success('✅ Operation completed successfully!')} variant="success">
                    <CheckCircle className="h-4 w-4" />
                    Success Toast
                  </Button>
                  <Button onClick={() => toast.error('❌ Something went wrong!')} variant="destructive">
                    <XCircle className="h-4 w-4" />
                    Error Toast
                  </Button>
                  <Button onClick={() => toast.warning('⚠️ Please check your input!')} variant="warning">
                    <AlertTriangle className="h-4 w-4" />
                    Warning Toast
                  </Button>
                  <Button onClick={() => toast.info('ℹ️ New update available!')} variant="info">
                    <Info className="h-4 w-4" />
                    Info Toast
                  </Button>
                </div>

                {/* Color Legend */}
                <div className="mt-4 p-4 bg-slate-50 dark:bg-slate-800 rounded-lg">
                  <h4 className="text-sm font-medium mb-2">Color Coding:</h4>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-green-500 rounded"></div>
                      <span>Success - Green</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-red-500 rounded"></div>
                      <span>Error - Red</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-yellow-500 rounded"></div>
                      <span>Warning - Yellow</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-blue-500 rounded"></div>
                      <span>Info - Blue</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Advanced Toasts */}
              <div>
                <h3 className="text-lg font-medium mb-4 text-slate-700 dark:text-slate-300">Advanced Toast Features</h3>
                <div className="flex flex-wrap gap-4">
                  <Button onClick={showRichToastExamples} variant="gradient">
                    💳 Payment Toast
                  </Button>
                  <Button onClick={showProgressToast} variant="info">
                    📤 Progress Toast
                  </Button>
                  <Button onClick={showPromiseToast} variant="outline">
                    🔄 Promise Toast
                  </Button>
                  <Button onClick={showToastExamples} variant="secondary">
                    🎯 Show All Types
                  </Button>
                </div>
              </div>

              {/* Real-world Examples */}
              <div>
                <h3 className="text-lg font-medium mb-4 text-slate-700 dark:text-slate-300">Real-world Examples</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <Button onClick={showLocationToast} variant="success" className="flex flex-col gap-1 h-auto py-3">
                    <MapPin className="h-5 w-5" />
                    <span className="text-xs">Location</span>
                  </Button>
                  <Button onClick={showEmailToast} variant="info" className="flex flex-col gap-1 h-auto py-3">
                    <Mail className="h-5 w-5" />
                    <span className="text-xs">Email</span>
                  </Button>
                  <Button onClick={showCalendarToast} variant="warning" className="flex flex-col gap-1 h-auto py-3">
                    <Calendar className="h-5 w-5" />
                    <span className="text-xs">Calendar</span>
                  </Button>
                  <Button onClick={showNotificationToast} variant="gradient" className="flex flex-col gap-1 h-auto py-3">
                    <Bell className="h-5 w-5" />
                    <span className="text-xs">Notification</span>
                  </Button>
                </div>
              </div>

              {/* Toast Positions */}
              <div>
                <h3 className="text-lg font-medium mb-4 text-slate-700 dark:text-slate-300">Toast Positions</h3>
                <div className="grid grid-cols-3 gap-2 max-w-md">
                  <Button size="sm" onClick={() => toast.success('Top Left!', { position: 'top-left' })}>
                    Top Left
                  </Button>
                  <Button size="sm" onClick={() => toast.success('Top Center!', { position: 'top-center' })}>
                    Top Center
                  </Button>
                  <Button size="sm" onClick={() => toast.success('Top Right!', { position: 'top-right' })}>
                    Top Right
                  </Button>
                  <Button size="sm" onClick={() => toast.success('Bottom Left!', { position: 'bottom-left' })}>
                    Bottom Left
                  </Button>
                  <Button size="sm" onClick={() => toast.success('Bottom Center!', { position: 'bottom-center' })}>
                    Bottom Center
                  </Button>
                  <Button size="sm" onClick={() => toast.success('Bottom Right!', { position: 'bottom-right' })}>
                    Bottom Right
                  </Button>
                </div>
              </div>
            </div>
          </section>

          {/* Usage Examples */}
          <section className="bg-white dark:bg-slate-800 rounded-xl p-8 shadow-lg">
            <h2 className="text-2xl font-semibold mb-6 text-slate-900 dark:text-slate-100">
              Usage Examples & Code Snippets
            </h2>

            <div className="space-y-6">
              <div className="bg-slate-50 dark:bg-slate-900 rounded-lg p-4">
                <h4 className="font-medium mb-2">Enhanced Button Examples</h4>
                <div className="space-y-4">
                  <div>
                    <h5 className="text-sm font-medium mb-2">Left Icon + Text</h5>
                    <pre className="text-xs text-slate-600 dark:text-slate-400 overflow-x-auto">
{`<Button leftIcon={<Save className="h-4 w-4" />} variant="success">
  Save Document
</Button>`}
                    </pre>
                  </div>

                  <div>
                    <h5 className="text-sm font-medium mb-2">Text + Right Icon</h5>
                    <pre className="text-xs text-slate-600 dark:text-slate-400 overflow-x-auto">
{`<Button rightIcon={<Download className="h-4 w-4" />} variant="info">
  Download Report
</Button>`}
                    </pre>
                  </div>

                  <div>
                    <h5 className="text-sm font-medium mb-2">Loading with Icon</h5>
                    <pre className="text-xs text-slate-600 dark:text-slate-400 overflow-x-auto">
{`<Button
  leftIcon={<Save className="h-4 w-4" />}
  loading={isSubmitting}
  loadingText="Saving..."
  variant="gradient-success"
>
  Save Changes
</Button>`}
                    </pre>
                  </div>

                  <div>
                    <h5 className="text-sm font-medium mb-2">Icon-Only Button</h5>
                    <pre className="text-xs text-slate-600 dark:text-slate-400 overflow-x-auto">
{`<Button size="icon" variant="outline">
  <Settings className="h-4 w-4" />
</Button>`}
                    </pre>
                  </div>
                </div>
              </div>

              <div className="bg-slate-50 dark:bg-slate-900 rounded-lg p-4">
                <h4 className="font-medium mb-2">Enhanced ComboBox with Rich Options</h4>
                <pre className="text-sm text-slate-600 dark:text-slate-400 overflow-x-auto">
{`<ComboBox
  size="lg"
  clearable
  options={[
    {
      value: "user1",
      label: "John Doe",
      description: "Software Engineer",
      icon: <User className="h-4 w-4" />
    }
  ]}
  value={selectedValue}
  onChange={setSelectedValue}
/>`}
                </pre>
              </div>

              <div className="bg-slate-50 dark:bg-slate-900 rounded-lg p-4">
                <h4 className="font-medium mb-2">Enhanced Toast with Actions</h4>
                <pre className="text-sm text-slate-600 dark:text-slate-400 overflow-x-auto">
{`toast.success({
  title: "Payment Successful",
  description: "Your payment has been processed.",
  action: {
    label: "View Receipt",
    onClick: () => navigate('/receipt')
  }
})`}
                </pre>
              </div>
            </div>
          </section>

          {/* Footer */}
          <div className="text-center py-8">
            <p className="text-slate-600 dark:text-slate-400">
              Enhanced shadcn components with comprehensive variants and advanced features.
            </p>
          </div>
        </div>
      </div>
    </AppLayout>
  )
}
