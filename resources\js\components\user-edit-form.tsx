import { FormEventHandler } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ComboBox, ComboBoxOption } from '@/components/ui/combobox';
import InputError from '@/components/input-error';
import { type FormDataConvertible } from '@inertiajs/core';

const roles = ['Admin', 'Editor', 'Viewer'];

// Define the shape of the user data passed in
export interface UserData {
    id: number;
    name: string;
    username: string;
    email: string;
    role: string;
}

// Define the form data structure, including optional password fields
export interface UserEditFormData {
    name: string;
    username: string;
    email: string; // Keep email for display, though not editable
    password?: string;
    password_confirmation?: string;
    role: string;
    [key: string]: FormDataConvertible | undefined; // Index signature allowing undefined for optional fields
}

// Define the component props
export interface UserEditFormProps {
    user: UserData;
    data: UserEditFormData;
    setData: (key: keyof UserEditFormData | Partial<UserEditFormData>, value?: FormDataConvertible) => void;
    errors: Partial<Record<keyof UserEditFormData, string>>;
    processing: boolean;
    onSubmit: FormEventHandler<HTMLFormElement>;
    onClose: () => void;
}

export default function UserEditForm({ 
    user, 
    data, 
    setData, 
    errors, 
    processing, 
    onSubmit, 
    onClose 
}: UserEditFormProps) {
    // Remove internal useForm hook
    // const { data, setData, put, processing, errors, reset } = useForm({...});

    const roleOptions: ComboBoxOption[] = roles.map(r => ({ value: r, label: r }));

    // Highlight fields with errors
    const inputClass = (field: keyof typeof errors) => errors[field] ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : '';

    // Submit is now handled by the prop passed from parent
    // const submit: FormEventHandler = (e) => { ... };

    return (
        // Use onSubmit from props
        <form className="space-y-6" onSubmit={onSubmit}>
            {/* Fields using data, setData, errors from props */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="grid gap-2">
                    <Label htmlFor="name">Name</Label>
                    <Input id="name" value={data.name} onChange={e => setData('name', e.target.value)} required className={inputClass('name')} />
                    <InputError message={errors.name} />
                </div>
                <div className="grid gap-2">
                    <Label htmlFor="username">Username</Label>
                    <Input id="username" value={data.username} onChange={e => setData('username', e.target.value)} required className={inputClass('username')} />
                    <InputError message={errors.username} />
                </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="grid gap-2">
                    <Label htmlFor="email">Email</Label>
                    {/* Display original email, disable editing */}
                    <Input id="email" value={user.email} disabled /> 
                     {/* Use user.email here as email isn't part of the editable form data state passed in 'data' prop typically */}
                    {/* <InputError message={errors.email} /> */}
                     {/* Email is not submitted, so no error expected */} 
                </div>
                <div className="grid gap-2">
                    <Label htmlFor="role">Role</Label>
                    <ComboBox
                        options={roleOptions}
                        value={data.role} // Role is editable
                        onChange={val => setData('role', val)}
                        className={inputClass('role')}
                    />
                    <InputError message={errors.role} />
                </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="grid gap-2">
                    <Label htmlFor="password">Password</Label>
                    <Input id="password" type="password" value={data.password || ''} onChange={e => setData('password', e.target.value)} placeholder="Leave blank to keep unchanged" className={inputClass('password')} />
                    <InputError message={errors.password} />
                </div>
                <div className="grid gap-2">
                    <Label htmlFor="password_confirmation">Confirm Password</Label>
                    <Input id="password_confirmation" type="password" value={data.password_confirmation || ''} onChange={e => setData('password_confirmation', e.target.value)} placeholder="Leave blank to keep unchanged" className={inputClass('password_confirmation')} />
                    <InputError message={errors.password_confirmation} />
                </div>
            </div>
            <div className="flex justify-end gap-2">
                {/* Use onClose from props */} 
                <Button type="button" variant="outline" onClick={onClose} disabled={processing}>Cancel</Button>
                {/* Use processing from props */} 
                <Button type="submit" disabled={processing} className="relative">
                    {processing && <span className="absolute left-2 inline-block w-4 h-4 animate-spin border-2 border-t-transparent border-white rounded-full"></span>}
                    Update
                </Button>
            </div>
        </form>
    );
}
