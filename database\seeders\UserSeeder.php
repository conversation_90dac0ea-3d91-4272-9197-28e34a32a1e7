<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create ONLY the initial Admin User
        User::firstOrCreate(
            ['email' => '<EMAIL>'], // Find by email to avoid duplicates on re-seed
            [
                'name' => 'Arsalan Khan',
                'username' => 'admin', // Add a username
                'password' => Hash::make('test1234'), // Change this!
                'role' => 'Admin',
                'email_verified_at' => now(),
            ]
        );

        // Remove Editor and Viewer creation from seeder
        // User::create([...]); // Removed Editor
        // User::create([...]); // Removed Viewer
    }
}
