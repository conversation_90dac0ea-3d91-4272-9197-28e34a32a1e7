import * as React from "react"
import { useTheme } from "next-themes"
import { Toaster as Son<PERSON>, ToasterP<PERSON>, toast as sonnerToast, ExternalToast } from "sonner"
import { CheckCircle, XCircle, AlertCircle, Info, X } from "lucide-react"
import { cn } from "@/lib/utils"

type ToastPosition =
  | "top-left"
  | "top-center"
  | "top-right"
  | "bottom-left"
  | "bottom-center"
  | "bottom-right"

type ToastVariant = "default" | "success" | "error" | "warning" | "info"

interface ToastAction {
  label: string
  onClick: () => void
  variant?: "default" | "destructive"
}

interface CustomToastProps extends ExternalToast {
  variant?: ToastVariant
  title?: string
  description?: string
  action?: ToastAction
  cancel?: {
    label: string
    onClick?: () => void
  }
  richContent?: React.ReactNode
}

interface ToasterProps extends Omit<ToasterProps, 'position'> {
  position?: ToastPosition
  variant?: "default" | "rich"
  animation?: "slide" | "fade" | "scale"
}

const Toaster = ({
  position = "bottom-right",
  variant = "default",
  animation = "slide",
  ...props
}: ToasterProps) => {
  const { theme = "system" } = useTheme()

  const getAnimationClass = () => {
    switch (animation) {
      case "fade":
        return "data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-80 data-[state=open]:fade-in-0"
      case "scale":
        return "data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-80 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95"
      default:
        return "data-[state=open]:slide-in-from-bottom-full data-[state=closed]:slide-out-to-bottom-full"
    }
  }

  return (
    <Sonner
      theme={theme as ToasterProps["theme"]}
      position={position}
      className={cn(
        "toaster group",
        getAnimationClass(),
        variant === "rich" && "rich-toaster"
      )}
      style={
        {
          "--normal-bg": "var(--popover)",
          "--normal-text": "var(--popover-foreground)",
          "--normal-border": "var(--border)",
          "--success-bg": "var(--success)",
          "--success-text": "var(--success-foreground)",
          "--error-bg": "var(--destructive)",
          "--error-text": "var(--destructive-foreground)",
          "--warning-bg": "var(--warning)",
          "--warning-text": "var(--warning-foreground)",
          "--info-bg": "var(--info)",
          "--info-text": "var(--info-foreground)",
        } as React.CSSProperties
      }
      toastOptions={{
        className: cn(
          "group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",
          variant === "rich" && "group-[.rich-toaster]:min-h-[4rem] group-[.rich-toaster]:p-4"
        ),
        ...props.toastOptions,
      }}
      {...props}
    />
  )
}

// Custom toast component for rich content
const ToastContent = ({
  variant,
  title,
  description,
  action,
  cancel,
  richContent
}: CustomToastProps) => {
  const getIcon = () => {
    switch (variant) {
      case "success":
        return <CheckCircle className="h-5 w-5 text-green-600" />
      case "error":
        return <XCircle className="h-5 w-5 text-red-600" />
      case "warning":
        return <AlertCircle className="h-5 w-5 text-yellow-600" />
      case "info":
        return <Info className="h-5 w-5 text-blue-600" />
      default:
        return null
    }
  }

  if (richContent) {
    return <div className="w-full">{richContent}</div>
  }

  return (
    <div className="flex items-start gap-3 w-full">
      {getIcon()}
      <div className="flex-1 space-y-1">
        {title && <div className="font-semibold text-sm">{title}</div>}
        {description && <div className="text-sm text-muted-foreground">{description}</div>}
        {(action || cancel) && (
          <div className="flex items-center gap-2 mt-2">
            {action && (
              <button
                onClick={action.onClick}
                className={cn(
                  "px-3 py-1 text-xs font-medium rounded-md transition-colors",
                  action.variant === "destructive"
                    ? "bg-destructive text-destructive-foreground hover:bg-destructive/90"
                    : "bg-primary text-primary-foreground hover:bg-primary/90"
                )}
              >
                {action.label}
              </button>
            )}
            {cancel && (
              <button
                onClick={cancel.onClick}
                className="px-3 py-1 text-xs font-medium rounded-md transition-colors bg-secondary text-secondary-foreground hover:bg-secondary/80"
              >
                {cancel.label}
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  )
}

// Preset toast functions
const toast = {
  // Basic toast
  message: (message: string, options?: ExternalToast) => {
    return sonnerToast(message, options)
  },

  // Success toast
  success: (message: string | CustomToastProps, options?: ExternalToast) => {
    if (typeof message === "string") {
      return sonnerToast.success(message, {
        icon: <CheckCircle className="h-4 w-4" />,
        ...options,
      })
    }
    return sonnerToast(<ToastContent {...message} variant="success" />, {
      className: "border-green-200 bg-green-50 text-green-900 dark:border-green-800 dark:bg-green-950 dark:text-green-100",
      ...options,
    })
  },

  // Error toast
  error: (message: string | CustomToastProps, options?: ExternalToast) => {
    if (typeof message === "string") {
      return sonnerToast.error(message, {
        icon: <XCircle className="h-4 w-4" />,
        ...options,
      })
    }
    return sonnerToast(<ToastContent {...message} variant="error" />, {
      className: "border-red-200 bg-red-50 text-red-900 dark:border-red-800 dark:bg-red-950 dark:text-red-100",
      ...options,
    })
  },

  // Warning toast
  warning: (message: string | CustomToastProps, options?: ExternalToast) => {
    if (typeof message === "string") {
      return sonnerToast.warning(message, {
        icon: <AlertCircle className="h-4 w-4" />,
        ...options,
      })
    }
    return sonnerToast(<ToastContent {...message} variant="warning" />, {
      className: "border-yellow-200 bg-yellow-50 text-yellow-900 dark:border-yellow-800 dark:bg-yellow-950 dark:text-yellow-100",
      ...options,
    })
  },

  // Info toast
  info: (message: string | CustomToastProps, options?: ExternalToast) => {
    if (typeof message === "string") {
      return sonnerToast.info(message, {
        icon: <Info className="h-4 w-4" />,
        ...options,
      })
    }
    return sonnerToast(<ToastContent {...message} variant="info" />, {
      className: "border-blue-200 bg-blue-50 text-blue-900 dark:border-blue-800 dark:bg-blue-950 dark:text-blue-100",
      ...options,
    })
  },

  // Loading toast
  loading: (message: string, options?: ExternalToast) => {
    return sonnerToast.loading(message, options)
  },

  // Promise toast
  promise: sonnerToast.promise,

  // Custom toast with rich content
  custom: (content: React.ReactNode, options?: ExternalToast) => {
    return sonnerToast(content, options)
  },

  // Dismiss toast
  dismiss: sonnerToast.dismiss,
}

export {
  Toaster,
  toast,
  ToastContent,
  type ToastPosition,
  type ToastVariant,
  type ToastAction,
  type CustomToastProps,
  type ToasterProps as CustomToasterProps
}
