# Combined Laravel 12 Feature Research

This document combines information from several guides related to Laravel 12, React integration, MySQL optimization, and financial double accounting.

---

## Content from Comprehensive Guide: Laravel 12, React Integration, MySQL Optimization, and Financial Double Accounting

# Comprehensive Guide: Laravel 12, React Integration, MySQL Optimization, and Financial Double Accounting

This guide compiles extensive research on Laravel 12 features, React integration, MySQL optimization strategies, and financial double accounting packages for Laravel applications. It's designed to provide developers with a complete resource for building modern, efficient, and scalable financial applications with Laravel 12.

## Table of Contents

1. [Laravel 12 Features](#laravel-12-features)
   - [Code Simplification](#code-simplification)
   - [Routing Improvements](#routing-improvements)
   - [Performance Enhancements](#performance-enhancements)
   - [Database Optimizations](#database-optimizations)
   - [UI Improvements](#ui-improvements)

2. [Laravel 12 with React Integration](#laravel-12-with-react-integration)
   - [React Starter Kit Overview](#react-starter-kit-overview)
   - [Setup and Installation](#setup-and-installation)
   - [Inertia.js Integration](#inertiajs-integration)
   - [Optimistic UI Updates](#optimistic-ui-updates)
   - [Best Practices](#react-integration-best-practices)

3. [MySQL Optimization for Laravel Applications](#mysql-optimization-for-laravel-applications)
   - [Database Design Strategies](#database-design-strategies)
   - [Indexing Techniques](#indexing-techniques)
   - [Query Optimization](#query-optimization)
   - [MySQL Server Configuration](#mysql-server-configuration)
   - [Partitioning](#partitioning)
   - [Laravel-Specific Optimizations](#laravel-specific-optimizations)
   - [Monitoring and Profiling](#monitoring-and-profiling)
   - [Scaling Strategies](#scaling-strategies)

4. [Financial Double Accounting Packages](#financial-double-accounting-packages)
   - [Package Comparison](#package-comparison)
   - [Eloquent IFRS](#eloquent-ifrs)
   - [Laravel Ledger](#laravel-ledger)
   - [Scott Laurent's Accounting](#scott-laurents-accounting)
   - [Integration Strategies](#integration-strategies)
   - [Performance Optimization](#performance-optimization)

## Laravel 12 Features

Laravel 12, released on March 12, 2024, introduces significant improvements that enhance developer experience, application performance, and code simplicity.

### Code Simplification

#### Simplified Validation

Laravel 12 introduces a more concise validation syntax:

```php
// Laravel 12 approach
$validated = $request->validate([
    'name' => 'required|string|max:255',
    'email' => 'required|email|unique:users',
]);

// Previously you needed to handle validation manually
```

#### Invokable Route Controllers

Laravel 12 enhances invokable controllers, making single-action controllers more elegant:

```php
// routes/web.php
Route::get('/dashboard', DashboardController::class);

// app/Http/Controllers/DashboardController.php
class DashboardController
{
    public function __invoke(Request $request)
    {
        return view('dashboard');
    }
}
```

#### Improved Type Hinting

Laravel 12 leverages PHP 8.2+ features, offering better type hints and return types:

```php
public function store(StoreUserRequest $request): RedirectResponse
{
    $user = User::create($request->validated());

    return redirect()->route('users.show', $user);
}
```

### Routing Improvements

#### Route Groups with Attributes

Laravel 12 introduces a cleaner way to define route groups using PHP attributes:

```php
#[RouteGroup([
    'middleware' => ['auth', 'verified'],
    'prefix' => 'admin',
    'as' => 'admin.',
])]
class AdminRoutes
{
    #[Get('dashboard')]
    public function dashboard()
    {
        return view('admin.dashboard');
    }

    #[Post('users')]
    public function storeUser(StoreUserRequest $request)
    {
        // Create user
    }
}
```

#### Route Caching Improvements

Laravel 12 enhances route caching, significantly improving application bootstrap time:

```bash
# Generate optimized route cache
php artisan route:cache

# Clear route cache
php artisan route:clear
```

#### API Versioning Support

Laravel 12 simplifies API versioning with built-in support:

```php
// routes/api.php
Route::prefix('v1')->group(function () {
    Route::apiResource('users', Api\V1\UserController::class);
});

Route::prefix('v2')->group(function () {
    Route::apiResource('users', Api\V2\UserController::class);
});
```

### Performance Enhancements

#### Just-In-Time Compilation

Laravel 12 introduces JIT compilation for Blade templates, significantly reducing render times:

```bash
# Enable JIT compilation for Blade templates
php artisan view:cache
```

#### Lazy Collections

Laravel 12 enhances lazy collections for memory-efficient processing of large datasets:

```php
// Process millions of records without memory issues
User::cursor()->filter(function ($user) {
    return $user->active;
})->each(function ($user) {
    $user->sendNewsletterEmail();
});
```

#### Route Registration Optimization

Laravel 12 optimizes route registration process, enhancing application bootstrap time:

```php
// Blazing fast route registration
Route::get('/user/{id}', [UserController::class, 'show']);
```

### Database Optimizations

#### Optimized Eloquent Query Builder

Laravel 12 features an optimized query builder with reduced overhead:

```php
// More efficient query execution
$users = User::query()
    ->where('active', true)
    ->whereHas('subscriptions', function ($query) {
        $query->where('status', 'active');
    })
    ->with(['profile', 'roles'])
    ->paginate(20);
```

#### Enhanced Relationship Loading

Laravel 12 improves relationship loading with better caching and reduced queries:

```php
// More efficient eager loading
$posts = Post::with(['author', 'comments.author', 'tags'])
    ->latest()
    ->paginate(15);
```

#### Database Connection Pooling Support

Laravel 12 adds support for database connection pooling, improving handling of concurrent connections:

```php
// config/database.php
'mysql' => [
    // ...
    'pool' => [
        'enabled' => true,
        'min' => 5,
        'max' => 20,
    ],
],
```

### UI Improvements

#### Optimistic UI Updates

Laravel 12 with Inertia.js enables seamless optimistic UI updates:

```php
// Controller
public function update(Request $request, Post $post)
{
    $validated = $request->validate([
        'title' => 'required|string|max:255',
        'content' => 'required|string',
    ]);

    $post->update($validated);

    return back();
}
```

```javascript
// React component with optimistic updates
const updatePost = async (data) => {
    // Optimistically update the UI
    setPost({...post, ...data});

    // Perform actual update in the background
    await axios.put(`/posts/${post.id}`, data);
};
```

#### Improved Vite Integration

Laravel 12 features enhanced Vite.js integration for faster asset compilation:

```javascript
// vite.config.js
import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';
import react from '@vitejs/plugin-react';

export default defineConfig({
    plugins: [
        laravel({
            input: ['resources/css/app.css', 'resources/js/app.jsx'],
            refresh: true,
        }),
        react(),
    ],
});
```

## Laravel 12 with React Integration

Laravel 12 offers excellent React integration through its official starter kit and the powerful Inertia.js library.

### React Starter Kit Overview

Laravel's React Starter Kit provides a complete solution for building modern SPAs with Laravel backend and React frontend:

```bash
# Create a new Laravel 12 project with React
composer create-project laravel/laravel example-app
cd example-app
composer require laravel/breeze --dev
php artisan breeze:install react
```

The starter kit includes:
- React frontend with Vite
- Inertia.js for seamless frontend-backend integration
- Tailwind CSS for styling
- Authentication scaffolding
- API token management

### Setup and Installation

Setting up a Laravel 12 project with React integration:

```bash
# Install dependencies
npm install

# Start development servers
php artisan serve
npm run dev
```

Directory structure:
- `/resources/js` - React components and logic
- `/resources/js/Pages` - Page components for Inertia.js
- `/resources/js/Layouts` - Layout components
- `/resources/js/Components` - Reusable React components

### Inertia.js Integration

Inertia.js bridges Laravel and React, allowing server-side routing with client-side rendering:

```php
// Controller
public function index()
{
    return Inertia::render('Dashboard', [
        'stats' => [
            'users' => User::count(),
            'posts' => Post::count(),
        ],
    ]);
}
```

```jsx
// resources/js/Pages/Dashboard.jsx
import React from 'react';
import Layout from '@/Layouts/AuthenticatedLayout';

export default function Dashboard({ stats }) {
    return (
        <Layout>
            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div className="p-6 bg-white border-b border-gray-200">
                            <h2>Dashboard Stats</h2>
                            <p>Users: {stats.users}</p>
                            <p>Posts: {stats.posts}</p>
                        </div>
                    </div>
                </div>
            </div>
        </Layout>
    );
}
```

### Optimistic UI Updates

Implementing optimistic UI updates with Laravel 12 and React:

```jsx
// resources/js/Pages/Posts/Edit.jsx
import React, { useState } from 'react';
import { Inertia } from '@inertiajs/inertia';

export default function Edit({ post }) {
    const [formData, setFormData] = useState({
        title: post.title,
        content: post.content,
    });

    const [isSubmitting, setIsSubmitting] = useState(false);

    const handleSubmit = async (e) => {
        e.preventDefault();
        setIsSubmitting(true);

        // Optimistically show success state
        const originalPost = {...post};
        post.title = formData.title;
        post.content = formData.content;

        try {
            await Inertia.put(`/posts/${post.id}`, formData);
        } catch (error) {
            // Revert to original data if error occurs
            post.title = originalPost.title;
            post.content = originalPost.content;
            alert('Failed to update post.');
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <form onSubmit={handleSubmit}>
            {/* Form fields */}
            <button
                type="submit"
                disabled={isSubmitting}
            >
                {isSubmitting ? 'Updating...' : 'Update Post'}
            </button>
        </form>
    );
}
```

### React Integration Best Practices

1. **State Management**:
   - Use React Query for server state
   - Use Context API or Redux for complex application state
   - Leverage Inertia.js for shared state between server and client

```jsx
// Setting up React Query with Laravel
import { QueryClient, QueryClientProvider } from 'react-query';

const queryClient = new QueryClient({
    defaultOptions: {
        queries: {
            refetchOnWindowFocus: false,
            retry: 1,
        },
    },
});

export default function App({ Component, pageProps }) {
    return (
        <QueryClientProvider client={queryClient}>
            <Component {...pageProps} />
        </QueryClientProvider>
    );
}
```

2. **Component Structure**:
   - Create reusable UI components
   - Separate business logic from UI components
   - Implement container/presentational pattern

```jsx
// Presentational component
const UserCard = ({ user, onEdit }) => (
    <div className="user-card">
        <h3>{user.name}</h3>
        <p>{user.email}</p>
        <button onClick={() => onEdit(user)}>Edit</button>
    </div>
);

// Container component
const UserList = () => {
    const [users, setUsers] = useState([]);
    const [selectedUser, setSelectedUser] = useState(null);

    useEffect(() => {
        axios.get('/api/users').then(response => {
            setUsers(response.data);
        });
    }, []);

    const handleEdit = (user) => {
        setSelectedUser(user);
    };

    return (
        <div>
            {users.map(user => (
                <UserCard
                    key={user.id}
                    user={user}
                    onEdit={handleEdit}
                />
            ))}
            {selectedUser && <UserEditModal user={selectedUser} />}
        </div>
    );
};
```

3. **API Handling**:
   - Create dedicated API service files
   - Implement error handling and loading states
   - Use interceptors for authentication

```jsx
// api/users.js
import axios from 'axios';

export const getUsers = () => axios.get('/api/users');
export const getUser = (id) => axios.get(`/api/users/${id}`);
export const updateUser = (id, data) => axios.put(`/api/users/${id}`, data);
export const deleteUser = (id) => axios.delete(`/api/users/${id}`);

// Using the API service
import React, { useState, useEffect } from 'react';
import { getUsers } from '@/api/users';

export default function UsersList() {
    const [users, setUsers] = useState([]);
    useEffect(() => {
        getUsers().then(response => {
            setUsers(response.data);
        });
    }, []);

    return (
        <div>
            {users.map(user => (
                <div key={user.id}>{user.name}</div>
            ))}
        </div>
    );
}
```

## MySQL Optimization for Laravel Applications

This section provides detailed strategies for optimizing MySQL performance within Laravel applications, particularly relevant for handling large financial datasets.

### Database Design Strategies

#### Optimal Data Types

Choosing the right data types is crucial for minimizing storage space and improving query performance. Use the smallest possible integer types, `UNSIGNED` for non-negative numbers, fixed-length types for consistent data like currency amounts, and `DATE` instead of `DATETIME` when time information is not necessary.

```php
// Migration with optimized data types
Schema::create('transactions', function (Blueprint $table) {
    $table->id();
    // Use UNSIGNED for positive-only numbers (saves space)
    $table->unsignedBigInteger('user_id');
    // Use the smallest possible integer type that can hold your values
    $table->smallInteger('fiscal_year')->unsigned();
    // Use fixed-length when possible for financial data
    $table->decimal('amount', 13, 4); // 13 digits, 4 decimals
    // Use VARCHAR with realistic limits instead of TEXT for small strings
    $table->string('reference', 50);
    // Use INT for foreign keys instead of UUID/GUID for performance
    $table->foreignId('entity_id')->constrained();
    // Use TINYINT(1) for boolean flags
    $table->boolean('is_reconciled')->default(false);
    // Use DATE instead of DATETIME when time isn't needed
    $table->date('transaction_date');
    $table->timestamps();
});
```

#### Normalization vs. Denormalization

While normalization ensures data integrity, denormalization can improve read performance for frequently accessed summary data. Consider creating denormalized summary tables for reports or dashboards.

```php
// Denormalized account balances table
Schema::create('account_balances', function (Blueprint $table) {
    $table->id();
    $table->foreignId('account_id')->constrained();
    $table->date('balance_date');
    $table->decimal('debit_total', 13, 4)->default(0);
    $table->decimal('credit_total', 13, 4)->default(0);
    $table->decimal('balance', 13, 4)->default(0);
    $table->timestamps();

    // Composite index for fast lookups
    $table->unique(['account_id', 'balance_date']);
});
```

### Indexing Strategies

Effective indexing is fundamental to fast database queries.

#### Strategic Column Indexing

Index columns frequently used in `WHERE`, `JOIN`, `ORDER BY`, and `GROUP BY` clauses.

```php
// Add indexes for commonly queried columns
Schema::table('transaction_entries', function (Blueprint $table) {
    // For filtering transactions by account
    $table->index('account_id');

    // For filtering by transaction type
    $table->index('is_credit');

    // Composite index for reports that filter by account and type
    $table->index(['account_id', 'is_credit']);
});

Schema::table('transactions', function (Blueprint $table) {
    // For date range queries
    $table->index('transaction_date');

    // For searching by reference number
    $table->index('reference');
});
```

#### Composite Indexes

Create composite indexes for queries involving multiple columns in the `WHERE` clause, following the order of columns in the query.

```php
// Composite index for common financial reporting query
Schema::table('transaction_entries', function (Blueprint $table) {
    // For queries that filter by account and transaction date
    $table->index(['account_id', 'transaction_id']);
});

Schema::table('transactions', function (Blueprint $table) {
    // For filtering transactions by entity and date range
    $table->index(['entity_id', 'transaction_date']);
});
```

#### Foreign Key Indexes

Always index foreign key columns to optimize join operations.

```php
Schema::table('transaction_entries', function (Blueprint $table) {
    $table->foreign('transaction_id')
          ->references('id')
          ->on('transactions')
          ->onDelete('cascade');

    $table->foreign('account_id')
          ->references('id')
          ->on('accounts');

    // Ensure foreign keys are indexed
    $table->index('transaction_id');
    $table->index('account_id');
});
```

#### Analyze Index Usage

Regularly analyze index usage to identify and remove unused or inefficient indexes.

```sql
-- Find unused indexes
SELECT
    t.TABLE_SCHEMA,
    t.TABLE_NAME,
    ix.INDEX_NAME,
    ix.COLUMN_NAME
FROM
    information_schema.STATISTICS ix
LEFT JOIN
    mysql.schema_index_statistics s
    ON ix.TABLE_SCHEMA = s.table_schema
    AND ix.TABLE_NAME = s.table_name
    AND ix.INDEX_NAME = s.index_name
WHERE
    s.rows_read IS NULL OR s.rows_read = 0
    AND t.TABLE_SCHEMA = 'your_database';
```

### Query Optimization

Writing efficient queries is paramount for performance.

#### Efficient Eloquent Queries

Avoid the N+1 query problem by using eager loading (`with`, `load`) for relationships.

```php
// GOOD: Efficient query with eager loading
$transactions = Transaction::with(['entries', 'entries.account'])
    ->where('entity_id', $entityId)
    ->whereBetween('transaction_date', [$startDate, $endDate])
    ->get();
```

#### Select Only Required Columns

Only select the columns you need using the `select()` method to reduce the amount of data transferred.

```php
// GOOD: Select only what you need
$accounts = Account::select('id', 'code', 'name', 'type')->get();
```

#### Raw Queries for Complex Operations

For complex aggregations, calculations, or operations that are difficult to express with the query builder, consider using raw SQL queries.

```php
// Using raw expressions for complex calculations
$accountBalances = DB::table('transaction_entries as e')
    ->join('transactions as t', 'e.transaction_id', '=', 't.id')
    ->select('e.account_id')
    ->selectRaw('SUM(CASE WHEN e.is_credit = 1 THEN e.amount ELSE 0 END) as credit_total')
    ->selectRaw('SUM(CASE WHEN e.is_credit = 0 THEN e.amount ELSE 0 END) as debit_total')
    ->whereRaw('t.transaction_date <= ?', [$balanceDate])
    ->groupBy('e.account_id')
    ->get();
```

#### Chunking for Large Datasets

When processing large datasets, use `chunk()` or `chunkById()` to retrieve and process data in smaller batches, preventing memory exhaustion.

```php
// Process millions of transactions efficiently
Transaction::where('fiscal_year', 2023)
    ->chunkById(1000, function ($transactions) {
        foreach ($transactions as $transaction) {
            // Process each transaction without memory issues
            ProcessTransaction::dispatch($transaction);
        }
    });
```

#### Query Caching

Cache the results of expensive queries that are frequently accessed but do not change often using Laravel's caching system.

```php
// Cache financial report queries
$monthEndReport = Cache::remember('month_end_report_' . $month, now()->addDays(1), function () use ($month) {
    return DB::table('transaction_entries as e')
        ->join('transactions as t', 'e.transaction_id', '=', 't.id')
        ->join('accounts as a', 'e.account_id', '=', 'a.id')
        ->select('a.id', 'a.name', 'a.type')
        ->selectRaw('SUM(CASE WHEN e.is_credit = 1 THEN e.amount ELSE 0 END) as credit_total')
        ->selectRaw('SUM(CASE WHEN e.is_credit = 0 THEN e.amount ELSE 0 END) as debit_total')
        ->whereRaw('t.transaction_date BETWEEN ? AND ?', [
            Carbon::parse($month)->startOfMonth(),
            Carbon::parse($month)->endOfMonth(),
        ])
        ->groupBy('a.id', 'a.name', 'a.type')
        ->get();
});
```

#### View Materialization

For complex and frequently run reporting queries, consider creating materialized views (or summary tables) that are periodically refreshed.

```php
// Create a scheduled task to refresh the materialized view
$schedule->call(function () {
    DB::statement('TRUNCATE account_balances_materialized');

    DB::statement("
        INSERT INTO account_balances_materialized (account_id, debit_total, credit_total, balance, as_of_date)
        SELECT
            e.account_id,
            SUM(CASE WHEN e.is_credit = 0 THEN e.amount ELSE 0 END) as debit_total,
            SUM(CASE WHEN e.is_credit = 1 THEN e.amount ELSE 0 END) as credit_total,
            SUM(CASE WHEN e.is_credit = 0 THEN e.amount ELSE -e.amount END) as balance,
            CURRENT_DATE() as as_of_date
        FROM transaction_entries e
        JOIN transactions t ON e.transaction_id = t.id
        WHERE t.transaction_date <= CURRENT_DATE()
        GROUP BY e.account_id
    ");
})->daily();
```

### MySQL Server Configuration

Optimizing the MySQL server configuration can significantly impact performance.

#### InnoDB Settings

Tune InnoDB settings based on your workload. Key parameters include `innodb_buffer_pool_size` (allocate 70-80% of RAM), `innodb_log_file_size`, `innodb_flush_log_at_trx_commit`, and `max_connections`.

```ini
# my.cnf optimized for financial data
[mysqld]
# InnoDB Buffer Pool (allocate 70-80% of available RAM)
innodb_buffer_pool_size = 4G

# For large transaction processing
innodb_log_file_size = 512M
innodb_log_buffer_size = 16M

# For write-heavy financial data
innodb_flush_log_at_trx_commit = 2

# Increase number of concurrent connections for high traffic
max_connections = 300

# Increase temp table size for complex joins and sorts
tmp_table_size = 64M
max_heap_table_size = 64M

# Disable query cache (better to use application-level caching)
query_cache_type = 0
query_cache_size = 0

# File-per-table for better management
innodb_file_per_table = 1

# For complex joins in financial reporting
join_buffer_size = 4M
sort_buffer_size = 4M

# Optimize IO for high-performance storage
innodb_flush_method = O_DIRECT

# Optimize for financial transaction processing
innodb_doublewrite = 1
innodb_thread_concurrency = 0
innodb_read_io_threads = 8
innodb_write_io_threads = 8
```

#### Connection Pooling

Implement database connection pooling in your Laravel application to manage connections efficiently, reducing the overhead of establishing new connections for each request.

```php
// config/database.php
'mysql' => [
    'driver' => 'mysql',
    'host' => env('DB_HOST', '127.0.0.1'),
    'port' => env('DB_PORT', '3306'),
    'database' => env('DB_DATABASE', 'forge'),
    'username' => env('DB_USERNAME', 'forge'),
    'password' => env('DB_PASSWORD', ''),
    'unix_socket' => env('DB_SOCKET', ''),
    'charset' => 'utf8mb4',
    'collation' => 'utf8mb4_unicode_ci',
    'prefix' => '',
    'prefix_indexes' => true,
    'strict' => true,
    'engine' => null,
    'options' => extension_loaded('pdo_mysql') ? array_filter([
        PDO::MYSQL_ATTR_SSL_CA => env('MYSQL_ATTR_SSL_CA'),
    ]) : [],

    // Connection pooling for production
    'pool' => [
        'enabled' => env('DB_POOL', false),
        'min' => env('DB_POOL_MIN', 5),
        'max' => env('DB_POOL_MAX', 20),
    ],
],
```

### Partitioning

For very large tables, partitioning can improve performance by dividing the data into smaller, more manageable chunks.

#### Table Partitioning by Date

Partition transaction tables by date range to improve performance for time-based queries.

```sql
-- Partition transactions table by date
ALTER TABLE transactions PARTITION BY RANGE (TO_DAYS(transaction_date)) (
    PARTITION p2022_q1 VALUES LESS THAN (TO_DAYS('2022-04-01')),
    PARTITION p2022_q2 VALUES LESS THAN (TO_DAYS('2022-07-01')),
    PARTITION p2022_q3 VALUES LESS THAN (TO_DAYS('2022-10-01')),
    PARTITION p2022_q4 VALUES LESS THAN (TO_DAYS('2023-01-01')),
    PARTITION p2023_q1 VALUES LESS THAN (TO_DAYS('2023-04-01')),
    PARTITION p2023_q2 VALUES LESS THAN (TO_DAYS('2023-07-01')),
    PARTITION p2023_q3 VALUES LESS THAN (TO_DAYS('2023-10-01')),
    PARTITION p2023_q4 VALUES LESS THAN (TO_DAYS('2024-01-01')),
    PARTITION p2024_q1 VALUES LESS THAN (TO_DAYS('2024-04-01')),
    PARTITION p2024_q2 VALUES LESS THAN (TO_DAYS('2024-07-01')),
    PARTITION p2024_q3 VALUES LESS THAN (TO_DAYS('2024-10-01')),
    PARTITION p2024_q4 VALUES LESS THAN (TO_DAYS('2025-01-01')),
    PARTITION future VALUES LESS THAN MAXVALUE
);
```

#### Partitioning by Entity ID

In multi-tenant applications, partitioning by entity ID can improve performance by allowing queries to only scan relevant partitions.

```sql
-- Partition by entity ID for multi-tenant systems
ALTER TABLE transaction_entries PARTITION BY HASH(account_id) PARTITIONS 8;
```

#### Partition Maintenance

Implement a strategy for maintaining partitions, such as adding new partitions for future data and archiving or dropping old partitions.

```php
// Create a command to manage partitions
namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ManageTransactionPartitions extends Command
{
    protected $signature = 'db:manage-partitions';
    protected $description = 'Add new quarterly partitions for transactions table';

    public function handle()
    {
        // Get the latest quarter currently partitioned
        $result = DB::select("
            SELECT partition_description
            FROM information_schema.partitions
            WHERE table_name = 'transactions'
            AND partition_name != 'future'
            ORDER BY partition_ordinal_position DESC
            LIMIT 1
        ");

        if (empty($result)) {
            $this->error('Could not determine last partition');
            return 1;
        }

        $lastPartitionValue = $result[0]->partition_description;
        $lastDate = Carbon::createFromFormat('Y-m-d', substr($lastPartitionValue, 9, 10));

        // Create next 4 quarters if they don't exist
        $nextQuarter = $lastDate->copy()->addMonths(3);

        for ($i = 0; $i < 4; $i++) {
            $partitionDate = $nextQuarter->copy()->addMonths($i * 3);
            $partitionName = 'p' . $partitionDate->format('Y_q') . $partitionDate->quarter;
            $boundaryDate = $partitionDate->format('Y-m-d');

            $this->info("Adding partition {$partitionName} for boundary {$boundaryDate}");

            DB::statement("
                ALTER TABLE transactions REORGANIZE PARTITION future INTO (
                    PARTITION {$partitionName} VALUES LESS THAN (TO_DAYS('{$boundaryDate}')),
                    PARTITION future VALUES LESS THAN MAXVALUE
                )
            ");
        }

        $this->info('Partition management completed');
        return 0;
    }
}
```

### Laravel-Specific Optimizations

Leverage Laravel's features to optimize database interactions.

#### Database Configuration

Adjust Laravel's database configuration for performance, such as disabling strict mode in certain scenarios or enabling emulated prepared statements and buffered queries if appropriate for your setup.

```php
// config/database.php
'mysql' => [
    // ...
    'strict' => false, // Disable strict mode for better performance in some cases
    'options' => [
        PDO::ATTR_EMULATE_PREPARES => true, // Enable emulated prepared statements
        PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true, // Use buffered queries
    ],
],
```

#### Query Builder Optimizations

Utilize optimized query builder methods and techniques like `when()` for conditional clauses and `tap()` for debugging.

```php
// Using when for conditional clauses
$query = Transaction::query();

if ($request->has('date_range')) {
    $query->whereBetween('transaction_date', [$request->start_date, $request->end_date]);
}

$transactions = $query->get();
```

#### Eager Loading Strategy

Implement a consistent eager loading strategy across your application to avoid the N+1 problem.

```php
// Define relationships in models
class Transaction extends Model
{
    public function entries()
    {
        return $this->hasMany(TransactionEntry::class);
    }
}

class TransactionEntry extends Model
{
    public function account()
    {
        return $this->belongsTo(Account::class);
    }
}

// Eager load relationships
$transactions = Transaction::with('entries.account')->get();
```

#### Laravel Data Caching

Beyond query caching, cache computed data, report results, or frequently accessed configuration settings using Laravel's caching drivers (Redis, Memcached).

```php
// Cache a complex report result
$reportData = Cache::remember('financial_summary', $minutes = 60, function () {
    return $this->generateFinancialSummary();
});
```

### Monitoring and Profiling

Regularly monitor and profile your database queries to identify performance bottlenecks.

#### Query Logging

Enable MySQL's slow query log to identify queries that are taking a long time to execute.

```ini
# my.cnf
[mysqld]
slow_query_log = 1
slow_query_log_file = /var/log/mysql/mysql-slow.log
long_query_time = 1 # Log queries taking longer than 1 second
log_queries_not_using_indexes = 1 # Log queries that don't use indexes
```

#### Database Profiling

Use MySQL's `EXPLAIN` statement to analyze the execution plan of individual queries and identify areas for optimization.

```sql
EXPLAIN SELECT * FROM transactions WHERE transaction_date BETWEEN '2023-01-01' AND '2023-12-31';
```

#### Laravel Telescope

Utilize Laravel Telescope in your development environment to monitor database queries, execution times, and other performance metrics.

```bash
# Install Telescope
composer require laravel/telescope --dev

# Publish assets
php artisan telescope:install

# Run migrations
php artisan migrate
```

### Scaling Strategies

As your financial application grows, consider scaling strategies.

#### Read-Write Splitting

Implement read-write splitting to direct read operations to read replicas and write operations to the primary database, distributing the load.

```php
// config/database.php
'mysql' => [
    'read' => [
        'host' => [
            '***********',
            '***********',
        ],
    ],
    'write' => [
        'host' => [
            '***********',
        ],
    ],
    'sticky' => true,
    // ... other mysql config
],
```

#### Sharding for Multi-Tenant

For very large multi-tenant applications, consider sharding the database based on tenant ID to distribute data across multiple database servers.

#### Archiving Old Data

Implement a strategy for archiving or purging old financial data that is no longer actively used to keep the active dataset smaller and improve query performance.

## Conclusion

Optimizing MySQL performance in Laravel financial applications is an ongoing process that involves careful database design, strategic indexing, efficient query writing, server configuration tuning, and leveraging Laravel-specific features. By implementing the techniques outlined in this guide, you can build scalable and responsive financial applications capable of handling large volumes of data.

---

## Content from Laravel 12 Financial Double Accounting Application Implementation Guide

# Laravel 12 Financial Double Accounting Application Implementation Guide

This practical guide walks through implementing a financial double accounting system using Laravel 12, React, and MySQL optimization techniques.

## Getting Started

### Project Setup

```bash
# Create a new Laravel 12 project
composer create-project laravel/laravel financial-accounting-app

# Navigate to project directory
cd financial-accounting-app

# Install React starter kit
composer require laravel/breeze --dev
php artisan breeze:install react

# Install financial accounting package (using Eloquent IFRS as example)
composer require ekmungai/eloquent-ifrs

# Publish package assets
php artisan vendor:publish --provider="Ekmungai\IFRS\IFRSServiceProvider"

# Run migrations
php artisan migrate

# Install NPM dependencies
npm install

# Add React Query for efficient data fetching
npm install @tanstack/react-query
```

### Database Configuration

Optimize your MySQL configuration for financial data in `.env`:

```
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=financial_app
DB_USERNAME=root
DB_PASSWORD=

# Connection pooling for production
DB_POOL=true
DB_POOL_MIN=5
DB_POOL_MAX=20
```

Update `config/database.php` for production:

```php
'mysql' => [
    // ...existing config
    'sticky' => true,
    'pool' => [
        'enabled' => env('DB_POOL', false),
        'min' => env('DB_POOL_MIN', 2),
        'max' => env('DB_POOL_MAX', 10),
    ],
],
```

## Database Schema Design

Create optimized migrations for financial data:

```php
// Create accounting_entities table
Schema::create('accounting_entities', function (Blueprint $table) {
    $table->id();
    $table->string('name');
    $table->timestamps();
    $table->index('name');
});

// Create chart_of_accounts table
Schema::create('chart_of_accounts', function (Blueprint $table) {
    $table->id();
    $table->foreignId('entity_id')->constrained('accounting_entities');
    $table->string('name');
    $table->timestamps();
    $table->index(['entity_id', 'name']);
});

// Create accounts table with optimized data types
Schema::create('accounts', function (Blueprint $table) {
    $table->id();
    $table->foreignId('chart_id')->constrained('chart_of_accounts');
    $table->string('code', 20);
    $table->string('name');
    $table->string('type', 20); // asset, liability, equity, revenue, expense
    $table->foreignId('parent_id')->nullable()->constrained('accounts');
    $table->timestamps();

    // Add indexes for frequent queries
    $table->index('code');
    $table->index(['chart_id', 'type']);
    $table->index(['chart_id', 'parent_id']);
});

// Create journals table with date partitioning
Schema::create('journals', function (Blueprint $table) {
    $table->id();
    $table->foreignId('entity_id')->constrained('accounting_entities');
    $table->string('reference', 50);
    $table->string('description');
    $table->date('journal_date');
    $table->timestamps();

    // Add indexes for reporting queries
    $table->index('reference');
    $table->index(['entity_id', 'journal_date']);
});

// Create journal_entries table with optimal indexing
Schema::create('journal_entries', function (Blueprint $table) {
    $table->id();
    $table->foreignId('journal_id')->constrained()->onDelete('cascade');
    $table->foreignId('account_id')->constrained('accounts');
    $table->decimal('amount', 13, 4);
    $table->boolean('is_credit')->default(false);
    $table->timestamps();

    // Add indexes for balance calculations
    $table->index(['account_id', 'is_credit']);
    $table->index(['journal_id', 'account_id']);
});
```

## Model Definitions

Create optimized Eloquent models:

```php
// app/Models/AccountingEntity.php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class AccountingEntity extends Model
{
    protected $fillable = ['name'];

    public function chartOfAccounts()
    {
        return $this->hasOne(ChartOfAccounts::class, 'entity_id');
    }

    public function journals()
    {
        return $this->hasMany(Journal::class, 'entity_id');
    }
}

// app/Models/ChartOfAccounts.php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ChartOfAccounts extends Model
{
    protected $fillable = ['entity_id', 'name'];

    public function entity()
    {
        return $this->belongsTo(AccountingEntity::class, 'entity_id');
    }

    public function accounts()
    {
        return $this->hasMany(Account::class, 'chart_id');
    }
}

// app/Models/Account.php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;

class Account extends Model
{
    protected $fillable = ['chart_id', 'code', 'name', 'type', 'parent_id'];

    public function chart()
    {
        return $this->belongsTo(ChartOfAccounts::class, 'chart_id');
    }

    public function parent()
    {
        return $this->belongsTo(Account::class, 'parent_id');
    }

    public function children()
    {
        return $this->hasMany(Account::class, 'parent_id');
    }

    public function entries()
    {
        return $this->hasMany(JournalEntry::class, 'account_id');
    }

    // Efficiently calculate balance
    public function balance($startDate = null, $endDate = null)
    {
        $query = $this->entries();

        if ($startDate) {
            $query->whereHas('journal', function ($q) use ($startDate) {
                $q->where('journal_date', '>=', $startDate);
            });
        }

        if ($endDate) {
            $query->whereHas('journal', function ($q) use ($endDate) {
                $q->where('journal_date', '<=', $endDate);
            });
        }

        $credits = (clone $query)->where('is_credit', true)->sum('amount');
        $debits = (clone $query)->where('is_credit', false)->sum('amount');

        // Calculate based on account type
        if (in_array($this->type, ['asset', 'expense'])) {
            return $debits - $credits;
        } else {
            return $credits - $debits;
        }
    }

    // Eager load balance for multiple accounts efficiently
    public static function withBalances($chartId, $date = null)
    {
        return static::where('chart_id', $chartId)
            ->withSum(['entries as credits' => function ($query) use ($date) {
                $query->where('is_credit', true);
                if ($date) {
                    $query->whereHas('journal', function ($q) use ($date) {
                        $q->where('journal_date', '<=', $date);
                    });
                }
            }], 'amount')
            ->withSum(['entries as debits' => function ($query) use ($date) {
                $query->where('is_credit', false);
                if ($date) {
                    $query->whereHas('journal', function ($q) use ($date) {
                        $q->where('journal_date', '<=', $date);
                    });
                }
            }], 'amount');
    }
}

// app/Models/Journal.php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class Journal extends Model
{
    protected $fillable = ['entity_id', 'reference', 'description', 'journal_date'];

    public function entity()
    {
        return $this->belongsTo(AccountingEntity::class, 'entity_id');
    }

    public function entries()
    {
        return $this->hasMany(JournalEntry::class);
    }

    // Create a balanced journal entry
    public function addEntries(array $entries)
    {
        // Validate that entries balance
        $total = 0;
        foreach ($entries as $entry) {
            $amount = $entry['amount'];
            if (!empty($entry['is_credit'])) {
                $total += $amount;
            } else {
                $total -= $amount;
            }
        }

        if (abs($total) > 0.0001) {
            throw new \Exception("Journal entries must balance. Current imbalance: {$total}");
        }

        // Create entries in a transaction
        return DB::transaction(function () use ($entries) {
            foreach ($entries as $entry) {
                $this->entries()->create($entry);
            }
            return $this;
        });
    }

    // Helper to post a balanced transaction
    public static function postTransaction($entityId, $reference, $description, $date, array $entries)
    {
        return DB::transaction(function () use ($entityId, $reference, $description, $date, $entries) {
            $journal = static::create([
                'entity_id' => $entityId,
                'reference' => $reference,
                'description' => $description,
                'journal_date' => $date,
            ]);

            $journal->addEntries($entries);

            return $journal;
        });
    }
}

// app/Models/JournalEntry.php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class JournalEntry extends Model
{
    protected $fillable = ['journal_id', 'account_id', 'amount', 'is_credit'];

    public function journal()
    {
        return $this->belongsTo(Journal::class);
    }

    public function account()
    {
        return $this->belongsTo(Account::class);
    }
}
```

## Service Layer

Create a service layer to encapsulate accounting logic:

```php
// app/Services/AccountingService.php
namespace App\Services;

use App\Models\Account;
use App\Models\Journal;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;

class AccountingService
{
    /**
     * Create a new transaction with balanced entries
     */
    public function createTransaction(int $entityId, string $reference, string $description,
                                     string $date, array $entries)
    {
        return Journal::postTransaction(
            $entityId,
            $reference,
            $description,
            Carbon::parse($date),
            $entries
        );
    }

    /**
     * Get account balance with caching for performance
     */
    public function getAccountBalance(int $accountId, ?string $date = null)
    {
        $cacheKey = "account_balance_{$accountId}_" . ($date ?? 'all');

        return Cache::remember($cacheKey, now()->addHours(1), function () use ($accountId, $date) {
            $account = Account::findOrFail($accountId);
            return $account->balance($date ? null : Carbon::parse($date));
        });
    }

    /**
     * Generate a trial balance
     */
    public function generateTrialBalance(int $entityId, ?string $date = null)
    {
        $cacheKey = "trial_balance_{$entityId}_" . ($date ?? 'current');
        $cacheTime = now()->addMinutes(30);

        return Cache::remember($cacheKey, $cacheTime, function () use ($entityId, $date) {
            $entity = AccountingEntity::findOrFail($entityId);
            $chartId = $entity->chartOfAccounts->id;

            $accounts = Account::withBalances($chartId, $date ? Carbon::parse($date) : null)
                ->get()
                ->map(function ($account) {
                    // Calculate balance based on account type
                    if (in_array($account->type, ['asset', 'expense'])) {
                        $balance = $account->debits - $account->credits;
                    } else {
                        $balance = $account->credits - $account->debits;
                    }

                    return [
                        'id' => $account->id,
                        'code' => $account->code,
                        'name' => $account->name,
                        'type' => $account->type,
                        'balance' => $balance,
                    ];
                });

            return [
                'date' => $date ?? now()->toDateString(),
                'entity' => $entity->name,
                'accounts' => $accounts,
                'total_debits' => $accounts->where('balance', '>', 0)->sum('balance'),
                'total_credits' => abs($accounts->where('balance', '<', 0)->sum('balance')),
            ];
        });
    }

    /**
     * Generate an income statement
     */
    public function generateIncomeStatement(int $entityId, string $startDate, string $endDate)
    {
        $cacheKey = "income_statement_{$entityId}_{$startDate}_{$endDate}";
        $cacheTime = now()->addMinutes(30);

        return Cache::remember($cacheKey, $cacheTime, function () use ($entityId, $startDate, $endDate) {
            $entity = AccountingEntity::findOrFail($entityId);
            $chartId = $entity->chartOfAccounts->id;

            $start = Carbon::parse($startDate);
            $end = Carbon::parse($endDate);

            $revenueAccounts = Account::where('chart_id', $chartId)
                ->where('type', 'revenue')
                ->get()
                ->map(function ($account) use ($start, $end) {
                    return [
                        'id' => $account->id,
                        'code' => $account->code,
                        'name' => $account->name,
                        'balance' => $account->balance($start, $end),
                    ];
                });

            $expenseAccounts = Account::where('chart_id', $chartId)
                ->where('type', 'expense')
                ->get()
                ->map(function ($account) use ($start, $end) {
                    return [
                        'id' => $account->id,
                        'code' => $account->code,
                        'name' => $account->name,
                        'balance' => $account->balance($start, $end),
                    ];
                });

            $totalRevenue = $revenueAccounts->sum('balance');
            $totalExpenses = $expenseAccounts->sum('balance');
            $netIncome = $totalRevenue - $totalExpenses;

            return [
                'start_date' => $startDate,
                'end_date' => $endDate,
                'entity' => $entity->name,
                'revenue_accounts' => $revenueAccounts,
                'expense_accounts' => $expenseAccounts,
                'total_revenue' => $totalRevenue,
                'total_expenses' => $totalExpenses,
                'net_income' => $netIncome,
            ];
        });
    }

    /**
     * Generate a balance sheet
     */
    public function generateBalanceSheet(int $entityId, ?string $date = null)
    {
        $cacheKey = "balance_sheet_{$entityId}_" . ($date ?? 'current');
        $cacheTime = now()->addMinutes(30);

        return Cache::remember($cacheKey, $cacheTime, function () use ($entityId, $date) {
            $entity = AccountingEntity::findOrFail($entityId);
            $chartId = $entity->chartOfAccounts->id;
            $balanceDate = $date ? Carbon::parse($date) : now();

            $assetAccounts = Account::where('chart_id', $chartId)
                ->where('type', 'asset')
                ->get()
                ->map(function ($account) use ($balanceDate) {
                    return [
                        'id' => $account->id,
                        'code' => $account->code,
                        'name' => $account->name,
                        'balance' => $account->balance(null, $balanceDate),
                    ];
                });

            $liabilityAccounts = Account::where('chart_id', $chartId)
                ->where('type', 'liability')
                ->get()
                ->map(function ($account) use ($balanceDate) {
                    return [
                        'id' => $account->id,
                        'code' => $account->code,
                        'name' => $account->name,
                        'balance' => $account->balance(null, $balanceDate),
                    ];
                });

            $equityAccounts = Account::where('chart_id', $chartId)
                ->where('type', 'equity')
                ->get()
                ->map(function ($account) use ($balanceDate) {
                    return [
                        'id' => $account->id,
                        'code' => $account->code,
                        'name' => $account->name,
                        'balance' => $account->balance(null, $balanceDate),
                    ];
                });

            $totalAssets = $assetAccounts->sum('balance');
            $totalLiabilities = $liabilityAccounts->sum('balance');
            $totalEquity = $equityAccounts->sum('balance');

            return [
                'date' => $date ?? now()->toDateString(),
                'entity' => $entity->name,
                'asset_accounts' => $assetAccounts,
                'liability_accounts' => $liabilityAccounts,
                'equity_accounts' => $equityAccounts,
                'total_assets' => $totalAssets,
                'total_liabilities' => $totalLiabilities,
                'total_equity' => $totalEquity,
                'is_balanced' => abs($totalAssets - ($totalLiabilities + $totalEquity)) < 0.0001,
            ];
        });
    }
}
```

## API Controllers

Create API controllers to expose accounting functionality:

```php
// app/Http/Controllers/Api/AccountingController.php
namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\AccountingService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class AccountingController extends Controller
{
    protected $accountingService;

    public function __construct(AccountingService $accountingService)
    {
        $this->accountingService = $accountingService;
    }

    /**
     * Post a new transaction
     */
    public function postTransaction(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'entity_id' => 'required|exists:accounting_entities,id',
            'reference' => 'required|string|max:50',
            'description' => 'required|string',
            'date' => 'required|date',
            'entries' => 'required|array|min:2',
            'entries.*.account_id' => 'required|exists:accounts,id',
            'entries.*.amount' => 'required|numeric|min:0.01',
            'entries.*.is_credit' => 'required|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $journal = $this->accountingService->createTransaction(
                $request->entity_id,
                $request->reference,
                $request->description,
                $request->date,
                $request->entries
            );

            return response()->json(['message' => 'Transaction posted successfully', 'journal' => $journal], 201);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 400);
        }
    }

    /**
     * Get trial balance
     */
    public function getTrialBalance(Request $request, int $entityId)
    {
        $date = $request->query('date');
        $trialBalance = $this->accountingService->generateTrialBalance($entityId, $date);

        return response()->json($trialBalance);
    }

    /**
     * Get income statement
     */
    public function getIncomeStatement(Request $request, int $entityId)
    {
        $validator = Validator::make($request->all(), [
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $incomeStatement = $this->accountingService->generateIncomeStatement(
            $entityId,
            $request->start_date,
            $request->end_date
        );

        return response()->json($incomeStatement);
    }

    /**
     * Get balance sheet
     */
    public function getBalanceSheet(Request $request, int $entityId)
    {
        $date = $request->query('date');
        $balanceSheet = $this->accountingService->generateBalanceSheet($entityId, $date);

        return response()->json($balanceSheet);
    }
}
```

## React Frontend with Optimistic UI

Implement the frontend using React with Optimistic UI for a responsive experience.

```jsx
// resources/js/Pages/Accounting/PostTransaction.jsx
import React, { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import axios from 'axios';

const postTransaction = async (data) => {
    const response = await axios.post('/api/accounting/transactions', data);
    return response.data;
};

export default function PostTransaction({ entityId, accounts }) {
    const queryClient = useQueryClient();
    const [formData, setFormData] = useState({
        entity_id: entityId,
        reference: '',
        description: '',
        date: '',
        entries: [{ account_id: '', amount: '', is_credit: false }, { account_id: '', amount: '', is_credit: true }],
    });

    const mutation = useMutation({
        mutationFn: postTransaction,
        onMutate: async (newTransaction) => {
            // Optimistic update (optional, depending on complexity)
            // This is more complex for double-entry, might skip for simplicity
        },
        onSuccess: () => {
            // Invalidate relevant queries to refetch data
            queryClient.invalidateQueries(['trialBalance', entityId]);
            queryClient.invalidateQueries(['balanceSheet', entityId]);
            // Optionally reset form
            setFormData({
                entity_id: entityId,
                reference: '',
                description: '',
                date: '',
                entries: [{ account_id: '', amount: '', is_credit: false }, { account_id: '', amount: '', is_credit: true }],
            });
            alert('Transaction posted successfully!');
        },
        onError: (error) => {
            alert('Error posting transaction: ' + (error.response?.data?.error || error.message));
        },
    });

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData({ ...formData, [name]: value });
    };

    const handleEntryChange = (index, e) => {
        const { name, value, type, checked } = e.target;
        const newEntries = [...formData.entries];
        newEntries[index][name] = type === 'checkbox' ? checked : value;
        setFormData({ ...formData, entries: newEntries });
    };

    const addEntry = () => {
        setFormData({
            ...formData,
            entries: [...formData.entries, { account_id: '', amount: '', is_credit: false }],
        });
    };

    const removeEntry = (index) => {
        const newEntries = [...formData.entries];
        newEntries.splice(index, 1);
        setFormData({ ...formData, entries: newEntries });
    };

    const handleSubmit = (e) => {
        e.preventDefault();
        mutation.mutate(formData);
    };

    return (
        <form onSubmit={handleSubmit}>
            <div>
                <label>Reference:</label>
                <input type="text" name="reference" value={formData.reference} onChange={handleInputChange} required />
            </div>
            <div>
                <label>Description:</label>
                <input type="text" name="description" value={formData.description} onChange={handleInputChange} required />
            </div>
            <div>
                <label>Date:</label>
                <input type="date" name="date" value={formData.date} onChange={handleInputChange} required />
            </div>

            <h3>Entries</h3>
            {formData.entries.map((entry, index) => (
                <div key={index}>
                    <select name="account_id" value={entry.account_id} onChange={(e) => handleEntryChange(index, e)} required>
                        <option value="">Select Account</option>
                        {accounts.map(account => (
                            <option key={account.id} value={account.id}>{account.name} ({account.code})</option>
                        ))}
                    </select>
                    <input type="number" name="amount" value={entry.amount} onChange={(e) => handleEntryChange(index, e)} step="0.01" required />
                    <label>
                        Credit:
                        <input type="checkbox" name="is_credit" checked={entry.is_credit} onChange={(e) => handleEntryChange(index, e)} />
                    </label>
                    {formData.entries.length > 2 && (
                        <button type="button" onClick={() => removeEntry(index)}>Remove</button>
                    )}
                </div>
            ))}
            <button type="button" onClick={addEntry}>Add Entry</button>

            <button type="submit" disabled={mutation.isLoading}>
                {mutation.isLoading ? 'Posting...' : 'Post Transaction'}
            </button>
        </form>
    );
}
```

```jsx
// resources/js/Pages/Accounting/TrialBalance.jsx
import React from 'react';
import { useQuery } from '@tanstack/react-query';
import axios from 'axios';

const fetchTrialBalance = async ({ queryKey }) => {
    const [_, entityId, date] = queryKey;
    const response = await axios.get(`/api/accounting/entities/${entityId}/trial-balance`, {
        params: { date }
    });
    return response.data;
};

export default function TrialBalance({ entityId }) {
    const [date, setDate] = React.useState(''); // State for date filter

    const { data, isLoading, isError, error } = useQuery({
        queryKey: ['trialBalance', entityId, date],
        queryFn: fetchTrialBalance,
        keepPreviousData: true, // Keep previous data while fetching new
    });

    if (isLoading) return <div>Loading Trial Balance...</div>;
    if (isError) return <div>Error loading Trial Balance: {error.message}</div>;

    return (
        <div>
            <h2>Trial Balance for {data.entity}</h2>
            <div>
                <label>As of Date:</label>
                <input type="date" value={date} onChange={(e) => setDate(e.target.value)} />
            </div>
            <table>
                <thead>
                    <tr>
                        <th>Code</th>
                        <th>Account Name</th>
                        <th>Type</th>
                        <th>Balance</th>
                    </tr>
                </thead>
                <tbody>
                    {data.accounts.map(account => (
                        <tr key={account.id}>
                            <td>{account.code}</td>
                            <td>{account.name}</td>
                            <td>{account.type}</td>
                            <td>{account.balance.toFixed(2)}</td>
                        </tr>
                    ))}
                </tbody>
                <tfoot>
                    <tr>
                        <td colSpan="3">Total Debits</td>
                        <td>{data.total_debits.toFixed(2)}</td>
                    </tr>
                    <tr>
                        <td colSpan="3">Total Credits</td>
                        <td>{data.total_credits.toFixed(2)}</td>
                    </tr>
                </tfoot>
            </table>
        </div>
    );
}
```

## MySQL Optimization Strategies

Refer to the dedicated MySQL Optimization guide for detailed techniques on database design, indexing, query optimization, server configuration, and scaling.

## Optimized Background Processing

For tasks like generating large reports or processing bulk transactions, use Laravel Queues and Workers to offload the work to the background, preventing timeouts and improving application responsiveness.

```php
// app/Jobs/GenerateFinancialReport.php
namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Services\AccountingService;
use App\Models\User; // Example: to notify user

class GenerateFinancialReport implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $reportType;
    protected $entityId;
    protected $params;
    protected $userId;

    /**
     * Create a new job instance.
     */
    public function __construct(string $reportType, int $entityId, array $params, int $userId)
    {
        $this->reportType = $reportType;
        $this->entityId = $entityId;
        $this->params = $params;
        $this->userId = $userId;
    }

    /**
     * Execute the job.
     */
    public function handle(AccountingService $accountingService): void
    {
        $reportData = [];
        switch ($this->reportType) {
            case 'trial_balance':
                $reportData = $accountingService->generateTrialBalance($this->entityId, $this->params['date'] ?? null);
                break;
            case 'income_statement':
                $reportData = $accountingService->generateIncomeStatement($this->entityId, $this->params['start_date'], $this->params['end_date']);
                break;
            case 'balance_sheet':
                $reportData = $accountingService->generateBalanceSheet($this->entityId, $this->params['date'] ?? null);
                break;
            // Add other report types
        }

        // Process or store the reportData
        // For example, save to a file or database, and notify the user

        $user = User::find($this->userId);
        if ($user) {
            // Notify user that report is ready
            // $user->notify(new ReportGeneratedNotification($this->reportType, $reportData));
        }
    }
}
```

```bash
# Dispatch the job
php artisan queue:work
```

## Deployment Considerations

*   **Caching**: Ensure caching is properly configured and utilized in production (`config:cache`, `route:cache`, `view:cache`).
*   **Database**: Optimize MySQL server configuration (`my.cnf`) for production workload. Consider using a managed database service.
*   **Queues**: Set up a robust queue worker process (e.g., Supervisor) to handle background jobs. Use a production-ready queue driver (Redis, Beanstalkd, SQS).
*   **Assets**: Ensure frontend assets are built and optimized for production (`npm run build`).
*   **Environment**: Configure production environment variables correctly (`.env`).

```bash
# Optimize Laravel for production
php artisan optimize

# Build frontend assets for production
npm run build

# Start queue worker (example using Supervisor)
# [program:laravel-worker]
# process_name=%(program_name)s_%(process_num)02d
# command=php /var/www/html/artisan queue:work redis --sleep=3 --tries=3 --max-time=3600
# autostart=true
# autorestart=true
# stopasgroup=true
# killafter=5000
# numprocs=8
# redirect_stderr=true
# stdout_logfile=/var/www/html/worker.log
# stopwaitsecs=3600
```

## Monitoring and Optimization

Continuously monitor application and database performance.

*   **Laravel Telescope**: Use in development/staging for insights.
*   **MySQL Slow Query Log**: Identify slow queries in production.
*   **APM Tools**: Integrate Application Performance Monitoring tools (e.g., New Relic, Sentry) for production monitoring.
*   **Database Monitoring**: Use tools like Prometheus/Grafana or cloud provider monitoring for database metrics.

## Conclusion

Implementing a financial double accounting system in Laravel 12 with React and optimized MySQL requires careful planning and attention to detail across the full stack. By leveraging Laravel's features, integrating frontend best practices, and applying robust database optimization techniques, you can build a performant and scalable application capable of handling complex financial data.

---

## Content from MySQL Optimization Techniques for Laravel Financial Applications

# MySQL Optimization Techniques for Laravel Financial Applications

This guide focuses specifically on MySQL optimization techniques for Laravel financial applications that handle large volumes of transaction data.

## Table of Contents
1. [Database Design Optimization](#database-design-optimization)
2. [Indexing Strategies](#indexing-strategies)
3. [Query Optimization](#query-optimization)
4. [MySQL Server Configuration](#mysql-server-configuration)
5. [Partitioning](#partitioning)
6. [Laravel-Specific Optimizations](#laravel-specific-optimizations)
7. [Monitoring and Profiling](#monitoring-and-profiling)
8. [Scaling Strategies](#scaling-strategies)

## Database Design Optimization

### Optimal Data Types

Choose the most efficient data types to minimize storage and improve performance:

```php
// Migration with optimized data types
Schema::create('transactions', function (Blueprint $table) {
    $table->id();
    // Use UNSIGNED for positive-only numbers (saves space)
    $table->unsignedBigInteger('user_id');
    // Use the smallest possible integer type that can hold your values
    $table->smallInteger('fiscal_year')->unsigned();
    // Use fixed-length when possible for financial data
    $table->decimal('amount', 13, 4); // 13 digits, 4 decimals
    // Use VARCHAR with realistic limits instead of TEXT for small strings
    $table->string('reference', 50);
    // Use INT for foreign keys instead of UUID/GUID for performance
    $table->foreignId('entity_id')->constrained();
    // Use TINYINT(1) for boolean flags
    $table->boolean('is_reconciled')->default(false);
    // Use DATE instead of DATETIME when time isn't needed
    $table->date('transaction_date');
    $table->timestamps();
});
```

### Normalization vs. Denormalization

For financial systems, proper normalization is crucial for data integrity but can affect query performance:

```php
// Normalized schema example
Schema::create('accounts', function (Blueprint $table) {
    $table->id();
    $table->foreignId('chart_id')->constrained();
    $table->string('code', 20);
    $table->string('name');
    $table->string('type', 20);
    $table->timestamps();
});

Schema::create('transactions', function (Blueprint $table) {
    $table->id();
    $table->string('reference', 50);
    $table->text('description');
    $table->date('transaction_date');
    $table->timestamps();
});

Schema::create('transaction_entries', function (Blueprint $table) {
    $table->id();
    $table->foreignId('transaction_id')->constrained()->onDelete('cascade');
    $table->foreignId('account_id')->constrained();
    $table->decimal('amount', 13, 4);
    $table->boolean('is_credit')->default(false);
    $table->timestamps();
});
```

### Denormalized Summary Tables

For frequently accessed summary data, consider denormalized summary tables:

```php
// Denormalized account balances table
Schema::create('account_balances', function (Blueprint $table) {
    $table->id();
    $table->foreignId('account_id')->constrained();
    $table->date('balance_date');
    $table->decimal('debit_total', 13, 4)->default(0);
    $table->decimal('credit_total', 13, 4)->default(0);
    $table->decimal('balance', 13, 4)->default(0);
    $table->timestamps();

    // Composite index for fast lookups
    $table->unique(['account_id', 'balance_date']);
});
```

## Indexing Strategies

### Strategic Column Indexing

Identify and index columns used in WHERE, JOIN, ORDER BY, and GROUP BY clauses:

```php
// Add indexes for commonly queried columns
Schema::table('transaction_entries', function (Blueprint $table) {
    // For filtering transactions by account
    $table->index('account_id');

    // For filtering by transaction type
    $table->index('is_credit');

    // Composite index for reports that filter by account and type
    $table->index(['account_id', 'is_credit']);
});

Schema::table('transactions', function (Blueprint $table) {
    // For date range queries
    $table->index('transaction_date');

    // For searching by reference number
    $table->index('reference');
});
```

### Composite Indexes

Create composite indexes for multi-column conditions, following the order of use:

```php
// Composite index for common financial reporting query
Schema::table('transaction_entries', function (Blueprint $table) {
    // For queries that filter by account and transaction date
    $table->index(['account_id', 'transaction_id']);
});

Schema::table('transactions', function (Blueprint $table) {
    // For filtering transactions by entity and date range
    $table->index(['entity_id', 'transaction_date']);
});
```

### Foreign Key Indexes

Always index foreign key columns to speed up joins:

```php
Schema::table('transaction_entries', function (Blueprint $table) {
    $table->foreign('transaction_id')
          ->references('id')
          ->on('transactions')
          ->onDelete('cascade');

    $table->foreign('account_id')
          ->references('id')
          ->on('accounts');

    // Ensure foreign keys are indexed
    $table->index('transaction_id');
    $table->index('account_id');
});
```

### Analyze Index Usage

Regularly analyze index usage to identify unused or inefficient indexes:

```sql
-- Find unused indexes
SELECT
    t.TABLE_SCHEMA,
    t.TABLE_NAME,
    ix.INDEX_NAME,
    ix.COLUMN_NAME
FROM
    information_schema.STATISTICS ix
LEFT JOIN
    mysql.schema_index_statistics s
    ON ix.TABLE_SCHEMA = s.table_schema
    AND ix.TABLE_NAME = s.table_name
    AND ix.INDEX_NAME = s.index_name
WHERE
    s.rows_read IS NULL OR s.rows_read = 0
    AND t.TABLE_SCHEMA = 'your_database';
```

## Query Optimization

### Efficient Eloquent Queries

Optimize your Laravel Eloquent queries for maximum performance:

```php
// BAD: Inefficient query with N+1 problem
$transactions = Transaction::where('entity_id', $entityId)
    ->whereBetween('transaction_date', [$startDate, $endDate])
    ->get();

foreach ($transactions as $transaction) {
    // This causes N additional queries
    $entries = $transaction->entries;
    foreach ($entries as $entry) {
        // This causes N*M additional queries
        $account = $entry->account;
    }
}

// GOOD: Efficient query with eager loading
$transactions = Transaction::with(['entries', 'entries.account'])
    ->where('entity_id', $entityId)
    ->whereBetween('transaction_date', [$startDate, $endDate])
    ->get();
```

### Select Only Required Columns

Specify only the columns you need instead of selecting everything:

```php
// BAD: Selecting all columns
$accounts = Account::all();

// GOOD: Select only what you need
$accounts = Account::select('id', 'code', 'name', 'type')->get();
```

### Raw Queries for Complex Operations

Use raw queries for complex aggregations and operations:

```php
// Using raw expressions for complex calculations
$accountBalances = DB::table('transaction_entries as e')
    ->join('transactions as t', 'e.transaction_id', '=', 't.id')
    ->select('e.account_id')
    ->selectRaw('SUM(CASE WHEN e.is_credit = 1 THEN e.amount ELSE 0 END) as credit_total')
    ->selectRaw('SUM(CASE WHEN e.is_credit = 0 THEN e.amount ELSE 0 END) as debit_total')
    ->whereRaw('t.transaction_date <= ?', [$balanceDate])
    ->groupBy('e.account_id')
    ->get();
```

### Chunking for Large Datasets

Process large data sets in chunks to avoid memory issues:

```php
// Process millions of transactions efficiently
Transaction::where('fiscal_year', 2023)
    ->chunkById(1000, function ($transactions) {
        foreach ($transactions as $transaction) {
            // Process each transaction without memory issues
            ProcessTransaction::dispatch($transaction);
        }
    });
```

### Query Caching

Cache expensive queries that don't change frequently:

```php
// Cache financial report queries
$monthEndReport = Cache::remember('month_end_report_' . $month, now()->addDays(1), function () use ($month) {
    return DB::table('transaction_entries as e')
        ->join('transactions as t', 'e.transaction_id', '=', 't.id')
        ->join('accounts as a', 'e.account_id', '=', 'a.id')
        ->select('a.id', 'a.name', 'a.type')
        ->selectRaw('SUM(CASE WHEN e.is_credit = 1 THEN e.amount ELSE 0 END) as credit_total')
        ->selectRaw('SUM(CASE WHEN e.is_credit = 0 THEN e.amount ELSE 0 END) as debit_total')
        ->whereRaw('t.transaction_date BETWEEN ? AND ?', [
            Carbon::parse($month)->startOfMonth(),
            Carbon::parse($month)->endOfMonth(),
        ])
        ->groupBy('a.id', 'a.name', 'a.type')
        ->get();
});
```

### View Materialization

Create materialized views for complex reporting queries:

```php
// Create a scheduled task to refresh the materialized view
$schedule->call(function () {
    DB::statement('TRUNCATE account_balances_materialized');

    DB::statement("
        INSERT INTO account_balances_materialized (account_id, debit_total, credit_total, balance, as_of_date)
        SELECT
            e.account_id,
            SUM(CASE WHEN e.is_credit = 0 THEN e.amount ELSE 0 END) as debit_total,
            SUM(CASE WHEN e.is_credit = 1 THEN e.amount ELSE 0 END) as credit_total,
            SUM(CASE WHEN e.is_credit = 0 THEN e.amount ELSE -e.amount END) as balance,
            CURRENT_DATE() as as_of_date
        FROM transaction_entries e
        JOIN transactions t ON e.transaction_id = t.id
        WHERE t.transaction_date <= CURRENT_DATE()
        GROUP BY e.account_id
    ");
})->daily();
```

## MySQL Server Configuration

### InnoDB Settings

Optimize InnoDB settings for financial data workloads:

```ini
# my.cnf optimized for financial data
[mysqld]
# InnoDB Buffer Pool (allocate 70-80% of available RAM)
innodb_buffer_pool_size = 4G

# For large transaction processing
innodb_log_file_size = 512M
innodb_log_buffer_size = 16M

# For write-heavy financial data
innodb_flush_log_at_trx_commit = 2

# Increase number of concurrent connections for high traffic
max_connections = 300

# Increase temp table size for complex joins and sorts
tmp_table_size = 64M
max_heap_table_size = 64M

# Disable query cache (better to use application-level caching)
query_cache_type = 0
query_cache_size = 0

# File-per-table for better management
innodb_file_per_table = 1

# For complex joins in financial reporting
join_buffer_size = 4M
sort_buffer_size = 4M

# Optimize IO for high-performance storage
innodb_flush_method = O_DIRECT

# Optimize for financial transaction processing
innodb_doublewrite = 1
innodb_thread_concurrency = 0
innodb_read_io_threads = 8
innodb_write_io_threads = 8
```

### Connection Pooling

Implement connection pooling for better resource utilization:

```php
// config/database.php
'mysql' => [
    'driver' => 'mysql',
    'host' => env('DB_HOST', '127.0.0.1'),
    'port' => env('DB_PORT', '3306'),
    'database' => env('DB_DATABASE', 'forge'),
    'username' => env('DB_USERNAME', 'forge'),
    'password' => env('DB_PASSWORD', ''),
    'unix_socket' => env('DB_SOCKET', ''),
    'charset' => 'utf8mb4',
    'collation' => 'utf8mb4_unicode_ci',
    'prefix' => '',
    'prefix_indexes' => true,
    'strict' => true,
    'engine' => null,
    'options' => extension_loaded('pdo_mysql') ? array_filter([
        PDO::MYSQL_ATTR_SSL_CA => env('MYSQL_ATTR_SSL_CA'),
    ]) : [],

    // Connection pooling for production
    'pool' => [
        'enabled' => env('DB_POOL', false),
        'min' => env('DB_POOL_MIN', 5),
        'max' => env('DB_POOL_MAX', 20),
    ],
],
```

## Partitioning

### Table Partitioning by Date

Partition large transaction tables by date for better performance:

```sql
-- Partition transactions table by date
ALTER TABLE transactions PARTITION BY RANGE (TO_DAYS(transaction_date)) (
    PARTITION p2022_q1 VALUES LESS THAN (TO_DAYS('2022-04-01')),
    PARTITION p2022_q2 VALUES LESS THAN (TO_DAYS('2022-07-01')),
    PARTITION p2022_q3 VALUES LESS THAN (TO_DAYS('2022-10-01')),
    PARTITION p2022_q4 VALUES LESS THAN (TO_DAYS('2023-01-01')),
    PARTITION p2023_q1 VALUES LESS THAN (TO_DAYS('2023-04-01')),
    PARTITION p2023_q2 VALUES LESS THAN (TO_DAYS('2023-07-01')),
    PARTITION p2023_q3 VALUES LESS THAN (TO_DAYS('2023-10-01')),
    PARTITION p2023_q4 VALUES LESS THAN (TO_DAYS('2024-01-01')),
    PARTITION p2024_q1 VALUES LESS THAN (TO_DAYS('2024-04-01')),
    PARTITION p2024_q2 VALUES LESS THAN (TO_DAYS('2024-07-01')),
    PARTITION p2024_q3 VALUES LESS THAN (TO_DAYS('2024-10-01')),
    PARTITION p2024_q4 VALUES LESS THAN (TO_DAYS('2025-01-01')),
    PARTITION future VALUES LESS THAN MAXVALUE
);
```

### Partitioning by Entity ID

For multi-tenant financial systems, consider partitioning by entity ID:

```sql
-- Partition by entity ID for multi-tenant systems
ALTER TABLE transaction_entries PARTITION BY HASH(account_id) PARTITIONS 8;
```

### Partition Maintenance

Implement partition maintenance for long-term performance:

```php
// Create a command to manage partitions
namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ManageTransactionPartitions extends Command
{
    protected $signature = 'db:manage-partitions';
    protected $description = 'Add new quarterly partitions for transactions table';

    public function handle()
    {
        // Get the latest quarter currently partitioned
        $result = DB::select("
            SELECT partition_description
            FROM information_schema.partitions
            WHERE table_name = 'transactions'
            AND partition_name != 'future'
            ORDER BY partition_ordinal_position DESC
            LIMIT 1
        ");

        if (empty($result)) {
            $this->error('Could not determine last partition');
            return 1;
        }

        $lastPartitionValue = $result[0]->partition_description;
        $lastDate = Carbon::createFromFormat('Y-m-d', substr($lastPartitionValue, 9, 10));

        // Create next 4 quarters if they don't exist
        $nextQuarter = $lastDate->copy()->addMonths(3);

        for ($i = 0; $i < 4; $i++) {
            $partitionDate = $nextQuarter->copy()->addMonths($i * 3);
            $partitionName = 'p' . $partitionDate->format('Y_q') . $partitionDate->quarter;
            $boundaryDate = $partitionDate->format('Y-m-d');

            $this->info("Adding partition {$partitionName} for boundary {$boundaryDate}");

            DB::statement("
                ALTER TABLE transactions REORGANIZE PARTITION future INTO (
                    PARTITION {$partitionName} VALUES LESS THAN (TO_DAYS('{$boundaryDate}')),
                    PARTITION future VALUES LESS THAN MAXVALUE
                )
            ");
        }

        $this->info('Partition management completed');
        return 0;
    }
}
```

## Laravel-Specific Optimizations

### Database Configuration

Optimize Laravel's database configuration:

```php
// config/database.php
'mysql' => [
    // ...
    'strict' => false, // Disable strict mode for better performance in some cases
    'options' => [
        PDO::ATTR_EMULATE_PREPARES => true, // Enable emulated prepared statements
        PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true, // Use buffered queries
    ],
],
```

### Query Builder Optimizations

Use optimized query builder methods:

```php
// Using when for conditional clauses
$query = Transaction::query();

if ($request->has('date_range')) {
    $query->whereBetween('transaction_date', [$request->start_date, $request->end_date]);
}

$transactions = $query->get();
```

### Eager Loading Strategy

Implement a consistent eager loading strategy:

```php
// Define relationships in models
class Transaction extends Model
{
    public function entries()
    {
        return $this->hasMany(TransactionEntry::class);
    }
}

class TransactionEntry extends Model
{
    public function account()
    {
        return $this->belongsTo(Account::class);
    }
}

// Eager load relationships
$transactions = Transaction::with('entries.account')->get();
```

### Laravel Data Caching

Cache computed data and report results:

```php
// Cache a complex report result
$reportData = Cache::remember('financial_summary', $minutes = 60, function () {
    return $this->generateFinancialSummary();
});
```

## Monitoring and Profiling

### Query Logging

Enable MySQL's slow query log:

```ini
# my.cnf
[mysqld]
slow_query_log = 1
slow_query_log_file = /var/log/mysql/mysql-slow.log
long_query_time = 1 # Log queries taking longer than 1 second
log_queries_not_using_indexes = 1 # Log queries that don't use indexes
```

### Database Profiling

Use MySQL's `EXPLAIN` statement:

```sql
EXPLAIN SELECT * FROM transactions WHERE transaction_date BETWEEN '2023-01-01' AND '2023-12-31';
```

### Laravel Telescope

Utilize Laravel Telescope:

```bash
# Install Telescope
composer require laravel/telescope --dev

# Publish assets
php artisan telescope:install

# Run migrations
php artisan migrate
```

## Scaling Strategies

### Read-Write Splitting

Implement read-write splitting:

```php
// config/database.php
'mysql' => [
    'read' => [
        'host' => [
            '***********',
            '***********',
        ],
    ],
    'write' => [
        'host' => [
            '***********',
        ],
    ],
    'sticky' => true,
    // ... other mysql config
],
```

### Sharding for Multi-Tenant

Consider sharding for very large multi-tenant applications.

### Archiving Old Data

Implement a strategy for archiving or purging old data.

## Conclusion

Optimizing MySQL performance in Laravel financial applications is an ongoing process that involves careful database design, strategic indexing, efficient query writing, server configuration tuning, and leveraging Laravel-specific features. By implementing the techniques outlined in this guide, you can build scalable and responsive financial applications capable of handling large volumes of data.

---

## Content from Laravel 12 Financial Double Accounting Application Implementation Guide

# Laravel 12 Financial Double Accounting Application Implementation Guide

This practical guide walks through implementing a financial double accounting system using Laravel 12, React, and MySQL optimization techniques.

## Getting Started

### Project Setup

```bash
# Create a new Laravel 12 project
composer create-project laravel/laravel financial-accounting-app

# Navigate to project directory
cd financial-accounting-app

# Install React starter kit
composer require laravel/breeze --dev
php artisan breeze:install react

# Install financial accounting package (using Eloquent IFRS as example)
composer require ekmungai/eloquent-ifrs

# Publish package assets
php artisan vendor:publish --provider="Ekmungai\IFRS\IFRSServiceProvider"

# Run migrations
php artisan migrate

# Install NPM dependencies
npm install

# Add React Query for efficient data fetching
npm install @tanstack/react-query
```

### Database Configuration

Optimize your MySQL configuration for financial data in `.env`:

```
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=financial_app
DB_USERNAME=root
DB_PASSWORD=

# Connection pooling for production
DB_POOL=true
DB_POOL_MIN=5
DB_POOL_MAX=20
```

Update `config/database.php` for production:

```php
'mysql' => [
    // ...existing config
    'sticky' => true,
    'pool' => [
        'enabled' => env('DB_POOL', false),
        'min' => env('DB_POOL_MIN', 2),
        'max' => env('DB_POOL_MAX', 10),
    ],
],
```

## Database Schema Design

Create optimized migrations for financial data:

```php
// Create accounting_entities table
Schema::create('accounting_entities', function (Blueprint $table) {
    $table->id();
    $table->string('name');
    $table->timestamps();
    $table->index('name');
});

// Create chart_of_accounts table
Schema::create('chart_of_accounts', function (Blueprint $table) {
    $table->id();
    $table->foreignId('entity_id')->constrained('accounting_entities');
    $table->string('name');
    $table->timestamps();
    $table->index(['entity_id', 'name']);
});

// Create accounts table with optimized data types
Schema::create('accounts', function (Blueprint $table) {
    $table->id();
    $table->foreignId('chart_id')->constrained('chart_of_accounts');
    $table->string('code', 20);
    $table->string('name');
    $table->string('type', 20); // asset, liability, equity, revenue, expense
    $table->foreignId('parent_id')->nullable()->constrained('accounts');
    $table->timestamps();

    // Add indexes for frequent queries
    $table->index('code');
    $table->index(['chart_id', 'type']);
    $table->index(['chart_id', 'parent_id']);
});

// Create journals table with date partitioning
Schema::create('journals', function (Blueprint $table) {
    $table->id();
    $table->foreignId('entity_id')->constrained('accounting_entities');
    $table->string('reference', 50);
    $table->string('description');
    $table->date('journal_date');
    $table->timestamps();

    // Add indexes for reporting queries
    $table->index('reference');
    $table->index(['entity_id', 'journal_date']);
});

// Create journal_entries table with optimal indexing
Schema::create('journal_entries', function (Blueprint $table) {
    $table->id();
    $table->foreignId('journal_id')->constrained()->onDelete('cascade');
    $table->foreignId('account_id')->constrained('accounts');
    $table->decimal('amount', 13, 4);
    $table->boolean('is_credit')->default(false);
    $table->timestamps();

    // Add indexes for balance calculations
    $table->index(['account_id', 'is_credit']);
    $table->index(['journal_id', 'account_id']);
});
```

## Model Definitions

Create optimized Eloquent models:

```php
// app/Models/AccountingEntity.php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class AccountingEntity extends Model
{
    protected $fillable = ['name'];

    public function chartOfAccounts()
    {
        return $this->hasOne(ChartOfAccounts::class, 'entity_id');
    }

    public function journals()
    {
        return $this->hasMany(Journal::class, 'entity_id');
    }
}

// app/Models/ChartOfAccounts.php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ChartOfAccounts extends Model
{
    protected $fillable = ['entity_id', 'name'];

    public function entity()
    {
        return $this->belongsTo(AccountingEntity::class, 'entity_id');
    }

    public function accounts()
    {
        return $this->hasMany(Account::class, 'chart_id');
    }
}

// app/Models/Account.php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;

class Account extends Model
{
    protected $fillable = ['chart_id', 'code', 'name', 'type', 'parent_id'];

    public function chart()
    {
        return $this->belongsTo(ChartOfAccounts::class, 'chart_id');
    }

    public function parent()
    {
        return $this->belongsTo(Account::class, 'parent_id');
    }

    public function children()
    {
        return $this->hasMany(Account::class, 'parent_id');
    }

    public function entries()
    {
        return $this->hasMany(JournalEntry::class, 'account_id');
    }

    // Efficiently calculate balance
    public function balance($startDate = null, $endDate = null)
    {
        $query = $this->entries();

        if ($startDate) {
            $query->whereHas('journal', function ($q) use ($startDate) {
                $q->where('journal_date', '>=', $startDate);
            });
        }

        if ($endDate) {
            $query->whereHas('journal', function ($q) use ($endDate) {
                $q->where('journal_date', '<=', $endDate);
            });
        }

        $credits = (clone $query)->where('is_credit', true)->sum('amount');
        $debits = (clone $query)->where('is_credit', false)->sum('amount');

        // Calculate based on account type
        if (in_array($this->type, ['asset', 'expense'])) {
            return $debits - $credits;
        } else {
            return $credits - $debits;
        }
    }

    // Eager load balance for multiple accounts efficiently
    public static function withBalances($chartId, $date = null)
    {
        return static::where('chart_id', $chartId)
            ->withSum(['entries as credits' => function ($query) use ($date) {
                $query->where('is_credit', true);
                if ($date) {
                    $query->whereHas('journal', function ($q) use ($date) {
                        $q->where('journal_date', '<=', $date);
                    });
                }
            }], 'amount')
            ->withSum(['entries as debits' => function ($query) use ($date) {
                $query->where('is_credit', false);
                if ($date) {
                    $query->whereHas('journal', function ($q) use ($date) {
                        $q->where('journal_date', '<=', $date);
                    });
                }
            }], 'amount');
    }
}

// app/Models/Journal.php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class Journal extends Model
{
    protected $fillable = ['entity_id', 'reference', 'description', 'journal_date'];

    public function entity()
    {
        return $this->belongsTo(AccountingEntity::class, 'entity_id');
    }

    public function entries()
    {
        return $this->hasMany(JournalEntry::class);
    }

    // Create a balanced journal entry
    public function addEntries(array $entries)
    {
        // Validate that entries balance
        $total = 0;
        foreach ($entries as $entry) {
            $amount = $entry['amount'];
            if (!empty($entry['is_credit'])) {
                $total += $amount;
            } else {
                $total -= $amount;
            }
        }

        if (abs($total) > 0.0001) {
            throw new \Exception("Journal entries must balance. Current imbalance: {$total}");
        }

        // Create entries in a transaction
        return DB::transaction(function () use ($entries) {
            foreach ($entries as $entry) {
                $this->entries()->create($entry);
            }
            return $this;
        });
    }

    // Helper to post a balanced transaction
    public static function postTransaction($entityId, $reference, $description, $date, array $entries)
    {
        return DB::transaction(function () use ($entityId, $reference, $description, $date, $entries) {
            $journal = static::create([
                'entity_id' => $entityId,
                'reference' => $reference,
                'description' => $description,
                'journal_date' => $date,
            ]);

            $journal->addEntries($entries);

            return $journal;
        });
    }
}

// app/Models/JournalEntry.php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class JournalEntry extends Model
{
    protected $fillable = ['journal_id', 'account_id', 'amount', 'is_credit'];

    public function journal()
    {
        return $this->belongsTo(Journal::class);
    }

    public function account()
    {
        return $this->belongsTo(Account::class);
    }
}
```

## Service Layer

Create a service layer to encapsulate accounting logic:

```php
// app/Services/AccountingService.php
namespace App\Services;

use App\Models\Account;
use App\Models\Journal;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;

class AccountingService
{
    /**
     * Create a new transaction with balanced entries
     */
    public function createTransaction(int $entityId, string $reference, string $description,
                                     string $date, array $entries)
    {
        return Journal::postTransaction(
            $entityId,
            $reference,
            $description,
            Carbon::parse($date),
            $entries
        );
    }

    /**
     * Get account balance with caching for performance
     */
    public function getAccountBalance(int $accountId, ?string $date = null)
    {
        $cacheKey = "account_balance_{$accountId}_" . ($date ?? 'all');

        return Cache::remember($cacheKey, now()->addHours(1), function () use ($accountId, $date) {
            $account = Account::findOrFail($accountId);
            return $account->balance($date ? null : Carbon::parse($date));
        });
    }

    /**
     * Generate a trial balance
     */
    public function generateTrialBalance(int $entityId, ?string $date = null)
    {
        $cacheKey = "trial_balance_{$entityId}_" . ($date ?? 'current');
        $cacheTime = now()->addMinutes(30);

        return Cache::remember($cacheKey, $cacheTime, function () use ($entityId, $date) {
            $entity = AccountingEntity::findOrFail($entityId);
            $chartId = $entity->chartOfAccounts->id;

            $accounts = Account::withBalances($chartId, $date ? Carbon::parse($date) : null)
                ->get()
                ->map(function ($account) {
                    // Calculate balance based on account type
                    if (in_array($account->type, ['asset', 'expense'])) {
                        $balance = $account->debits - $account->credits;
                    } else {
                        $balance = $account->credits - $account->debits;
                    }

                    return [
                        'id' => $account->id,
                        'code' => $account->code,
                        'name' => $account->name,
                        'type' => $account->type,
                        'balance' => $balance,
                    ];
                });

            return [
                'date' => $date ?? now()->toDateString(),
                'entity' => $entity->name,
                'accounts' => $accounts,
                'total_debits' => $accounts->where('balance', '>', 0)->sum('balance'),
                'total_credits' => abs($accounts->where('balance', '<', 0)->sum('balance')),
            ];
        });
    }

    /**
     * Generate an income statement
     */
    public function generateIncomeStatement(int $entityId, string $startDate, string $endDate)
    {
        $cacheKey = "income_statement_{$entityId}_{$startDate}_{$endDate}";
        $cacheTime = now()->addMinutes(30);

        return Cache::remember($cacheKey, $cacheTime, function () use ($entityId, $startDate, $endDate) {
            $entity = AccountingEntity::findOrFail($entityId);
            $chartId = $entity->chartOfAccounts->id;

            $start = Carbon::parse($startDate);
            $end = Carbon::parse($endDate);

            $revenueAccounts = Account::where('chart_id', $chartId)
                ->where('type', 'revenue')
                ->get()
                ->map(function ($account) use ($start, $end) {
                    return [
                        'id' => $account->id,
                        'code' => $account->code,
                        'name' => $account->name,
                        'balance' => $account->balance($start, $end),
                    ];
                });

            $expenseAccounts = Account::where('chart_id', $chartId)
                ->where('type', 'expense')
                ->get()
                ->map(function ($account) use ($start, $end) {
                    return [
                        'id' => $account->id,
                        'code' => $account->code,
                        'name' => $account->name,
                        'balance' => $account->balance($start, $end),
                    ];
                });

            $totalRevenue = $revenueAccounts->sum('balance');
            $totalExpenses = $expenseAccounts->sum('balance');
            $netIncome = $totalRevenue - $totalExpenses;

            return [
                'start_date' => $startDate,
                'end_date' => $endDate,
                'entity' => $entity->name,
                'revenue_accounts' => $revenueAccounts,
                'expense_accounts' => $expenseAccounts,
                'total_revenue' => $totalRevenue,
                'total_expenses' => $totalExpenses,
                'net_income' => $netIncome,
            ];
        });
    }

    /**
     * Generate a balance sheet
     */
    public function generateBalanceSheet(int $entityId, ?string $date = null)
    {
        $cacheKey = "balance_sheet_{$entityId}_" . ($date ?? 'current');
        $cacheTime = now()->addMinutes(30);

        return Cache::remember($cacheKey, $cacheTime, function () use ($entityId, $date) {
            $entity = AccountingEntity::findOrFail($entityId);
            $chartId = $entity->chartOfAccounts->id;
            $balanceDate = $date ? Carbon::parse($date) : now();

            $assetAccounts = Account::where('chart_id', $chartId)
                ->where('type', 'asset')
                ->get()
                ->map(function ($account) use ($balanceDate) {
                    return [
                        'id' => $account->id,
                        'code' => $account->code,
                        'name' => $account->name,
                        'balance' => $account->balance(null, $balanceDate),
                    ];
                });

            $liabilityAccounts = Account::where('chart_id', $chartId)
                ->where('type', 'liability')
                ->get()
                ->map(function ($account) use ($balanceDate) {
                    return [
                        'id' => $account->id,
                        'code' => $account->code,
                        'name' => $account->name,
                        'balance' => $account->balance(null, $balanceDate),
                    ];
                });

            $equityAccounts = Account::where('chart_id', $chartId)
                ->where('type', 'equity')
                ->get()
                ->map(function ($account) use ($balanceDate) {
                    return [
                        'id' => $account->id,
                        'code' => $account->code,
                        'name' => $account->name,
                        'balance' => $account->balance(null, $balanceDate),
                    ];
                });

            $totalAssets = $assetAccounts->sum('balance');
            $totalLiabilities = $liabilityAccounts->sum('balance');
            $totalEquity = $equityAccounts->sum('balance');

            return [
                'date' => $date ?? now()->toDateString(),
                'entity' => $entity->name,
                'asset_accounts' => $assetAccounts,
                'liability_accounts' => $liabilityAccounts,
                'equity_accounts' => $equityAccounts,
                'total_assets' => $totalAssets,
                'total_liabilities' => $totalLiabilities,
                'total_equity' => $totalEquity,
                'is_balanced' => abs($totalAssets - ($totalLiabilities + $totalEquity)) < 0.0001,
            ];
        });
    }
}
```

## API Controllers

Create API controllers to expose accounting functionality:

```php
// app/Http/Controllers/Api/AccountingController.php
namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\AccountingService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class AccountingController extends Controller
{
    protected $accountingService;

    public function __construct(AccountingService $accountingService)
    {
        $this->accountingService = $accountingService;
    }

    /**
     * Post a new transaction
     */
    public function postTransaction(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'entity_id' => 'required|exists:accounting_entities,id',
            'reference' => 'required|string|max:50',
            'description' => 'required|string',
            'date' => 'required|date',
            'entries' => 'required|array|min:2',
            'entries.*.account_id' => 'required|exists:accounts,id',
            'entries.*.amount' => 'required|numeric|min:0.01',
            'entries.*.is_credit' => 'required|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $journal = $this->accountingService->createTransaction(
                $request->entity_id,
                $request->reference,
                $request->description,
                $request->date,
                $request->entries
            );

            return response()->json(['message' => 'Transaction posted successfully', 'journal' => $journal], 201);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 400);
        }
    }

    /**
     * Get trial balance
     */
    public function getTrialBalance(Request $request, int $entityId)
    {
        $date = $request->query('date');
        $trialBalance = $this->accountingService->generateTrialBalance($entityId, $date);

        return response()->json($trialBalance);
    }

    /**
     * Get income statement
     */
    public function getIncomeStatement(Request $request, int $entityId)
    {
        $validator = Validator::make($request->all(), [
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $incomeStatement = $this->accountingService->generateIncomeStatement(
            $entityId,
            $request->start_date,
            $request->end_date
        );

        return response()->json($incomeStatement);
    }

    /**
     * Get balance sheet
     */
    public function getBalanceSheet(Request $request, int $entityId)
    {
        $date = $request->query('date');
        $balanceSheet = $this->accountingService->generateBalanceSheet($entityId, $date);

        return response()->json($balanceSheet);
    }
}
```

## React Frontend with Optimistic UI

Implement the frontend using React with Optimistic UI for a responsive experience.

```jsx
// resources/js/Pages/Accounting/PostTransaction.jsx
import React, { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import axios from 'axios';

const postTransaction = async (data) => {
    const response = await axios.post('/api/accounting/transactions', data);
    return response.data;
};

export default function PostTransaction({ entityId, accounts }) {
    const queryClient = useQueryClient();
    const [formData, setFormData] = useState({
        entity_id: entityId,
        reference: '',
        description: '',
        date: '',
        entries: [{ account_id: '', amount: '', is_credit: false }, { account_id: '', amount: '', is_credit: true }],
    });

    const mutation = useMutation({
        mutationFn: postTransaction,
        onMutate: async (newTransaction) => {
            // Optimistic update (optional, depending on complexity)
            // This is more complex for double-entry, might skip for simplicity
        },
        onSuccess: () => {
            // Invalidate relevant queries to refetch data
            queryClient.invalidateQueries(['trialBalance', entityId]);
            queryClient.invalidateQueries(['balanceSheet', entityId]);
            // Optionally reset form
            setFormData({
                entity_id: entityId,
                reference: '',
                description: '',
                date: '',
                entries: [{ account_id: '', amount: '', is_credit: false }, { account_id: '', amount: '', is_credit: true }],
            });
            alert('Transaction posted successfully!');
        },
        onError: (error) => {
            alert('Error posting transaction: ' + (error.response?.data?.error || error.message));
        },
    });

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData({ ...formData, [name]: value });
    };

    const handleEntryChange = (index, e) => {
        const { name, value, type, checked } = e.target;
        const newEntries = [...formData.entries];
        newEntries[index][name] = type === 'checkbox' ? checked : value;
        setFormData({ ...formData, entries: newEntries });
    };

    const addEntry = () => {
        setFormData({
            ...formData,
            entries: [...formData.entries, { account_id: '', amount: '', is_credit: false }],
        });
    };

    const removeEntry = (index) => {
        const newEntries = [...formData.entries];
        newEntries.splice(index, 1);
        setFormData({ ...formData, entries: newEntries });
    };

    const handleSubmit = (e) => {
        e.preventDefault();
        mutation.mutate(formData);
    };

    return (
        <form onSubmit={handleSubmit}>
            <div>
                <label>Reference:</label>
                <input type="text" name="reference" value={formData.reference} onChange={handleInputChange} required />
            </div>
            <div>
                <label>Description:</label>
                <input type="text" name="description" value={formData.description} onChange={handleInputChange} required />
            </div>
            <div>
                <label>Date:</label>
                <input type="date" name="date" value={formData.date} onChange={handleInputChange} required />
            </div>

            <h3>Entries</h3>
            {formData.entries.map((entry, index) => (
                <div key={index}>
                    <select name="account_id" value={entry.account_id} onChange={(e) => handleEntryChange(index, e)} required>
                        <option value="">Select Account</option>
                        {accounts.map(account => (
                            <option key={account.id} value={account.id}>{account.name} ({account.code})</option>
                        ))}
                    </select>
                    <input type="number" name="amount" value={entry.amount} onChange={(e) => handleEntryChange(index, e)} step="0.01" required />
                    <label>
                        Credit:
                        <input type="checkbox" name="is_credit" checked={entry.is_credit} onChange={(e) => handleEntryChange(index, e)} />
                    </label>
                    {formData.entries.length > 2 && (
                        <button type="button" onClick={() => removeEntry(index)}>Remove</button>
                    )}
                </div>
            ))}
            <button type="button" onClick={addEntry}>Add Entry</button>

            <button type="submit" disabled={mutation.isLoading}>
                {mutation.isLoading ? 'Posting...' : 'Post Transaction'}
            </button>
        </form>
    );
}
```

```jsx
// resources/js/Pages/Accounting/TrialBalance.jsx
import React from 'react';
import { useQuery } from '@tanstack/react-query';
import axios from 'axios';

const fetchTrialBalance = async ({ queryKey }) => {
    const [_, entityId, date] = queryKey;
    const response = await axios.get(`/api/accounting/entities/${entityId}/trial-balance`, {
        params: { date }
    });
    return response.data;
};

export default function TrialBalance({ entityId }) {
    const [date, setDate] = React.useState(''); // State for date filter

    const { data, isLoading, isError, error } = useQuery({
        queryKey: ['trialBalance', entityId, date],
        queryFn: fetchTrialBalance,
        keepPreviousData: true, // Keep previous data while fetching new
    });

    if (isLoading) return <div>Loading Trial Balance...</div>;
    if (isError) return <div>Error loading Trial Balance: {error.message}</div>;

    return (
        <div>
            <h2>Trial Balance for {data.entity}</h2>
            <div>
                <label>As of Date:</label>
                <input type="date" value={date} onChange={(e) => setDate(e.target.value)} />
            </div>
            <table>
                <thead>
                    <tr>
                        <th>Code</th>
                        <th>Account Name</th>
                        <th>Type</th>
                        <th>Balance</th>
                    </tr>
                </thead>
                <tbody>
                    {data.accounts.map(account => (
                        <tr key={account.id}>
                            <td>{account.code}</td>
                            <td>{account.name}</td>
                            <td>{account.type}</td>
                            <td>{account.balance.toFixed(2)}</td>
                        </tr>
                    ))}
                </tbody>
                <tfoot>
                    <tr>
                        <td colSpan="3">Total Debits</td>
                        <td>{data.total_debits.toFixed(2)}</td>
                    </tr>
                    <tr>
                        <td colSpan="3">Total Credits</td>
                        <td>{data.total_credits.toFixed(2)}</td>
                    </tr>
                </tfoot>
            </table>
        </div>
    );
}
```

## MySQL Optimization Strategies

Refer to the dedicated MySQL Optimization guide for detailed techniques on database design, indexing, query optimization, server configuration, and scaling.

## Optimized Background Processing

For tasks like generating large reports or processing bulk transactions, use Laravel Queues and Workers to offload the work to the background, preventing timeouts and improving application responsiveness.

```php
// app/Jobs/GenerateFinancialReport.php
namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Services\AccountingService;
use App\Models\User; // Example: to notify user

class GenerateFinancialReport implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $reportType;
    protected $entityId;
    protected $params;
    protected $userId;

    /**
     * Create a new job instance.
     */
    public function __construct(string $reportType, int $entityId, array $params, int $userId)
    {
        $this->reportType = $reportType;
        $this->entityId = $entityId;
        $this->params = $params;
        $this->userId = $userId;
    }

    /**
     * Execute the job.
     */
    public function handle(AccountingService $accountingService): void
    {
        $reportData = [];
        switch ($this->reportType) {
            case 'trial_balance':
                $reportData = $accountingService->generateTrialBalance($this->entityId, $this->params['date'] ?? null);
                break;
            case 'income_statement':
                $reportData = $accountingService->generateIncomeStatement($this->entityId, $this->params['start_date'], $this->params['end_date']);
                break;
            case 'balance_sheet':
                $reportData = $accountingService->generateBalanceSheet($this->entityId, $this->params['date'] ?? null);
                break;
            // Add other report types
        }

        // Process or store the reportData
        // For example, save to a file or database, and notify the user

        $user = User::find($this->userId);
        if ($user) {
            // Notify user that report is ready
            // $user->notify(new ReportGeneratedNotification($this->reportType, $reportData));
        }
    }
}
```

```bash
# Dispatch the job
php artisan queue:work
```

## Deployment Considerations

*   **Caching**: Ensure caching is properly configured and utilized in production (`config:cache`, `route:cache`, `view:cache`).
*   **Database**: Optimize MySQL server configuration (`my.cnf`) for production workload. Consider using a managed database service.
*   **Queues**: Set up a robust queue worker process (e.g., Supervisor) to handle background jobs. Use a production-ready queue driver (Redis, Beanstalkd, SQS).
*   **Assets**: Ensure frontend assets are built and optimized for production (`npm run build`).
*   **Environment**: Configure production environment variables correctly (`.env`).

```bash
# Optimize Laravel for production
php artisan optimize

# Build frontend assets for production
npm run build

# Start queue worker (example using Supervisor)
# [program:laravel-worker]
# process_name=%(program_name)s_%(process_num)02d
# command=php /var/www/html/artisan queue:work redis --sleep=3 --tries=3 --max-time=3600
# autostart=true
# autorestart=true
# stopasgroup=true
# killafter=5000
# numprocs=8
# redirect_stderr=true
# stdout_logfile=/var/www/html/worker.log
# stopwaitsecs=3600
```

## Monitoring and Optimization

Continuously monitor application and database performance.

*   **Laravel Telescope**: Use in development/staging for insights.
*   **MySQL Slow Query Log**: Identify slow queries in production.
*   **APM Tools**: Integrate Application Performance Monitoring tools (e.g., New Relic, Sentry) for production monitoring.
*   **Database Monitoring**: Use tools like Prometheus/Grafana or cloud provider monitoring for database metrics.

## Conclusion

Implementing a financial double accounting system in Laravel 12 with React and optimized MySQL requires careful planning and attention to detail across the full stack. By leveraging Laravel's features, integrating frontend best practices, and applying robust database optimization techniques, you can build a performant and scalable application capable of handling complex financial data.

---

## Content from MySQL Optimization Techniques for Laravel Financial Applications

# MySQL Optimization Techniques for Laravel Financial Applications

This guide focuses specifically on MySQL optimization techniques for Laravel financial applications that handle large volumes of transaction data.

## Table of Contents
1. [Database Design Optimization](#database-design-optimization)
2. [Indexing Strategies](#indexing-strategies)
3. [Query Optimization](#query-optimization)
4. [MySQL Server Configuration](#mysql-server-configuration)
5. [Partitioning](#partitioning)
6. [Laravel-Specific Optimizations](#laravel-specific-optimizations)
7. [Monitoring and Profiling](#monitoring-and-profiling)
8. [Scaling Strategies](#scaling-strategies)

## Database Design Optimization

### Optimal Data Types

Choose the most efficient data types to minimize storage and improve performance:

```php
// Migration with optimized data types
Schema::create('transactions', function (Blueprint $table) {
    $table->id();
    // Use UNSIGNED for positive-only numbers (saves space)
    $table->unsignedBigInteger('user_id');
    // Use the smallest possible integer type that can hold your values
    $table->smallInteger('fiscal_year')->unsigned();
    // Use fixed-length when possible for financial data
    $table->decimal('amount', 13, 4); // 13 digits, 4 decimals
    // Use VARCHAR with realistic limits instead of TEXT for small strings
    $table->string('reference', 50);
    // Use INT for foreign keys instead of UUID/GUID for performance
    $table->foreignId('entity_id')->constrained();
    // Use TINYINT(1) for boolean flags
    $table->boolean('is_reconciled')->default(false);
    // Use DATE instead of DATETIME when time isn't needed
    $table->date('transaction_date');
    $table->timestamps();
});
```

### Normalization vs. Denormalization

For financial systems, proper normalization is crucial for data integrity but can affect query performance:

```php
// Normalized schema example
Schema::create('accounts', function (Blueprint $table) {
    $table->id();
    $table->foreignId('chart_id')->constrained();
    $table->string('code', 20);
    $table->string('name');
    $table->string('type', 20);
    $table->timestamps();
});

Schema::create('transactions', function (Blueprint $table) {
    $table->id();
    $table->string('reference', 50);
    $table->text('description');
    $table->date('transaction_date');
    $table->timestamps();
});

Schema::create('transaction_entries', function (Blueprint $table) {
    $table->id();
    $table->foreignId('transaction_id')->constrained()->onDelete('cascade');
    $table->foreignId('account_id')->constrained();
    $table->decimal('amount', 13, 4);
    $table->boolean('is_credit')->default(false);
    $table->timestamps();
});
```

### Denormalized Summary Tables

For frequently accessed summary data, consider denormalized summary tables:

```php
// Denormalized account balances table
Schema::create('account_balances', function (Blueprint $table) {
    $table->id();
    $table->foreignId('account_id')->constrained();
    $table->date('balance_date');
    $table->decimal('debit_total', 13, 4)->default(0);
    $table->decimal('credit_total', 13, 4)->default(0);
    $table->decimal('balance', 13, 4)->default(0);
    $table->timestamps();

    // Composite index for fast lookups
    $table->unique(['account_id', 'balance_date']);
});
```

## Indexing Strategies

### Strategic Column Indexing

Identify and index columns used in WHERE, JOIN, ORDER BY, and GROUP BY clauses:

```php
// Add indexes for commonly queried columns
Schema::table('transaction_entries', function (Blueprint $table) {
    // For filtering transactions by account
    $table->index('account_id');

    // For filtering by transaction type
    $table->index('is_credit');

    // Composite index for reports that filter by account and type
    $table->index(['account_id', 'is_credit']);
});

Schema::table('transactions', function (Blueprint $table) {
    // For date range queries
    $table->index('transaction_date');

    // For searching by reference number
    $table->index('reference');
});
```

### Composite Indexes

Create composite indexes for multi-column conditions, following the order of use:

```php
// Composite index for common financial reporting query
Schema::table('transaction_entries', function (Blueprint $table) {
    // For queries that filter by account and transaction date
    $table->index(['account_id', 'transaction_id']);
});

Schema::table('transactions', function (Blueprint $table) {
    // For filtering transactions by entity and date range
    $table->index(['entity_id', 'transaction_date']);
});
```

### Foreign Key Indexes

Always index foreign key columns to speed up joins:

```php
Schema::table('transaction_entries', function (Blueprint $table) {
    $table->foreign('transaction_id')
          ->references('id')
          ->on('transactions')
          ->onDelete('cascade');

    $table->foreign('account_id')
          ->references('id')
          ->on('accounts');

    // Ensure foreign keys are indexed
    $table->index('transaction_id');
    $table->index('account_id');
});
```

### Analyze Index Usage

Regularly analyze index usage to identify unused or inefficient indexes:

```sql
-- Find unused indexes
SELECT
    t.TABLE_SCHEMA,
    t.TABLE_NAME,
    ix.INDEX_NAME,
    ix.COLUMN_NAME
FROM
    information_schema.STATISTICS ix
LEFT JOIN
    mysql.schema_index_statistics s
    ON ix.TABLE_SCHEMA = s.table_schema
    AND ix.TABLE_NAME = s.table_name
    AND ix.INDEX_NAME = s.index_name
WHERE
    s.rows_read IS NULL OR s.rows_read = 0
    AND t.TABLE_SCHEMA = 'your_database';
```

## Query Optimization

### Efficient Eloquent Queries

Optimize your Laravel Eloquent queries for maximum performance:

```php
// BAD: Inefficient query with N+1 problem
$transactions = Transaction::where('entity_id', $entityId)
    ->whereBetween('transaction_date', [$startDate, $endDate])
    ->get();

foreach ($transactions as $transaction) {
    // This causes N additional queries
    $entries = $transaction->entries;
    foreach ($entries as $entry) {
        // This causes N*M additional queries
        $account = $entry->account;
    }
}

// GOOD: Efficient query with eager loading
$transactions = Transaction::with(['entries', 'entries.account'])
    ->where('entity_id', $entityId)
    ->whereBetween('transaction_date', [$startDate, $endDate])
    ->get();
```

### Select Only Required Columns

Specify only the columns you need instead of selecting everything:

```php
// BAD: Selecting all columns
$accounts = Account::all();

// GOOD: Select only what you need
$accounts = Account::select('id', 'code', 'name', 'type')->get();
```

### Raw Queries for Complex Operations

Use raw queries for complex aggregations and operations:

```php
// Using raw expressions for complex calculations
$accountBalances = DB::table('transaction_entries as e')
    ->join('transactions as t', 'e.transaction_id', '=', 't.id')
    ->select('e.account_id')
    ->selectRaw('SUM(CASE WHEN e.is_credit = 1 THEN e.amount ELSE 0 END) as credit_total')
    ->selectRaw('SUM(CASE WHEN e.is_credit = 0 THEN e.amount ELSE 0 END) as debit_total')
    ->whereRaw('t.transaction_date <= ?', [$balanceDate])
    ->groupBy('e.account_id')
    ->get();
```

### Chunking for Large Datasets

Process large data sets in chunks to avoid memory issues:

```php
// Process millions of transactions efficiently
Transaction::where('fiscal_year', 2023)
    ->chunkById(1000, function ($transactions) {
        foreach ($transactions as $transaction) {
            // Process each transaction without memory issues
            ProcessTransaction::dispatch($transaction);
        }
    });
```

### Query Caching

Cache expensive queries that don't change frequently:

```php
// Cache financial report queries
$monthEndReport = Cache::remember('month_end_report_' . $month, now()->addDays(1), function () use ($month) {
    return DB::table('transaction_entries as e')
        ->join('transactions as t', 'e.transaction_id', '=', 't.id')
        ->join('accounts as a', 'e.account_id', '=', 'a.id')
        ->select('a.id', 'a.name', 'a.type')
        ->selectRaw('SUM(CASE WHEN e.is_credit = 1 THEN e.amount ELSE 0 END) as credit_total')
        ->selectRaw('SUM(CASE WHEN e.is_credit = 0 THEN e.amount ELSE 0 END) as debit_total')
        ->whereRaw('t.transaction_date BETWEEN ? AND ?', [
            Carbon::parse($month)->startOfMonth(),
            Carbon::parse($month)->endOfMonth(),
        ])
        ->groupBy('a.id', 'a.name', 'a.type')
        ->get();
});
```

### View Materialization

Create materialized views for complex reporting queries:

```php
// Create a scheduled task to refresh the materialized view
$schedule->call(function () {
    DB::statement('TRUNCATE account_balances_materialized');

    DB::statement("
        INSERT INTO account_balances_materialized (account_id, debit_total, credit_total, balance, as_of_date)
        SELECT
            e.account_id,
            SUM(CASE WHEN e.is_credit = 0 THEN e.amount ELSE 0 END) as debit_total,
            SUM(CASE WHEN e.is_credit = 1 THEN e.amount ELSE 0 END) as credit_total,
            SUM(CASE WHEN e.is_credit = 0 THEN e.amount ELSE -e.amount END) as balance,
            CURRENT_DATE() as as_of_date
        FROM transaction_entries e
        JOIN transactions t ON e.transaction_id = t.id
        WHERE t.transaction_date <= CURRENT_DATE()
        GROUP BY e.account_id
    ");
})->daily();
```

## MySQL Server Configuration

### InnoDB Settings

Optimize InnoDB settings for financial data workloads:

```ini
# my.cnf optimized for financial data
[mysqld]
# InnoDB Buffer Pool (allocate 70-80% of available RAM)
innodb_buffer_pool_size = 4G

# For large transaction processing
innodb_log_file_size = 512M
innodb_log_buffer_size = 16M

# For write-heavy financial data
innodb_flush_log_at_trx_commit = 2

# Increase number of concurrent connections for high traffic
max_connections = 300

# Increase temp table size for complex joins and sorts
tmp_table_size = 64M
max_heap_table_size = 64M

# Disable query cache (better to use application-level caching)
query_cache_type = 0
query_cache_size = 0

# File-per-table for better management
innodb_file_per_table = 1

# For complex joins in financial reporting
join_buffer_size = 4M
sort_buffer_size = 4M

# Optimize IO for high-performance storage
innodb_flush_method = O_DIRECT

# Optimize for financial transaction processing
innodb_doublewrite = 1
innodb_thread_concurrency = 0
innodb_read_io_threads = 8
innodb_write_io_threads = 8
```

### Connection Pooling

Implement connection pooling for better resource utilization:

```php
// config/database.php
'mysql' => [
    'driver' => 'mysql',
    'host' => env('DB_HOST', '127.0.0.1'),
    'port' => env('DB_PORT', '3306'),
    'database' => env('DB_DATABASE', 'forge'),
    'username' => env('DB_USERNAME', 'forge'),
    'password' => env('DB_PASSWORD', ''),
    'unix_socket' => env('DB_SOCKET', ''),
    'charset' => 'utf8mb4',
    'collation' => 'utf8mb4_unicode_ci',
    'prefix' => '',
    'prefix_indexes' => true,
    'strict' => true,
    'engine' => null,
    'options' => extension_loaded('pdo_mysql') ? array_filter([
        PDO::MYSQL_ATTR_SSL_CA => env('MYSQL_ATTR_SSL_CA'),
    ]) : [],

    // Connection pooling for production
    'pool' => [
        'enabled' => env('DB_POOL', false),
        'min' => env('DB_POOL_MIN', 5),
        'max' => env('DB_POOL_MAX', 20),
    ],
],
```

## Partitioning

### Table Partitioning by Date

Partition large transaction tables by date for better performance:

```sql
-- Partition transactions table by date
ALTER TABLE transactions PARTITION BY RANGE (TO_DAYS(transaction_date)) (
    PARTITION p2022_q1 VALUES LESS THAN (TO_DAYS('2022-04-01')),
    PARTITION p2022_q2 VALUES LESS THAN (TO_DAYS('2022-07-01')),
    PARTITION p2022_q3 VALUES LESS THAN (TO_DAYS('2022-10-01')),
    PARTITION p2022_q4 VALUES LESS THAN (TO_DAYS('2023-01-01')),
    PARTITION p2023_q1 VALUES LESS THAN (TO_DAYS('2023-04-01')),
    PARTITION p2023_q2 VALUES LESS THAN (TO_DAYS('2023-07-01')),
    PARTITION p2023_q3 VALUES LESS THAN (TO_DAYS('2023-10-01')),
    PARTITION p2023_q4 VALUES LESS THAN (TO_DAYS('2024-01-01')),
    PARTITION p2024_q1 VALUES LESS THAN (TO_DAYS('2024-04-01')),
    PARTITION p2024_q2 VALUES LESS THAN (TO_DAYS('2024-07-01')),
    PARTITION p2024_q3 VALUES LESS THAN (TO_DAYS('2024-10-01')),
    PARTITION p2024_q4 VALUES LESS THAN (TO_DAYS('2025-01-01')),
    PARTITION future VALUES LESS THAN MAXVALUE
);
```

### Partitioning by Entity ID

For multi-tenant financial systems, consider partitioning by entity ID:

```sql
-- Partition by entity ID for multi-tenant systems
ALTER TABLE transaction_entries PARTITION BY HASH(account_id) PARTITIONS 8;
```

### Partition Maintenance

Implement partition maintenance for long-term performance:

```php
// Create a command to manage partitions
namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ManageTransactionPartitions extends Command
{
    protected $signature = 'db:manage-partitions';
    protected $description = 'Add new quarterly partitions for transactions table';

    public function handle()
    {
        // Get the latest quarter currently partitioned
        $result = DB::select("
            SELECT partition_description
            FROM information_schema.partitions
            WHERE table_name = 'transactions'
            AND partition_name != 'future'
            ORDER BY partition_ordinal_position DESC
            LIMIT 1
        ");

        if (empty($result)) {
            $this->error('Could not determine last partition');
            return 1;
        }

        $lastPartitionValue = $result[0]->partition_description;
        $lastDate = Carbon::createFromFormat('Y-m-d', substr($lastPartitionValue, 9, 10));

        // Create next 4 quarters if they don't exist
        $nextQuarter = $lastDate->copy()->addMonths(3);

        for ($i = 0; $i < 4; $i++) {
            $partitionDate = $nextQuarter->copy()->addMonths($i * 3);
            $partitionName = 'p' . $partitionDate->format('Y_q') . $partitionDate->quarter;
            $boundaryDate = $partitionDate->format('Y-m-d');

            $this->info("Adding partition {$partitionName} for boundary {$boundaryDate}");

            DB::statement("
                ALTER TABLE transactions REORGANIZE PARTITION future INTO (
                    PARTITION {$partitionName} VALUES LESS THAN (TO_DAYS('{$boundaryDate}')),
                    PARTITION future VALUES LESS THAN MAXVALUE
                )
            ");
        }

        $this->info('Partition management completed');
        return 0;
    }
}
```

## Laravel-Specific Optimizations

### Database Configuration

Optimize Laravel's database configuration:

```php
// config/database.php
'mysql' => [
    // ...
    'strict' => false, // Disable strict mode for better performance in some cases
    'options' => [
        PDO::ATTR_EMULATE_PREPARES => true, // Enable emulated prepared statements
        PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true, // Use buffered queries
    ],
],
```

### Query Builder Optimizations

Use optimized query builder methods:

```php
// Using when for conditional clauses
$query = Transaction::query();

if ($request->has('date_range')) {
    $query->whereBetween('transaction_date', [$request->start_date, $request->end_date]);
}

$transactions = $query->get();
```

### Eager Loading Strategy

Implement a consistent eager loading strategy:

```php
// Define relationships in models
class Transaction extends Model
{
    public function entries()
    {
        return $this->hasMany(TransactionEntry::class);
    }
}

class TransactionEntry extends Model
{
    public function account()
    {
        return $this->belongsTo(Account::class);
    }
}

// Eager load relationships
$transactions = Transaction::with('entries.account')->get();
```

### Laravel Data Caching

Cache computed data and report results:

```php
// Cache a complex report result
$reportData = Cache::remember('financial_summary', $minutes = 60, function () {
    return $this->generateFinancialSummary();
});
```

## Monitoring and Profiling

### Query Logging

Enable MySQL's slow query log:

```ini
# my.cnf
[mysqld]
slow_query_log = 1
slow_query_log_file = /var/log/mysql/mysql-slow.log
long_query_time = 1 # Log queries taking longer than 1 second
log_queries_not_using_indexes = 1 # Log queries that don't use indexes
```

### Database Profiling

Use MySQL's `EXPLAIN` statement:

```sql
EXPLAIN SELECT * FROM transactions WHERE transaction_date BETWEEN '2023-01-01' AND '2023-12-31';
```

### Laravel Telescope

Utilize Laravel Telescope:

```bash
# Install Telescope
composer require laravel/telescope --dev

# Publish assets
php artisan telescope:install

# Run migrations
php artisan migrate
```

## Scaling Strategies

### Read-Write Splitting

Implement read-write splitting:

```php
// config/database.php
'mysql' => [
    'read' => [
        'host' => [
            '***********',
            '***********',
        ],
    ],
    'write' => [
        'host' => [
            '***********',
        ],
    ],
    'sticky' => true,
    // ... other mysql config
],
```

### Sharding for Multi-Tenant

Consider sharding for very large multi-tenant applications.

### Archiving Old Data

Implement a strategy for archiving or purging old data.

## Conclusion

Optimizing MySQL performance in Laravel financial applications is an ongoing process that involves careful database design, strategic indexing, efficient query writing, server configuration tuning, and leveraging Laravel-specific features. By implementing the techniques outlined in this guide, you can build scalable and responsive financial applications capable of handling large volumes of data.
