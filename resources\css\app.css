@import 'tailwindcss';

@plugin 'tailwindcss-animate';

@source '../views';
@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';

@custom-variant dark (&:is(.dark *));

@theme {
    --font-sans:
        'Instrument Sans', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';

    --radius-lg: var(--radius);
    --radius-md: calc(var(--radius) - 2px);
    --radius-sm: calc(var(--radius) - 4px);

    --color-background: var(--background);
    --color-foreground: var(--foreground);

    --color-card: var(--card);
    --color-card-foreground: var(--card-foreground);

    --color-popover: var(--popover);
    --color-popover-foreground: var(--popover-foreground);

    --color-primary: var(--primary);
    --color-primary-foreground: var(--primary-foreground);

    --color-secondary: var(--secondary);
    --color-secondary-foreground: var(--secondary-foreground);

    --color-muted: var(--muted);
    --color-muted-foreground: var(--muted-foreground);

    --color-accent: var(--accent);
    --color-accent-foreground: var(--accent-foreground);

    --color-destructive: var(--destructive);
    --color-destructive-foreground: var(--destructive-foreground);

    --color-success: var(--success);
    --color-success-foreground: var(--success-foreground);
    --color-warning: var(--warning);
    --color-warning-foreground: var(--warning-foreground);
    --color-info: var(--info);
    --color-info-foreground: var(--info-foreground);
    --color-error: var(--error);
    --color-error-foreground: var(--error-foreground);

    --color-border: var(--border);
    --color-input: var(--input);
    --color-ring: var(--ring);

    --color-chart-1: var(--chart-1);
    --color-chart-2: var(--chart-2);
    --color-chart-3: var(--chart-3);
    --color-chart-4: var(--chart-4);
    --color-chart-5: var(--chart-5);

    --color-sidebar: var(--sidebar);
    --color-sidebar-foreground: var(--sidebar-foreground);
    --color-sidebar-primary: var(--sidebar-primary);
    --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
    --color-sidebar-accent: var(--sidebar-accent);
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-ring: var(--sidebar-ring);
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
    *,
    ::after,
    ::before,
    ::backdrop,
    ::file-selector-button {
        border-color: var(--color-gray-200, currentColor);
    }
}

:root {
    --background: oklch(1 0 0);
    --foreground: oklch(0.145 0 0);
    --card: oklch(1 0 0);
    --card-foreground: oklch(0.145 0 0);
    --popover: oklch(1 0 0);
    --popover-foreground: oklch(0.145 0 0);
    --primary: oklch(0.205 0 0);
    --primary-foreground: oklch(0.985 0 0);
    --secondary: oklch(0.97 0 0);
    --secondary-foreground: oklch(0.205 0 0);
    --muted: oklch(0.97 0 0);
    --muted-foreground: oklch(0.556 0 0);
    --accent: oklch(0.97 0 0);
    --accent-foreground: oklch(0.205 0 0);
    --destructive: oklch(0.577 0.245 27.325);
    --destructive-foreground: oklch(0.985 0 0);
    --border: oklch(0.922 0 0);
    --input: oklch(0.922 0 0);
    --ring: oklch(0.87 0 0);

    /* Semantic Colors for Toast and Components */
    --success: oklch(0.55 0.15 145);
    --success-foreground: oklch(0.985 0 0);
    --warning: oklch(0.65 0.15 85);
    --warning-foreground: oklch(0.145 0 0);
    --info: oklch(0.55 0.15 250);
    --info-foreground: oklch(0.985 0 0);
    --error: oklch(0.577 0.245 27.325);
    --error-foreground: oklch(0.985 0 0);

    /* Toast specific colors */
    --toast-success-bg: oklch(0.95 0.05 145);
    --toast-success-border: oklch(0.8 0.1 145);
    --toast-warning-bg: oklch(0.95 0.05 85);
    --toast-warning-border: oklch(0.8 0.1 85);
    --toast-info-bg: oklch(0.95 0.05 250);
    --toast-info-border: oklch(0.8 0.1 250);
    --toast-error-bg: oklch(0.95 0.05 27);
    --toast-error-border: oklch(0.8 0.1 27);

    --chart-1: oklch(0.646 0.222 41.116);
    --chart-2: oklch(0.6 0.118 184.704);
    --chart-3: oklch(0.398 0.07 227.392);
    --chart-4: oklch(0.828 0.189 84.429);
    --chart-5: oklch(0.769 0.188 70.08);
    --radius: 0.625rem;
    --sidebar: oklch(0.985 0 0);
    --sidebar-foreground: oklch(0.145 0 0);
    --sidebar-primary: oklch(0.205 0 0);
    --sidebar-primary-foreground: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.97 0 0);
    --sidebar-accent-foreground: oklch(0.205 0 0);
    --sidebar-border: oklch(0.922 0 0);
    --sidebar-ring: oklch(0.87 0 0);
}

.dark {
    --background: oklch(0.145 0 0);
    --foreground: oklch(0.985 0 0);
    --card: oklch(0.145 0 0);
    --card-foreground: oklch(0.985 0 0);
    --popover: oklch(0.145 0 0);
    --popover-foreground: oklch(0.985 0 0);
    --primary: oklch(0.985 0 0);
    --primary-foreground: oklch(0.205 0 0);
    --secondary: oklch(0.269 0 0);
    --secondary-foreground: oklch(0.985 0 0);
    --muted: oklch(0.269 0 0);
    --muted-foreground: oklch(0.708 0 0);
    --accent: oklch(0.269 0 0);
    --accent-foreground: oklch(0.985 0 0);
    --destructive: oklch(0.396 0.141 25.723);
    --destructive-foreground: oklch(0.985 0 0);
    --border: oklch(0.269 0 0);
    --input: oklch(0.269 0 0);
    --ring: oklch(0.439 0 0);

    /* Dark mode semantic colors */
    --success: oklch(0.45 0.12 145);
    --success-foreground: oklch(0.985 0 0);
    --warning: oklch(0.55 0.12 85);
    --warning-foreground: oklch(0.145 0 0);
    --info: oklch(0.45 0.12 250);
    --info-foreground: oklch(0.985 0 0);
    --error: oklch(0.396 0.141 25.723);
    --error-foreground: oklch(0.985 0 0);

    /* Dark mode toast colors */
    --toast-success-bg: oklch(0.2 0.08 145);
    --toast-success-border: oklch(0.35 0.1 145);
    --toast-warning-bg: oklch(0.25 0.08 85);
    --toast-warning-border: oklch(0.4 0.1 85);
    --toast-info-bg: oklch(0.2 0.08 250);
    --toast-info-border: oklch(0.35 0.1 250);
    --toast-error-bg: oklch(0.2 0.08 27);
    --toast-error-border: oklch(0.35 0.1 27);

    --chart-1: oklch(0.488 0.243 264.376);
    --chart-2: oklch(0.696 0.17 162.48);
    --chart-3: oklch(0.769 0.188 70.08);
    --chart-4: oklch(0.627 0.265 303.9);
    --chart-5: oklch(0.645 0.246 16.439);
    --sidebar: oklch(0.205 0 0);
    --sidebar-foreground: oklch(0.985 0 0);
    --sidebar-primary: oklch(0.985 0 0);
    --sidebar-primary-foreground: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.269 0 0);
    --sidebar-accent-foreground: oklch(0.985 0 0);
    --sidebar-border: oklch(0.269 0 0);
    --sidebar-ring: oklch(0.439 0 0);
}

@layer base {
    * {
        @apply border-border;
    }

    body {
        @apply bg-background text-foreground;
    }
}

/* Toast specific styling for better visual distinction */
@layer components {
    /* Override Sonner's default styling with important declarations */
    [data-sonner-toast] {
        @apply !shadow-lg !border !rounded-lg;
    }

    /* Success Toast Styling */
    [data-sonner-toast][data-type="success"] {
        @apply !bg-green-100 !text-green-800 !border-green-300;
    }

    /* Error Toast Styling */
    [data-sonner-toast][data-type="error"] {
        @apply !bg-red-100 !text-red-800 !border-red-300;
    }

    /* Warning Toast Styling */
    [data-sonner-toast][data-type="warning"] {
        @apply !bg-yellow-100 !text-yellow-800 !border-yellow-300;
    }

    /* Info Toast Styling */
    [data-sonner-toast][data-type="info"] {
        @apply !bg-blue-100 !text-blue-800 !border-blue-300;
    }

    /* Dark mode toast styling */
    .dark [data-sonner-toast][data-type="success"] {
        @apply !bg-green-900/80 !text-green-100 !border-green-700;
    }

    .dark [data-sonner-toast][data-type="error"] {
        @apply !bg-red-900/80 !text-red-100 !border-red-700;
    }

    .dark [data-sonner-toast][data-type="warning"] {
        @apply !bg-yellow-900/80 !text-yellow-100 !border-yellow-700;
    }

    .dark [data-sonner-toast][data-type="info"] {
        @apply !bg-blue-900/80 !text-blue-100 !border-blue-700;
    }

    /* Custom toast styling for our enhanced toasts */
    .toast-success {
        @apply !bg-green-100 !text-green-800 !border-green-300;
    }

    .toast-error {
        @apply !bg-red-100 !text-red-800 !border-red-300;
    }

    .toast-warning {
        @apply !bg-yellow-100 !text-yellow-800 !border-yellow-300;
    }

    .toast-info {
        @apply !bg-blue-100 !text-blue-800 !border-blue-300;
    }

    .dark .toast-success {
        @apply !bg-green-900/80 !text-green-100 !border-green-700;
    }

    .dark .toast-error {
        @apply !bg-red-900/80 !text-red-100 !border-red-700;
    }

    .dark .toast-warning {
        @apply !bg-yellow-900/80 !text-yellow-100 !border-yellow-700;
    }

    .dark .toast-info {
        @apply !bg-blue-900/80 !text-blue-100 !border-blue-700;
    }
}
