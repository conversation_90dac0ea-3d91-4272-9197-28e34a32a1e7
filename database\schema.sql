-- AccSystem SQL Schema
-- As the project grows, add all table structures and migrations here.

SET FOREIGN_KEY_CHECKS=0;

-- Users Table
CREATE TABLE IF NOT EXISTS `users` (
    `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(255) NOT NULL,
    `username` VARCHAR(100) NOT NULL,
    `email` VARCHAR(255) NOT NULL,
    `email_verified_at` TIMESTAMP NULL DEFAULT NULL,
    `password` VARCHAR(255) NOT NULL,
    `remember_token` VARCHAR(100) NULL DEFAULT NULL,
    `role` VARCHAR(50) NOT NULL DEFAULT 'user',
    `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY `users_username_unique` (`username`),
    UNIQUE KEY `users_email_unique` (`email`)
) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Account Types Table
CREATE TABLE IF NOT EXISTS `account_types` (
    `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(100) NOT NULL,
    `label` VARCHAR(100) NOT NULL,
    `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY `account_types_name_unique` (`name`)
) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Currencies Table
CREATE TABLE IF NOT EXISTS `currencies` (
    `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(100) NOT NULL,
    `code` VARCHAR(10) NOT NULL,
    `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY `currencies_code_unique` (`code`)
) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Accounts Table
CREATE TABLE IF NOT EXISTS `accounts` (
    `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `account_type_id` BIGINT UNSIGNED NOT NULL,
    `name` VARCHAR(255) NOT NULL,
    `user_id` BIGINT UNSIGNED NOT NULL,
    `contact` VARCHAR(100) NULL DEFAULT NULL,
    `email` VARCHAR(255) NULL DEFAULT NULL,
    `description` VARCHAR(255) NULL DEFAULT NULL,
    `ac_holder` VARCHAR(255) NULL DEFAULT NULL,
    `ac_number` VARCHAR(100) NULL DEFAULT NULL,
    `address` VARCHAR(255) NULL DEFAULT NULL,
    `salary` DECIMAL(15,2) NULL DEFAULT NULL,       -- Made nullable
    `joining_date` DATE NULL DEFAULT NULL,           -- Made nullable (already was)
    `currency_id` BIGINT UNSIGNED NULL DEFAULT NULL, -- Added via update migration
    `formula` CHAR(1) NULL DEFAULT NULL,             -- Added via update migration
    `code` VARCHAR(50) NULL DEFAULT NULL,
    `cnic` VARCHAR(30) NULL DEFAULT NULL,            -- Made nullable
    `father_name` VARCHAR(255) NULL DEFAULT NULL,    -- Added via update migration, nullable
    `created_at` TIMESTAMP NULL DEFAULT NULL,
    `updated_at` TIMESTAMP NULL DEFAULT NULL,
    KEY `accounts_name_index` (`name`), -- Index on name for searching
    CONSTRAINT `accounts_account_type_id_foreign` FOREIGN KEY (`account_type_id`) REFERENCES `account_types` (`id`) ON DELETE CASCADE,
    CONSTRAINT `accounts_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
    CONSTRAINT `accounts_currency_id_foreign` FOREIGN KEY (`currency_id`) REFERENCES `currencies` (`id`) ON DELETE SET NULL
) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- CHQ Ref Bank
CREATE TABLE `chq_ref_banks` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
  `name` VARCHAR(255) NOT NULL,
  `description` TEXT NULL,
  `user_id` BIGINT UNSIGNED NOT NULL,
  `created_at` TIMESTAMP NULL DEFAULT NULL,
  `updated_at` TIMESTAMP NULL DEFAULT NULL,
  CONSTRAINT `chq_ref_banks_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Todos Table
CREATE TABLE IF NOT EXISTS `todos` (
    `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `user_id` BIGINT UNSIGNED NOT NULL,
    `title` VARCHAR(255) NOT NULL,
    `completed` TINYINT(1) NOT NULL DEFAULT 0,
    `created_at` TIMESTAMP NULL DEFAULT NULL,
    `updated_at` TIMESTAMP NULL DEFAULT NULL,
    CONSTRAINT `todos_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Personal Access Tokens Table (Standard Laravel Sanctum)
CREATE TABLE IF NOT EXISTS `personal_access_tokens` (
    `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `tokenable_type` VARCHAR(255) NOT NULL,
    `tokenable_id` BIGINT UNSIGNED NOT NULL,
    `name` VARCHAR(255) NOT NULL,
    `token` VARCHAR(64) NOT NULL,
    `abilities` TEXT NULL DEFAULT NULL,
    `last_used_at` TIMESTAMP NULL DEFAULT NULL,
    `expires_at` TIMESTAMP NULL DEFAULT NULL,
    `created_at` TIMESTAMP NULL DEFAULT NULL,
    `updated_at` TIMESTAMP NULL DEFAULT NULL,
    UNIQUE KEY `personal_access_tokens_token_unique` (`token`),
    KEY `personal_access_tokens_tokenable_type_tokenable_id_index` (`tokenable_type`,`tokenable_id`)
) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Cache Table (Standard Laravel)
CREATE TABLE IF NOT EXISTS `cache` (
    `key` VARCHAR(255) NOT NULL PRIMARY KEY,
    `value` MEDIUMTEXT NOT NULL,
    `expiration` INT NOT NULL,
    INDEX `cache_key_index` (`key`)
) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS `cache_locks` (
    `key` VARCHAR(255) NOT NULL PRIMARY KEY,
    `owner` VARCHAR(255) NOT NULL,
    `expiration` INT NOT NULL
) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Jobs Table (Standard Laravel)
CREATE TABLE IF NOT EXISTS `jobs` (
    `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `queue` VARCHAR(255) NOT NULL,
    `payload` LONGTEXT NOT NULL,
    `attempts` TINYINT UNSIGNED NOT NULL,
    `reserved_at` INT UNSIGNED NULL DEFAULT NULL,
    `available_at` INT UNSIGNED NOT NULL,
    `created_at` INT UNSIGNED NOT NULL,
    INDEX `jobs_queue_index` (`queue`)
) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Password Reset Tokens Table
CREATE TABLE IF NOT EXISTS `password_reset_tokens` (
    `email` VARCHAR(255) NOT NULL PRIMARY KEY,
    `token` VARCHAR(255) NOT NULL,
    `created_at` TIMESTAMP NULL DEFAULT NULL
) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Sessions Table
CREATE TABLE IF NOT EXISTS `sessions` (
    `id` VARCHAR(255) NOT NULL PRIMARY KEY,
    `user_id` BIGINT UNSIGNED NULL,
    `ip_address` VARCHAR(45) NULL DEFAULT NULL,
    `user_agent` TEXT NULL DEFAULT NULL,
    `payload` LONGTEXT NOT NULL,
    `last_activity` INT NOT NULL,
    INDEX `sessions_user_id_index` (`user_id`)
) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Job Batches Table
CREATE TABLE IF NOT EXISTS `job_batches` (
    `id` VARCHAR(255) NOT NULL PRIMARY KEY,
    `name` VARCHAR(255) NOT NULL,
    `total_jobs` INT NOT NULL,
    `pending_jobs` INT NOT NULL,
    `failed_jobs` INT NOT NULL,
    `failed_job_ids` LONGTEXT NOT NULL,
    `options` MEDIUMTEXT NULL DEFAULT NULL,
    `cancelled_at` INT NULL DEFAULT NULL,
    `created_at` INT NOT NULL,
    `finished_at` INT NULL DEFAULT NULL
) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Failed Jobs Table
CREATE TABLE IF NOT EXISTS `failed_jobs` (
    `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `uuid` VARCHAR(255) NOT NULL UNIQUE,
    `connection` TEXT NOT NULL,
    `queue` TEXT NOT NULL,
    `payload` LONGTEXT NOT NULL,
    `exception` LONGTEXT NOT NULL,
    `failed_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Payment Types Table
CREATE TABLE IF NOT EXISTS `payment_types` (
    `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(100) NOT NULL,
    `label` VARCHAR(100) NOT NULL,
    `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY `payment_types_name_unique` (`name`)
) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Journal Entries Table
CREATE TABLE IF NOT EXISTS `journal_entries` (
    `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `TID` VARCHAR(50) NOT NULL,
    `date` DATE NOT NULL,
    `payment_type_id` BIGINT UNSIGNED NOT NULL,
    `account_id` BIGINT UNSIGNED NOT NULL,
    `is_credit` TINYINT(1) NOT NULL DEFAULT 0,
    `description` VARCHAR(255) NOT NULL,
    `chq_no` VARCHAR(100) NULL DEFAULT NULL,
    `inv_no` VARCHAR(100) NULL DEFAULT NULL,
    `amount` DECIMAL(18,2) NOT NULL,
    `currency_amount` DECIMAL(18,2) NULL DEFAULT NULL,
    `exchange_rate` DECIMAL(18,2) NULL DEFAULT NULL,
    `formula_type` VARCHAR(100) NULL DEFAULT NULL,
    `avg_rate` DECIMAL(18,2) NULL DEFAULT NULL,
    `running_currency_balance` DECIMAL(18,2) NULL DEFAULT NULL,
    `running_pkr_balance` DECIMAL(18,2) NULL DEFAULT NULL,
    `created_by` BIGINT UNSIGNED NULL DEFAULT NULL,
    `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    KEY `journal_entries_TID_index` (`TID`),
    KEY `journal_entries_date_index` (`date`),
    KEY `journal_entries_account_id_index` (`account_id`),
    CONSTRAINT `journal_entries_payment_type_id_foreign` FOREIGN KEY (`payment_type_id`) REFERENCES `payment_types` (`id`) ON DELETE CASCADE,
    CONSTRAINT `journal_entries_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE,
    CONSTRAINT `journal_entries_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


-- Cheque Entries Table
CREATE TABLE `cheque_entries` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `entry_type` enum('inward','outward') NOT NULL,
  `entry_date` date NOT NULL,
  `cheque_date` date NOT NULL,
  `posting_date` date NULL,
  `from_account_id` bigint unsigned NOT NULL,
  `to_account_id` bigint unsigned NOT NULL,
  `amount` decimal(15,2) NOT NULL,
  `chq_ref_bank_id` bigint unsigned NOT NULL,
  `chq_no` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` enum('pending','ok','returned') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `cheque_entries_should_be_unique` (`cheque_date`, `chq_ref_bank_id`, `chq_no`, `amount`),
  KEY `cheque_entries_TID_index` (`TID`),
  CONSTRAINT `cheque_entries_from_account_id_foreign` FOREIGN KEY (`from_account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `cheque_entries_to_account_id_foreign` FOREIGN KEY (`to_account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `cheque_entries_chq_ref_bank_id_foreign` FOREIGN KEY (`chq_ref_bank_id`) REFERENCES `chq_ref_banks` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


-- Exchange Entries Table
CREATE TABLE IF NOT EXISTS `exchange_entries` (
    `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `ps_number` VARCHAR(100) NOT NULL COMMENT 'Links to inv_no from journal_entries',
    `tid` VARCHAR(50) NOT NULL COMMENT 'Transaction ID for this exchange entry',
    `date` DATE NOT NULL,
    `account_id` BIGINT UNSIGNED NOT NULL COMMENT 'Customer Account ID',
    `currency_code` VARCHAR(10) NOT NULL,
    `amount` DECIMAL(18,2) NOT NULL COMMENT 'Currency amount',
    `payment_type_id` BIGINT UNSIGNED NOT NULL,
    `description` VARCHAR(255) NULL DEFAULT NULL,
    `created_by` BIGINT UNSIGNED NULL DEFAULT NULL,
    `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX `exchange_entries_ps_number_index` (`ps_number`),
    INDEX `exchange_entries_tid_index` (`tid`),
    CONSTRAINT `exchange_entries_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE,
    CONSTRAINT `exchange_entries_payment_type_id_foreign` FOREIGN KEY (`payment_type_id`) REFERENCES `payment_types` (`id`) ON DELETE CASCADE,
    CONSTRAINT `exchange_entries_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Recalculation Logs Table
CREATE TABLE IF NOT EXISTS `recalculation_logs` (
    `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `account_id` BIGINT UNSIGNED NOT NULL,
    `status` VARCHAR(50) DEFAULT 'pending', -- 'pending', 'processing', 'completed', 'failed'
    `started_at` TIMESTAMP NULL,
    `completed_at` DATETIME NULL,
    `from_date` DATE NULL, -- The date from which recalculation started
    `trigger_type` VARCHAR(50) NULL, -- 'auto', 'manual', 'scheduled'
    `description` TEXT NULL, -- Description of the recalculation
    `transaction_count` INT UNSIGNED DEFAULT 0, -- Number of transactions processed
    `queued` BOOLEAN DEFAULT FALSE, -- Whether the recalculation was processed via queue
    `job_id` VARCHAR(255) NULL, -- Queue job ID if applicable
    `error_message` TEXT NULL, -- Error message if failed
    `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX `status_index` (`status`),
    INDEX `account_completed_index` (`account_id`, `completed_at`),
    FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE
) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create the transaction_audit_logs table
CREATE TABLE `transaction_audit_logs` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `transaction_type` varchar(20) NOT NULL COMMENT 'purchase, sale',
    `transaction_id` varchar(50) NOT NULL COMMENT 'TID or other identifier',
    `inv_no` varchar(50) NOT NULL COMMENT 'P-NO or S-NO',
    `action` varchar(20) NOT NULL COMMENT 'created, updated, deleted',
    `old_values` json DEFAULT NULL COMMENT 'Previous values before change',
    `new_values` json DEFAULT NULL COMMENT 'New values after change',
    `changed_fields` json DEFAULT NULL COMMENT 'Array of changed field names',
    `user_id` bigint(20) unsigned DEFAULT NULL,
    `ip_address` varchar(45) DEFAULT NULL COMMENT 'IPv4 or IPv6 address',
    `user_agent` text DEFAULT NULL COMMENT 'Browser/client information',
    `notes` text DEFAULT NULL COMMENT 'Additional notes about the change',
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `idx_transaction_type_inv_no` (`transaction_type`, `inv_no`),
    KEY `idx_user_id_created_at` (`user_id`, `created_at`),
    KEY `idx_action_created_at` (`action`, `created_at`),
    KEY `idx_created_at` (`created_at`),
    CONSTRAINT `fk_transaction_audit_logs_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Audit trail for all transaction changes';


-- Add more tables as needed

SET FOREIGN_KEY_CHECKS=1;

-- Run Seeder by running this command:
-- php artisan db:seed
