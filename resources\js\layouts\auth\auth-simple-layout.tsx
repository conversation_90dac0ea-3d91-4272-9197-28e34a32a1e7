import AppLogoIcon from '@/components/app-logo-icon';
import { Link } from '@inertiajs/react';
import { ThemeToggle } from '@/components/ui/theme-toggle';
import { type PropsWithChildren } from 'react';

interface AuthLayoutProps {
    name?: string;
    title?: string;
    description?: string;
}

export default function AuthSimpleLayout({ children, title, description }: PropsWithChildren<AuthLayoutProps>) {
    return (
        <div className="bg-background flex  flex-col items-center justify-center gap-6 p-6 md:p-10">
            {/* Navbar/Header */}
            <header className="mb-8 w-full max-w-6xl">
                <nav className="flex items-center justify-between gap-4">
                    <Link href={route('home')} className="font-black text-xl tracking-tight flex items-center gap-2">
                        <span className="inline-block align-middle">
                            <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect width="32" height="32" rx="8" fill="#F53003" className="dark:fill-[#F8B803]" />
                                <text x="50%" y="57%" textAnchor="middle" fill="#fff" fontSize="15" fontWeight="bold" dy=".3em">AS</text>
                            </svg>
                        </span>
                        <span>AccSystem</span>
                    </Link>
                    <span className="flex items-center">
                        {/* Theme Toggle Button */}
                        <ThemeToggle />
                    </span>
                </nav>
            </header>
            <div className="w-full max-w-sm">
                <div className="flex flex-col gap-8">
                    {/* Existing app logo, title, description, etc. can remain below navbar */}
                    <div className="flex flex-col items-center gap-4">
                        <p className="sr-only">{title}</p>

                        <div className="space-y-2 text-center">
                            {title && (
                                <h1 className="text-2xl font-bold text-center">{title}</h1>
                            )}
                            {description && <p className="text-muted-foreground text-center">{description}</p>}
                        </div>
                    </div>
                    {children}
                </div>
            </div>
        </div>
    );
}
