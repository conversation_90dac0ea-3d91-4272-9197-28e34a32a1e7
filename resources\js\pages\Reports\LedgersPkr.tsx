import React, { useState, useEffect } from 'react';
import AppLayout from '@/layouts/app-layout';
import { Head, router } from '@inertiajs/react'; // Import router
import { CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from "@/components/ui/command";
import { CheckIcon } from "lucide-react";
import { DataTable } from "@/components/ui/data-table";
import { TableFooter, TableRow, TableCell, Table, TableHead } from "@/components/ui/table";
import axios from 'axios';
import { NumericFormat } from 'react-number-format';
import { BreadcrumbItem } from "@/types";
import { Input } from "@/components/ui/input";
import { TableHeader, TableBody } from "@/components/ui/table";

interface Account {
    id: number;
    name: string;
    description?: string;
    account_type?: { // Add account_type to the interface
        id: number;
        name: string;
    };
}

// Define a type for ledger entries
interface LedgerEntry {
    is_date_header?: boolean;
    date: string;
    TID?: string;
    id?: number;
    payment_type?: {
        id: number;
        name: string;
    };
    description?: string;
    inv_no?: string;
    chq_no?: string;
    amount?: number;
    is_credit?: boolean;
    balance?: number;
    absolute_balance?: number;
}

interface PendingCheques {
    inward: {
        count: number;
        total_amount: number;
        cheques: Array<{
            id: number;
            chq_no: string;
            amount: number;
            cheque_date: string;
            status: string;
        }>;
    };
    outward: {
        count: number;
        total_amount: number;
        cheques: Array<{
            id: number;
            chq_no: string;
            amount: number;
            cheque_date: string;
            status: string;
        }>;
    };
}

interface LedgerState {
    ledgerData: LedgerEntry[];
    pendingCheques?: PendingCheques;
    account?: {
        id: number;
        name: string;
    };
    totals?: {
        totalDr: number;
        totalCr: number;
        balance: number;
    };
}

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Ledgers PKR', href: '/ledgers-pkr' },
];

export default function LedgersPkr() {
    const [fromDate, setFromDate] = useState<Date>();
    const [tillDate, setTillDate] = useState<Date>();
    const [open, setOpen] = useState(false);
    const [selectedAccount, setSelectedAccount] = useState("");
    const [accounts, setAccounts] = useState<{ value: string; label: string }[]>([]);
    const [ledgerState, setLedgerState] = useState<LedgerState>({
        ledgerData: [],
        pendingCheques: undefined,
        account: undefined
    });
    const [showPdfDialog, setShowPdfDialog] = useState(false);
    const [showPrintDialog, setShowPrintDialog] = useState(false); // New state for print dialog
    const [searchTerm, setSearchTerm] = useState<string>("");

    useEffect(() => {
        // Fetch accounts list on component mount
        axios.get<Account[]>('/api/accounts')
            .then(response => {
                // Transform accounts data to match ComboBoxOption format
                setAccounts(
                    response.data.map((account: Account) => ({
                        value: account.id.toString(),
                        label: `(${account.id}) ${account.name}`,
                    })),
                );
            })
            .catch(error => console.error('Error fetching accounts:', error));
    }, []);

    // Function to process ledger data with date headers
    const processLedgerData = (rawData: any) => {
        if (!rawData || !rawData.ledgerData) {
            return [];
        }

        const processedData: LedgerEntry[] = [];
        const originalData = rawData.ledgerData.original;

        // Process entries by date, adding date headers before each day's entries
        Object.keys(originalData).sort().forEach(date => {
            // Add date header first
            processedData.push({
                is_date_header: true,
                date: date,
                // Add minimal required fields to make TypeScript happy
                TID: `header-${date}`,
                balance: 0
            });

            // Then add all entries for this date
            if (Array.isArray(originalData[date])) {
                originalData[date].forEach((entry: any) => {
                    processedData.push({
                        date: date,
                        TID: entry.TID,
                        id: entry.id,
                        payment_type: entry.payment_type,
                        description: entry.description,
                        inv_no: entry.inv_no,
                        chq_no: entry.chq_no,
                        amount: entry.amount,
                        is_credit: entry.is_credit,
                        balance: entry.balance
                    });
                });
            }
        });

        return processedData;
    };

    const handleViewClick = () => {
        if (!fromDate || !tillDate || !selectedAccount) {
            alert('Please select From Date, Till Date, and Account.');
            return;
        }

        // Fetch ledger data based on selected dates and account
        const params = new URLSearchParams({
            fromDate: format(fromDate, 'yyyy-MM-dd'),
            tillDate: format(tillDate, 'yyyy-MM-dd'),
            accountId: selectedAccount,
        });

        fetch(`/api/ledgers-pkr/data?${params.toString()}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.text().then(text => {
                    try {
                        return text ? JSON.parse(text) : null;
                    } catch {
                        throw new Error('Invalid JSON response');
                    }
                });
            })
            .then(data => {
                // Log the data structure for debugging
                console.log('Ledger data received:', data);

                if (!data || !data.ledgerData) {
                    throw new Error('No data returned');
                }
                // Use data directly from backend, which now includes running balance
                const processedData: LedgerEntry[] = processLedgerData(data);

                // Add totals calculation to the state
                const totals = calculateTotals();
                setLedgerState({
                    ledgerData: processedData,
                    pendingCheques: data.pendingCheques,
                    account: data.account,
                    totals: totals
                });
            })
            .catch(error => {
                console.error('Error fetching ledger data:', error);
                alert(`Failed to fetch ledger data: ${error.message}`);
                setLedgerState({
                    ledgerData: [],
                    pendingCheques: undefined,
                    account: undefined
                });
            });
    };

    const handleGeneratePdf = (withPartyName: boolean) => {
        if (!fromDate || !tillDate || !selectedAccount) {
            alert('Please select From Date, Till Date, and Account.');
            return;
        }

        const params = new URLSearchParams({
            fromDate: format(fromDate, 'yyyy-MM-dd'),
            tillDate: format(tillDate, 'yyyy-MM-dd'),
            accountId: selectedAccount,
            withPartyName: String(withPartyName), // Convert boolean to string for URL param
        });

        // Use web.php route for direct PDF download/stream
        window.open(`/ledgers-pkr/pdf?${params.toString()}`, '_blank');
        setShowPdfDialog(false);
    };

    const handlePrint = (withPartyName: boolean) => {
        if (!fromDate || !tillDate || !selectedAccount) {
            alert('Please select From Date, Till Date, and Account.');
            return;
        }

        const params = new URLSearchParams({
            fromDate: format(fromDate, 'yyyy-MM-dd'),
            tillDate: format(tillDate, 'yyyy-MM-dd'),
            accountId: selectedAccount,
            withPartyName: String(withPartyName),
        });

        // Open a new window for the print view
        const printWindow = window.open(`/ledgers-pkr/print?${params.toString()}`, '_blank');
        
        // Optional: focus the new window
        if (printWindow) {
            printWindow.focus();
            // The printing itself will be triggered by a script in the loaded page
        } else {
            alert('Failed to open print window. Please check your pop-up blocker settings.');
        }
        setShowPrintDialog(false);
    };

    const calculateTotals = () => {
        // Calculate Debit (positive amounts)
        const totalDr = ledgerState.ledgerData
            .filter(entry => !entry.is_date_header && entry.amount && entry.amount > 0)
            .reduce((sum, entry) => sum + (Number(entry.amount) || 0), 0);
        
        // Calculate Credit (negative amounts as positive)
        const totalCr = Math.abs(ledgerState.ledgerData
            .filter(entry => !entry.is_date_header && entry.amount && entry.amount < 0)
            .reduce((sum, entry) => sum + (Number(entry.amount) || 0), 0));
        
        // Get the last entry's balance for the running balance
        const lastEntry = [...ledgerState.ledgerData]
            .filter(entry => !entry.is_date_header && typeof entry.balance === 'number')
            .pop();
        
        const balance = lastEntry?.balance || 0;

        // Get pending cheques
        const pendingCheques = ledgerState.pendingCheques || { inward: { total_amount: 0, count: 0 }, outward: { total_amount: 0, count: 0 } };
        
        // Get cheque totals
        const chequeInwardTotal = -(pendingCheques.inward?.total_amount || 0); // Make negative for inward
        const chequeOutwardTotal = pendingCheques.outward?.total_amount || 0; // Keep positive for outward
        
        // Calculate final total (balance + pending cheques)
        const finalTotal = balance + chequeInwardTotal + chequeOutwardTotal;
        
        return {
            totalDr,
            totalCr,
            balance,
            chequeInwardTotal,
            chequeOutwardTotal,
            finalTotal,
            chequeInwardCount: pendingCheques.inward?.count || 0,
            chequeOutwardCount: pendingCheques.outward?.count || 0
        };
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Ledgers PKR" />
            <div className="container mx-auto px-6 py-6">
                <h2 className="mb-4 text-2xl font-bold">Ledger Details (PKR)</h2>
                <div className="flex flex-wrap items-center gap-4 mb-4 md:flex-nowrap">
                    {/* Date Pickers Group */}
                    <div className="flex flex-wrap items-center gap-4">
                        {/* From Date DatePicker */}
                        <Popover>
                            <PopoverTrigger asChild>
                                <Button
                                    variant={"outline"}
                                    className={cn(
                                        "w-[240px] justify-start text-left font-normal",
                                        !fromDate && "text-muted-foreground"
                                    )}
                                >
                                    <CalendarIcon className="mr-2 h-4 w-4" />
                                    {fromDate ? format(fromDate, "PPP") : <span>From Date</span>}
                                </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0">
                                <Calendar
                                    mode="single"
                                    selected={fromDate}
                                    onSelect={setFromDate}
                                    initialFocus
                                />
                            </PopoverContent>
                        </Popover>

                        {/* Till Date DatePicker */}
                        <Popover>
                            <PopoverTrigger asChild>
                                <Button
                                    variant={"outline"}
                                    className={cn(
                                        "w-[240px] justify-start text-left font-normal",
                                        !tillDate && "text-muted-foreground"
                                    )}
                                >
                                    <CalendarIcon className="mr-2 h-4 w-4" />
                                    {tillDate ? format(tillDate, "PPP") : <span>Till Date</span>}
                                </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0">
                                <Calendar
                                    mode="single"
                                    selected={tillDate}
                                    onSelect={setTillDate}
                                    initialFocus
                                />
                            </PopoverContent>
                        </Popover>
                    </div>

                    {/* Buttons Group */}
                    <div className="flex flex-wrap items-center gap-4">
                        <Button onClick={handleViewClick}>View</Button>
                        <Button variant="secondary">Excel</Button> {/* Excel button - functionality TBD */}
                        <Button variant="secondary" onClick={() => setShowPdfDialog(true)}>PDF</Button>
                        <Button variant="secondary" onClick={() => setShowPrintDialog(true)}>Print</Button> {/* Updated Print Button */}
                    </div>
                </div>

                {/* PDF Generation Dialog */}
                <Dialog open={showPdfDialog} onOpenChange={setShowPdfDialog}>
                    <DialogContent className="sm:max-w-[425px]">
                        <DialogHeader>
                            <DialogTitle>Generate PDF Report</DialogTitle>
                            <DialogDescription>
                                Select an option for your PDF report.
                            </DialogDescription>
                        </DialogHeader>
                        <div className="grid gap-4 py-4">
                            <Button onClick={() => handleGeneratePdf(true)}>With Party Name</Button>
                            <Button onClick={() => handleGeneratePdf(false)} variant="outline">Without Party Name</Button>
                        </div>
                        <DialogFooter>
                            <Button variant="ghost" onClick={() => setShowPdfDialog(false)}>Cancel</Button>
                        </DialogFooter>
                    </DialogContent>
                </Dialog>

                {/* Print Dialog */}
                <Dialog open={showPrintDialog} onOpenChange={setShowPrintDialog}>
                    <DialogContent className="sm:max-w-[425px]">
                        <DialogHeader>
                            <DialogTitle>Print Ledger Report</DialogTitle>
                            <DialogDescription>
                                Select an option for your printout.
                            </DialogDescription>
                        </DialogHeader>
                        <div className="grid gap-4 py-4">
                            <Button onClick={() => handlePrint(true)}>With Party Name</Button>
                            <Button onClick={() => handlePrint(false)} variant="outline">Without Party Name</Button>
                        </div>
                        <DialogFooter>
                            <Button variant="ghost" onClick={() => setShowPrintDialog(false)}>Cancel</Button>
                        </DialogFooter>
                    </DialogContent>
                </Dialog>

                <div className="flex items-center space-x-4 mb-6">
                    {/* Account ComboBox */}
                    <Popover open={open} onOpenChange={setOpen}>
                        <PopoverTrigger asChild>
                            <Button
                                variant="outline"
                                role="combobox"
                                aria-expanded={open}
                                className="w-[200px] justify-between"
                            >
                                {selectedAccount
                                    ? accounts.find((account) => account.value === selectedAccount)?.label
                                    : "Select account..."}
                                <CheckIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                            </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-[200px] p-0">
                            <Command>
                                <CommandInput placeholder="Search account..." />
                                <CommandEmpty>No account found.</CommandEmpty>
                                <CommandGroup>
                                    {accounts.map((account) => (
                                        <CommandItem
                                            key={account.value}
                                            value={account.label}
                                            onSelect={() => {
                                                setSelectedAccount(account.value === selectedAccount ? "" : account.value);
                                                setOpen(false);
                                            }}
                                        >
                                            {account.label}
                                            <CheckIcon
                                                className={cn(
                                                    "ml-auto h-4 w-4",
                                                    selectedAccount === account.value ? "opacity-100" : "opacity-0"
                                                )}
                                            />
                                        </CommandItem>
                                    ))}
                                </CommandGroup>
                            </Command>
                        </PopoverContent>
                    </Popover>
                </div>

                {/* Custom Ledger Table that properly renders date headers */}
                <div>
                    <div className="flex items-center py-4">
                        <Input
                            placeholder="Search..."
                            value={searchTerm || ""}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="max-w-sm"
                        />
                    </div>
                    <div className="rounded-md border">
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead className="whitespace-nowrap">TID</TableHead>
                                    <TableHead className="whitespace-nowrap">PaymentType</TableHead>
                                    <TableHead className="whitespace-nowrap">Naration</TableHead>
                                    <TableHead className="whitespace-nowrap">Inv#</TableHead>
                                    <TableHead className="whitespace-nowrap">ChqNo</TableHead>
                                    <TableHead className="whitespace-nowrap">Debit</TableHead>
                                    <TableHead className="whitespace-nowrap">Credit</TableHead>
                                    <TableHead className="whitespace-nowrap">Balance</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {ledgerState.ledgerData.length ? (
                                    // Group entries by date for display
                                    (() => {
                                        // Sort entries by date
                                        const sortedEntries = [...ledgerState.ledgerData].sort((a, b) => 
                                            new Date(a.date).getTime() - new Date(b.date).getTime()
                                        );
                                        
                                        // Group by date
                                        const entriesByDate: Record<string, LedgerEntry[]> = {};
                                        sortedEntries.forEach(entry => {
                                            if (!entry.is_date_header) {
                                                const dateKey = entry.date.split('T')[0];
                                                if (!entriesByDate[dateKey]) {
                                                    entriesByDate[dateKey] = [];
                                                }
                                                entriesByDate[dateKey].push(entry);
                                            }
                                        });
                                        
                                        // Render groups with date headers
                                        return Object.entries(entriesByDate).map(([date, entries]) => {
                                            // Apply search filter
                                            const filteredEntries = searchTerm 
                                                ? entries.filter(entry => 
                                                    entry.TID?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                                                    entry.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                                                    entry.payment_type?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                                                    entry.inv_no?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                                                    entry.chq_no?.toLowerCase().includes(searchTerm.toLowerCase())
                                                ) 
                                                : entries;
                                            
                                            // Only render date group if it has entries after filtering
                                            if (filteredEntries.length === 0) return null;
                                            
                                            return (
                                                <React.Fragment key={`date-group-${date}`}>
                                                    {/* Date Header */}
                                                    <TableRow className="bg-gray-100 dark:bg-accent font-bold">
                                                        <TableCell colSpan={8} className="py-2">
                                                            {new Date(date).toLocaleDateString('en-US', { 
                                                                weekday: 'long',
                                                                year: 'numeric', 
                                                                month: 'short', 
                                                                day: 'numeric'
                                                            })}
                                                        </TableCell>
                                                    </TableRow>
                                                    
                                                    {/* Entries for this date */}
                                                    {filteredEntries.map(entry => (
                                                        <TableRow key={`entry-${entry.TID}-${entry.id}`}>
                                                            <TableCell className="whitespace-nowrap">
                                                                {entry.TID}
                                                            </TableCell>
                                                            <TableCell className="whitespace-nowrap">
                                                                {entry.payment_type ? (
                                                                    <a
                                                                        href={`/journal-entry/edit/${entry.id}`}
                                                                        onClick={(e) => {
                                                                            if (e.ctrlKey || e.metaKey || e.button === 1) {
                                                                                return;
                                                                            }
                                                                            e.preventDefault();
                                                                            router.visit(`/journal-entry/edit/${entry.id}`);
                                                                        }}
                                                                        className="text-green-600 hover:underline"
                                                                    >
                                                                        {entry.payment_type?.name || ''}
                                                                    </a>
                                                                ) : null}
                                                            </TableCell>
                                                            <TableCell className="whitespace-nowrap">
                                                                {entry.description || ''}
                                                            </TableCell>
                                                            <TableCell className="whitespace-nowrap">
                                                                <span className={entry.inv_no ? 'text-green-800' : ''}>
                                                                    {entry.inv_no || ''}
                                                                </span>
                                                            </TableCell>
                                                            <TableCell className="whitespace-nowrap">
                                                                <span className={entry.chq_no && !isNaN(Number(entry.chq_no)) ? "text-blue-600" : ""}>
                                                                    {entry.chq_no || ''}
                                                                </span>
                                                            </TableCell>
                                                            <TableCell className="whitespace-nowrap text-right">
                                                                <NumericFormat 
                                                                    value={entry.amount && !entry.is_credit ? Math.abs(entry.amount) : 0} 
                                                                    displayType={'text'} 
                                                                    thousandSeparator={true} 
                                                                />
                                                            </TableCell>
                                                            <TableCell className="whitespace-nowrap text-right">
                                                                <NumericFormat 
                                                                    value={entry.amount && entry.is_credit ? Math.abs(entry.amount) : 0} 
                                                                    displayType={'text'} 
                                                                    thousandSeparator={true} 
                                                                />
                                                            </TableCell>
                                                            <TableCell className="whitespace-nowrap text-right">
                                                                {typeof entry.balance === 'number' ? (
                                                                    <span className={entry.balance > 0 ? 'text-red-600 text-right' : 'text-right'}>
                                                                        <NumericFormat 
                                                                            value={Math.abs(entry.balance)} 
                                                                            displayType={'text'} 
                                                                            thousandSeparator={true} 
                                                                        />
                                                                        {' '}{entry.balance > 0 ? 'Dr' : 'Cr'}
                                                                    </span>
                                                                ) : null}
                                                            </TableCell>
                                                        </TableRow>
                                                    ))}
                                                </React.Fragment>
                                            );
                                        });
                                    })()
                                ) : (
                                    <TableRow>
                                        <TableCell colSpan={8} className="h-24 text-center">
                                            No results.
                                        </TableCell>
                                    </TableRow>
                                )}
                            </TableBody>
                            <TableFooter>
                                <TableRow>
                                    <TableCell className="font-bold">Total Dr:</TableCell>
                                    <TableCell colSpan={4} 
                                    className={calculateTotals().totalDr > 0 ? 'text-red-600 font-bold' : ''}
                                    >
                                        <NumericFormat 
                                            value={calculateTotals().totalDr} 
                                            displayType={'text'} 
                                            thousandSeparator={true} 
                                        />
                                        {' Dr'}
                                    </TableCell>
                                    <TableCell colSpan={2} className="font-bold">Balance:</TableCell>
                                    <TableCell
                                    className={calculateTotals().balance >= 0 ? 'text-red-600 font-bold text-right' : 'font-bold text-right'}
                                    >
                                        <NumericFormat 
                                            value={Math.abs(calculateTotals().balance)} 
                                            displayType={'text'} 
                                            thousandSeparator={true} 
                                        />
                                        {' '}{calculateTotals().balance >= 0 ? 'Dr' : 'Cr'}
                                    </TableCell>
                                </TableRow>
                                <TableRow>
                                    <TableCell className="font-bold">Total Cr:</TableCell>
                                    <TableCell colSpan={4} className="font-bold text-left">
                                        <NumericFormat 
                                            value={calculateTotals().totalCr} 
                                            displayType={'text'} 
                                            thousandSeparator={true} 
                                        />
                                        {' Cr'}
                                    </TableCell>
                                    <TableCell colSpan={2} className="font-bold">CHQ INWARD:</TableCell>
                                    <TableCell 
                                        className={calculateTotals().chequeInwardTotal > 0 ? 'text-red-600 font-bold text-right' : 'font-bold text-right'}
                                    >
                                        <NumericFormat 
                                            value={Math.abs(calculateTotals().chequeInwardTotal)} 
                                            displayType={'text'} 
                                            thousandSeparator={true} 
                                        />
                                        {calculateTotals().chequeInwardTotal < 0 ? ' Cr' : 'Dr'}
                                    </TableCell>
                                </TableRow>
                                <TableRow>
                                    <TableCell className="font-bold">CHQ(IW):</TableCell>
                                    <TableCell colSpan={4} className="font-bold text-left">
                                        {calculateTotals().chequeInwardCount}
                                    </TableCell>
                                    <TableCell colSpan={2} className="font-bold">CHQ OUTWARD:</TableCell>
                                    <TableCell
                                    className={calculateTotals().chequeOutwardTotal > 0 ? 'text-red-600 font-bold text-right' : 'font-bold text-right'}>
                                        <NumericFormat 
                                            value={calculateTotals().chequeOutwardTotal} 
                                            displayType={'text'} 
                                            thousandSeparator={true} 
                                        />
                                        {calculateTotals().chequeOutwardTotal > 0 ? ' Dr' : 'Cr'}
                                    </TableCell>
                                </TableRow>
                                <TableRow>
                                    <TableCell className="font-bold">CHQ(OW):</TableCell>
                                    <TableCell colSpan={4} className="font-bold text-left">
                                        {calculateTotals().chequeOutwardCount}
                                    </TableCell>
                                    <TableCell colSpan={2} className="font-bold">Total:</TableCell>
                                    <TableCell 
                                    className={calculateTotals().finalTotal > 0 ? 'text-red-600 font-bold text-right' : 'font-bold text-right'}>
                                        <NumericFormat 
                                            value={Math.abs(calculateTotals().finalTotal)} 
                                            displayType={'text'} 
                                            thousandSeparator={true} 
                                        />
                                        {' '}{calculateTotals().finalTotal >= 0 ? 'Dr' : 'Cr'}
                                    </TableCell>
                                </TableRow>
                            </TableFooter>
                        </Table>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
